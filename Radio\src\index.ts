import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

console.log("🚀 Radio Browser API Server başlatılıyor...");

// Radio Browser API fonksiyonları
async function searchRadioStations(query: string, limit: number = 20) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/search?name=${encodeURIComponent(query)}&limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Radio istasyonu arama hatası:", error);
    return [];
  }
}

async function getTopStations(limit: number = 50) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/topvote?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Top istasyonları alma hatası:", error);
    return [];
  }
}

async function getStationsByCountry(country: string, limit: number = 30) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/bycountry/${encodeURIComponent(country)}?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Ülkeye göre istasyon alma hatası:", error);
    return [];
  }
}

async function getStationsByGenre(genre: string, limit: number = 30) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/bytag/${encodeURIComponent(genre)}?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Türe göre istasyon alma hatası:", error);
    return [];
  }
}

// API ENDPOINTS

// 1. Ana sayfa - API bilgisi
app.get('/', (req, res) => {
  res.json({
    message: "📻 Radio Browser API Server",
    version: "1.0.0",
    endpoints: {
      "/api/stations/top": "En popüler istasyonlar",
      "/api/stations/search/:query": "İstasyon arama",
      "/api/stations/country/:country": "Ülkeye göre istasyonlar",
      "/api/stations/genre/:genre": "Türe göre istasyonlar"
    }
  });
});

// 2. En popüler istasyonlar
app.get('/api/stations/top', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const stations = await getTopStations(limit);

    res.json({
      success: true,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Sunucu hatası"
    });
  }
});

// 3. İstasyon arama
app.get('/api/stations/search/:query', async (req, res) => {
  try {
    const query = req.params.query;
    const limit = parseInt(req.query.limit as string) || 20;
    const stations = await searchRadioStations(query, limit);

    res.json({
      success: true,
      query: query,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Arama hatası"
    });
  }
});

// 4. Ülkeye göre istasyonlar
app.get('/api/stations/country/:country', async (req, res) => {
  try {
    const country = req.params.country;
    const limit = parseInt(req.query.limit as string) || 30;
    const stations = await getStationsByCountry(country, limit);

    res.json({
      success: true,
      country: country,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Ülke bazlı arama hatası"
    });
  }
});

// 5. Türe göre istasyonlar
app.get('/api/stations/genre/:genre', async (req, res) => {
  try {
    const genre = req.params.genre;
    const limit = parseInt(req.query.limit as string) || 30;
    const stations = await getStationsByGenre(genre, limit);

    res.json({
      success: true,
      genre: genre,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Tür bazlı arama hatası"
    });
  }
});

// Server başlat
app.listen(PORT, () => {
  console.log(`✅ API Server çalışıyor: http://localhost:${PORT}`);
  console.log(`📱 Mobil uygulama için endpoint'ler hazır!`);
  console.log(`🔗 Ana sayfa: http://localhost:${PORT}`);
});
