import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

console.log("🚀 Radio Browser API Server başlatılıyor...");

// Radio Browser API fonksiyonları
async function searchRadioStations(query: string, limit: number = 20) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/search?name=${encodeURIComponent(query)}&limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Radio istasyonu arama hatası:", error);
    return [];
  }
}

async function getTopStations(limit: number = 50) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/topvote?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Top istasyonları alma hatası:", error);
    return [];
  }
}

async function getStationsByCountry(country: string, limit: number = 30) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/bycountry/${encodeURIComponent(country)}?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Ülkeye göre istasyon alma hatası:", error);
    return [];
  }
}

async function getStationsByGenre(genre: string, limit: number = 30) {
  try {
    const response = await fetch(`https://de1.api.radio-browser.info/json/stations/bytag/${encodeURIComponent(genre)}?limit=${limit}`);
    const stations = await response.json();
    return stations;
  } catch (error) {
    console.error("Türe göre istasyon alma hatası:", error);
    return [];
  }
}

// API ENDPOINTS

// 1. Ana sayfa - API bilgisi
app.get('/', (req, res) => {
  res.json({
    message: "📻 Radio Browser API Server",
    version: "1.0.0",
    getEndpoints: {
      "/api/stations/top": "En popüler istasyonlar",
      "/api/stations/search/:query": "İstasyon arama",
      "/api/stations/country/:country": "Ülkeye göre istasyonlar",
      "/api/stations/genre/:genre": "Türe göre istasyonlar"
    },
    postEndpoints: {
      "/api/stations/favorite": "İstasyon favorilere ekleme",
      "/api/stations/unfavorite": "İstasyon favorilerden çıkarma",
      "/api/stations/listen": "İstasyon dinleme kaydı",
      "/api/stations/vote": "İstasyon oylama",
      "/api/user/profile": "Kullanıcı profil güncelleme",
      "/api/stations/advanced-search": "Gelişmiş arama",
      "/api/user/favorites": "Kullanıcı favorileri getirme"
    }
  });
});

// 2. En popüler istasyonlar - MOBİL UYGULAMAYA HAZIR
app.get('/api/stations/top', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const stations = await getTopStations(limit);

    res.json({
      success: true,
      message: "Top istasyonlar başarıyla getirildi",
      timestamp: new Date().toISOString(),
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name || "Bilinmeyen İstasyon",
        streamUrl: station.url_resolved || station.url,
        homepage: station.homepage || "",
        favicon: station.favicon || "https://via.placeholder.com/64x64?text=📻",
        country: station.country || "Bilinmeyen",
        countryCode: station.countrycode || "",
        language: station.language || "Bilinmeyen",
        votes: station.votes || 0,
        codec: station.codec || "MP3",
        bitrate: station.bitrate || 128,
        tags: station.tags || "",
        genre: station.tags ? station.tags.split(',')[0] : "Genel",
        isWorking: station.lastcheckok === 1,
        lastChecked: station.lastchecktime || ""
      }))
    });
  } catch (error) {
    console.error("Top stations error:", error);
    res.status(500).json({
      success: false,
      error: "Sunucu hatası",
      message: "İstasyonlar yüklenirken hata oluştu"
    });
  }
});

// 3. İstasyon arama - MOBİL UYGULAMAYA HAZIR
app.get('/api/stations/search/:query', async (req, res) => {
  try {
    const query = req.params.query;
    const limit = parseInt(req.query.limit as string) || 15;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: "Arama terimi en az 2 karakter olmalı",
        message: "Lütfen geçerli bir arama terimi girin"
      });
    }

    const stations = await searchRadioStations(query.trim(), limit);

    res.json({
      success: true,
      message: `"${query}" için ${stations.length} sonuç bulundu`,
      query: query,
      timestamp: new Date().toISOString(),
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name || "Bilinmeyen İstasyon",
        streamUrl: station.url_resolved || station.url,
        homepage: station.homepage || "",
        favicon: station.favicon || "https://via.placeholder.com/64x64?text=📻",
        country: station.country || "Bilinmeyen",
        countryCode: station.countrycode || "",
        language: station.language || "Bilinmeyen",
        votes: station.votes || 0,
        codec: station.codec || "MP3",
        bitrate: station.bitrate || 128,
        tags: station.tags || "",
        genre: station.tags ? station.tags.split(',')[0] : "Genel",
        isWorking: station.lastcheckok === 1,
        lastChecked: station.lastchecktime || "",
        clickCount: station.clickcount || 0
      }))
    });
  } catch (error) {
    console.error("Search error:", error);
    res.status(500).json({
      success: false,
      error: "Arama hatası",
      message: "Arama yapılırken hata oluştu"
    });
  }
});

// 4. Ülkeye göre istasyonlar
app.get('/api/stations/country/:country', async (req, res) => {
  try {
    const country = req.params.country;
    const limit = parseInt(req.query.limit as string) || 30;
    const stations = await getStationsByCountry(country, limit);

    res.json({
      success: true,
      country: country,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Ülke bazlı arama hatası"
    });
  }
});

// 5. Türe göre istasyonlar
app.get('/api/stations/genre/:genre', async (req, res) => {
  try {
    const genre = req.params.genre;
    const limit = parseInt(req.query.limit as string) || 30;
    const stations = await getStationsByGenre(genre, limit);

    res.json({
      success: true,
      genre: genre,
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Tür bazlı arama hatası"
    });
  }
});

// POST ENDPOINTS

// 6. İstasyon favorilere ekleme
app.post('/api/stations/favorite', async (req, res) => {
  try {
    const { stationId, userId, stationName } = req.body;

    if (!stationId || !userId) {
      return res.status(400).json({
        success: false,
        error: "stationId ve userId gerekli"
      });
    }

    // Burada normalde veritabanına kaydedersiniz
    // Şimdilik mock response döndürüyoruz
    res.json({
      success: true,
      message: "İstasyon favorilere eklendi",
      data: {
        stationId,
        userId,
        stationName,
        addedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Favori ekleme hatası"
    });
  }
});

// 7. İstasyon favorilerden çıkarma
app.post('/api/stations/unfavorite', async (req, res) => {
  try {
    const { stationId, userId } = req.body;

    if (!stationId || !userId) {
      return res.status(400).json({
        success: false,
        error: "stationId ve userId gerekli"
      });
    }

    res.json({
      success: true,
      message: "İstasyon favorilerden çıkarıldı",
      data: {
        stationId,
        userId,
        removedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Favori çıkarma hatası"
    });
  }
});

// 8. İstasyon dinleme kaydı
app.post('/api/stations/listen', async (req, res) => {
  try {
    const { stationId, userId, duration } = req.body;

    if (!stationId || !userId) {
      return res.status(400).json({
        success: false,
        error: "stationId ve userId gerekli"
      });
    }

    res.json({
      success: true,
      message: "Dinleme kaydedildi",
      data: {
        stationId,
        userId,
        duration: duration || 0,
        listenedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Dinleme kaydetme hatası"
    });
  }
});

// 9. İstasyon oylama
app.post('/api/stations/vote', async (req, res) => {
  try {
    const { stationId, userId, rating } = req.body;

    if (!stationId || !userId || !rating) {
      return res.status(400).json({
        success: false,
        error: "stationId, userId ve rating gerekli"
      });
    }

    if (rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        error: "Rating 1-5 arasında olmalı"
      });
    }

    res.json({
      success: true,
      message: "Oy kaydedildi",
      data: {
        stationId,
        userId,
        rating,
        votedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Oylama hatası"
    });
  }
});

// 10. Kullanıcı profil güncelleme
app.post('/api/user/profile', async (req, res) => {
  try {
    const { userId, name, email, preferences } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: "userId gerekli"
      });
    }

    res.json({
      success: true,
      message: "Profil güncellendi",
      data: {
        userId,
        name,
        email,
        preferences,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Profil güncelleme hatası"
    });
  }
});

// 11. Gelişmiş arama (POST ile filtreler)
app.post('/api/stations/advanced-search', async (req, res) => {
  try {
    const { query, country, genre, language, minBitrate, limit } = req.body;

    // Gelişmiş arama parametreleri ile API çağrısı
    let searchUrl = 'https://de1.api.radio-browser.info/json/stations/search?';

    if (query) searchUrl += `name=${encodeURIComponent(query)}&`;
    if (country) searchUrl += `country=${encodeURIComponent(country)}&`;
    if (genre) searchUrl += `tag=${encodeURIComponent(genre)}&`;
    if (language) searchUrl += `language=${encodeURIComponent(language)}&`;
    if (minBitrate) searchUrl += `bitrateMin=${minBitrate}&`;

    searchUrl += `limit=${limit || 20}`;

    const response = await fetch(searchUrl);
    const stations = await response.json();

    res.json({
      success: true,
      searchParams: { query, country, genre, language, minBitrate },
      count: stations.length,
      data: stations.map((station: any) => ({
        id: station.stationuuid,
        name: station.name,
        url: station.url,
        homepage: station.homepage,
        favicon: station.favicon,
        country: station.country,
        language: station.language,
        votes: station.votes,
        codec: station.codec,
        bitrate: station.bitrate,
        tags: station.tags
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Gelişmiş arama hatası"
    });
  }
});

// 12. Kullanıcı favorileri getirme (POST ile userId)
app.post('/api/user/favorites', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: "userId gerekli"
      });
    }

    // Mock favori istasyonlar (normalde veritabanından gelir)
    const mockFavorites = [
      {
        id: "78012206-1aa1-11e9-a80b-52543be04c81",
        name: "MANGORADIO",
        url: "https://mangoradio.stream.laut.fm/mangoradio",
        country: "Germany",
        addedAt: "2025-01-15T10:30:00Z"
      },
      {
        id: "12345678-1aa1-11e9-a80b-52543be04c82",
        name: "BBC Radio 1",
        url: "http://stream.live.vc.bbcmedia.co.uk/bbc_radio_one",
        country: "United Kingdom",
        addedAt: "2025-01-14T15:20:00Z"
      }
    ];

    res.json({
      success: true,
      userId,
      count: mockFavorites.length,
      data: mockFavorites
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Favoriler getirme hatası"
    });
  }
});

// Server başlat
app.listen(PORT, () => {
  console.log(`✅ API Server çalışıyor: http://localhost:${PORT}`);
  console.log(`📱 Mobil uygulama için endpoint'ler hazır!`);
  console.log(`🔗 Ana sayfa: http://localhost:${PORT}`);
  console.log(`📋 GET Endpoints: 5 adet`);
  console.log(`📋 POST Endpoints: 7 adet`);
});
