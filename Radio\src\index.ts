import { openai } from '@ai-sdk/openai';
import { VoltAgent, Agent, MCPConfiguration } from "@voltagent/core";
import { VercelAIProvider } from "@voltagent/vercel-ai";

const mcpConfig = new MCPConfiguration({
  servers: {
    semantic_scholar: {
      command: "npx",
      type: "stdio",
      args: [
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@openags/radio-browser-mcp",
        "--key",
        process.env.SMITHERY_API_KEY || ""
      ],
    }
  },
});

console.log(await mcpConfig.getTools());

const agent = new Agent({
  name: "MCP Example Agent",
  description: "You help users to find information.",
  llm: new VercelAIProvider(),
  model: openai("gpt-3.5-turbo"),
  tools: await mcpConfig.getTools(),
});

new VoltAgent({
  agents: {
    agent,
  },
});
