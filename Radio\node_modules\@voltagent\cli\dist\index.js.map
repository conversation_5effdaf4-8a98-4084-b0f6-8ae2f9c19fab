{"version": 3, "sources": ["../src/index.ts", "../src/commands/init.ts", "../src/utils/analytics.ts", "../src/commands/update.ts", "../src/commands/whoami.ts", "../src/commands/add.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from \"commander\";\nimport chalk from \"chalk\";\nimport figlet from \"figlet\";\nimport { registerInitCommand } from \"./commands/init\";\nimport { registerUpdateCommand } from \"./commands/update\";\nimport { registerWhoamiCommand } from \"./commands/whoami\";\nimport { registerAddCommand } from \"./commands/add\";\nimport { captureError } from \"./utils/analytics\";\nimport posthogClient from \"./utils/analytics\";\n\nconst createCLI = () => {\n  // Create Commander program\n  const program = new Command();\n\n  // Set CLI information\n  program\n    .name(\"voltagent\")\n    .description(\"VoltAgent CLI - CLI tool for update checks\")\n    .version(\"0.1.0\");\n\n  // Register commands\n  registerInitCommand(program);\n  registerUpdateCommand(program);\n  registerWhoamiCommand(program);\n  registerAddCommand(program);\n\n  return program;\n};\n\nconst runCLI = async () => {\n  try {\n    // Welcome message\n    console.log(\n      chalk.cyan(\n        figlet.textSync(\"VoltAgent CLI\", {\n          font: \"Standard\",\n          horizontalLayout: \"default\",\n          verticalLayout: \"default\",\n        }),\n      ),\n    );\n\n    // Run CLI\n    const program = createCLI();\n    await program.parseAsync(process.argv);\n\n    // Ensure PostHog events are sent before exiting\n    await posthogClient.shutdown();\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error);\n    console.error(chalk.red(\"An unexpected error occurred:\"));\n    console.error(errorMessage);\n\n    // Track unexpected error\n    captureError({\n      command: \"unknown\",\n      errorMessage,\n    });\n\n    // Ensure PostHog events are sent before exiting with error\n    await posthogClient.shutdown();\n    process.exit(1);\n  }\n};\n\n// Run\nrunCLI().catch((error) => {\n  const errorMessage = error instanceof Error ? error.message : String(error);\n  console.error(`Error: ${errorMessage}`);\n\n  // Track error in the CLI runner\n  captureError({\n    command: \"cli_runner\",\n    errorMessage,\n  });\n\n  // Shutdown PostHog client before exiting\n  posthogClient.shutdown().then(() => {\n    process.exit(1);\n  });\n});\n\nexport { runCLI };\n", "import { Command } from \"commander\";\nimport chalk from \"chalk\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { execSync } from \"child_process\";\nimport { captureInitEvent, captureError } from \"../utils/analytics\";\n\nexport const registerInitCommand = (program: Command): void => {\n  program\n    .command(\"init\")\n    .description(\"Integrate VoltAgent CLI into a project\")\n    .action(async () => {\n      try {\n        console.log(chalk.cyan(\"Integrating VoltAgent CLI into your project...\"));\n\n        // Check for package.json file\n        const packageJsonPath = path.join(process.cwd(), \"package.json\");\n        if (!fs.existsSync(packageJsonPath)) {\n          console.error(\n            chalk.red(\n              \"Error: package.json file not found. This command must be run inside a Node.js project.\",\n            ),\n          );\n          process.exit(1);\n        }\n\n        // Read package.json file\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, \"utf-8\"));\n\n        // Get existing scripts\n        const scripts = packageJson.scripts || {};\n\n        // Detect package manager\n        let packageManager = \"npm\";\n        try {\n          // Check if pnpm is being used\n          if (fs.existsSync(path.join(process.cwd(), \"pnpm-lock.yaml\"))) {\n            packageManager = \"pnpm\";\n          } else if (fs.existsSync(path.join(process.cwd(), \"yarn.lock\"))) {\n            packageManager = \"yarn\";\n          }\n        } catch (error) {\n          // Default to npm if detection fails\n          packageManager = \"npm\";\n        }\n\n        console.log(chalk.blue(`Detected package manager: ${packageManager}`));\n\n        // Add \"volt\" script to package.json\n        let modified = false;\n        if (!scripts[\"volt\"] || scripts[\"volt\"] !== \"volt\") {\n          scripts[\"volt\"] = \"volt\";\n          modified = true;\n        }\n\n        if (!modified) {\n          console.log(chalk.yellow(\"No changes made. The 'volt' script is already configured.\"));\n        } else {\n          // Update package.json file\n          packageJson.scripts = scripts;\n          fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));\n          console.log(chalk.green(\"✓ Added 'volt' script to package.json\"));\n        }\n\n        // Check if @voltagent/cli is installed locally and install if needed\n        let isPackageInstalled = false;\n        try {\n          // Check if the package exists in node_modules\n          fs.accessSync(\n            path.join(process.cwd(), \"node_modules\", \"@voltagent\", \"cli\"),\n            fs.constants.F_OK,\n          );\n          isPackageInstalled = true;\n        } catch (error) {\n          // Package is not installed\n          isPackageInstalled = false;\n        }\n\n        if (!isPackageInstalled) {\n          console.log(chalk.cyan(\"Installing @voltagent/cli locally...\"));\n          try {\n            const installCommand =\n              packageManager === \"yarn\"\n                ? \"yarn add @voltagent/cli --dev\"\n                : packageManager === \"pnpm\"\n                  ? \"pnpm add @voltagent/cli --save-dev\"\n                  : \"npm install @voltagent/cli --save-dev\";\n\n            execSync(installCommand, { stdio: \"inherit\" });\n            console.log(chalk.green(\"✓ @voltagent/cli successfully installed!\"));\n          } catch (error) {\n            console.error(\n              chalk.red(\"Failed to install @voltagent/cli:\"),\n              error instanceof Error ? error.message : String(error),\n            );\n          }\n        }\n\n        // Create .voltagent directory\n        const voltagentDir = path.join(process.cwd(), \".voltagent\");\n        if (!fs.existsSync(voltagentDir)) {\n          fs.mkdirSync(voltagentDir);\n        }\n\n        console.log(chalk.green(\"✓ VoltAgent CLI successfully integrated!\"));\n\n        console.log(\"\\n\", chalk.cyan(\"To run VoltAgent:\"), chalk.green(\"npm run volt\"));\n\n        // Track init event\n        captureInitEvent({\n          packageManager,\n        });\n      } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        console.error(chalk.red(\"Error:\"), errorMessage);\n\n        // Track error event\n        captureError({\n          command: \"init\",\n          errorMessage,\n        });\n\n        process.exit(1);\n      }\n    });\n};\n", "import { PostHog } from \"posthog-node\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport os from \"os\";\nimport crypto from \"crypto\";\n\n// Check if telemetry is disabled via environment variable\nconst isTelemetryDisabled = (): boolean => {\n  return (\n    process.env.VOLTAGENT_TELEMETRY_DISABLED === \"1\" ||\n    process.env.VOLTAGENT_TELEMETRY_DISABLED === \"true\"\n  );\n};\n\n// Initialize PostHog client\nconst client = new PostHog(\"phc_cLPjGbbZ9BdRtLG3cxoHNch3ZJnQvNhXCHRkeWUI6z5\", {\n  host: \"https://us.i.posthog.com\",\n  flushAt: 1,\n  flushInterval: 0,\n  disableGeoip: false,\n});\n\n// Generate a machine-specific but anonymous ID\nconst getMachineId = (): string => {\n  try {\n    // Create a hash from stable machine properties\n    const hostname = os.hostname();\n    const cpus = os.cpus().length;\n    const platform = os.platform();\n    const arch = os.arch();\n\n    const dataToHash = `${hostname}-${cpus}-${platform}-${arch}`;\n    return crypto.createHash(\"sha256\").update(dataToHash).digest(\"hex\").substring(0, 32);\n  } catch (error) {\n    // Fallback to a random UUID if machine info isn't accessible\n    return uuidv4();\n  }\n};\n\n// Get OS info for analytics with fallback\nconst getOSInfo = () => {\n  try {\n    return {\n      os_platform: os.platform(),\n      os_release: os.release(),\n      os_version: os.version(),\n      os_arch: os.arch(),\n    };\n  } catch (error) {\n    // Fallback to minimal info if OS info isn't accessible due to security restrictions\n    return {\n      os_platform: \"unknown\",\n      os_release: \"unknown\",\n      os_version: \"unknown\",\n      os_arch: \"unknown\",\n    };\n  }\n};\n\n// Function to capture CLI initialization events\nexport const captureInitEvent = (options: {\n  packageManager: string;\n}) => {\n  // Skip if telemetry is disabled\n  if (isTelemetryDisabled()) return;\n\n  client.capture({\n    distinctId: getMachineId(),\n    event: \"cli_init\",\n    properties: {\n      package_manager: options.packageManager,\n      machine_id: getMachineId(),\n      ...getOSInfo(),\n    },\n  });\n};\n\n// Function to capture CLI update check events\nexport const captureUpdateEvent = (options: {\n  hadUpdates: boolean;\n}) => {\n  // Skip if telemetry is disabled\n  if (isTelemetryDisabled()) return;\n\n  client.capture({\n    distinctId: getMachineId(),\n    event: \"cli_update_check\",\n    properties: {\n      had_updates: options.hadUpdates,\n      machine_id: getMachineId(),\n      ...getOSInfo(),\n    },\n  });\n};\n\n// Function to capture error events\nexport const captureError = (options: {\n  command: string;\n  errorMessage: string;\n}) => {\n  // Skip if telemetry is disabled\n  if (isTelemetryDisabled()) return;\n\n  client.capture({\n    distinctId: getMachineId(),\n    event: \"cli_error\",\n    properties: {\n      command: options.command,\n      error_message: options.errorMessage,\n      machine_id: getMachineId(),\n      ...getOSInfo(),\n    },\n  });\n};\n\n// Function to capture whoami command events\nexport const captureWhoamiEvent = (options: {\n  numVoltPackages: number;\n}) => {\n  // Skip if telemetry is disabled\n  if (isTelemetryDisabled()) return;\n\n  client.capture({\n    distinctId: getMachineId(),\n    event: \"cli_whoami\",\n    properties: {\n      num_volt_packages: options.numVoltPackages,\n      machine_id: getMachineId(),\n      ...getOSInfo(),\n    },\n  });\n};\n\nexport default client;\n", "import { Command } from \"commander\";\nimport chalk from \"chalk\";\nimport ora from \"ora\";\nimport path from \"path\";\nimport fs from \"fs\";\nimport * as ncuPackage from \"npm-check-updates\";\nimport inquirer from \"inquirer\";\nimport { captureUpdateEvent, captureError } from \"../utils/analytics\";\n\n// Not directly importing from @voltagent/core due to potential circular dependencies\n// instead, we'll implement a simpler version here\ntype UpdateResult = {\n  hasUpdates: boolean;\n  updates: Record<string, string>;\n  count: number;\n  message: string;\n};\n\n/**\n * Simple version of checkForUpdates that uses npm-check-updates API\n */\nconst checkForUpdates = async (\n  packagePath?: string,\n  options?: { filter?: string },\n): Promise<UpdateResult> => {\n  try {\n    // Find package.json path\n    const rootDir = packagePath ? path.dirname(packagePath) : process.cwd();\n    const packageJsonPath = packagePath || path.join(rootDir, \"package.json\");\n\n    // Check if package.json exists\n    if (!fs.existsSync(packageJsonPath)) {\n      return {\n        hasUpdates: false,\n        updates: {},\n        count: 0,\n        message: \"Could not find package.json\",\n      };\n    }\n\n    // Use ncu API instead of CLI\n    const result = await ncuPackage.default({\n      packageFile: packageJsonPath,\n      silent: true,\n      jsonUpgraded: true,\n      filter: options?.filter,\n    });\n\n    // Process results\n    const updates = result as Record<string, string>;\n    const count = Object.keys(updates).length;\n\n    if (count > 0) {\n      const updatesList = Object.entries(updates)\n        .map(([name, version]) => `  - ${name} → ${version}`)\n        .join(\"\\n\");\n\n      return {\n        hasUpdates: true,\n        updates,\n        count,\n        message: `Found ${count} outdated packages:\\n${updatesList}`,\n      };\n    } else {\n      return {\n        hasUpdates: false,\n        updates: {},\n        count: 0,\n        message: \"All packages are up to date\",\n      };\n    }\n  } catch (error) {\n    console.error(\"Error checking for updates:\", error);\n    return {\n      hasUpdates: false,\n      updates: {},\n      count: 0,\n      message: `Error checking for updates: ${error instanceof Error ? error.message : String(error)}`,\n    };\n  }\n};\n\n/**\n * Custom interactive updater using inquirer\n */\nconst interactiveUpdate = async (\n  updates: Record<string, string>,\n  packagePath?: string,\n): Promise<void> => {\n  // Get package.json\n  const rootDir = packagePath ? path.dirname(packagePath) : process.cwd();\n  const packageJsonPath = packagePath || path.join(rootDir, \"package.json\");\n\n  // Prepare choices for inquirer\n  const choices = Object.entries(updates).map(([name, version]) => {\n    return {\n      name: `${chalk.cyan(name)}: ${chalk.gray(getCurrentVersion(name, packageJsonPath))} → ${chalk.green(version)}`,\n      value: name,\n      short: name,\n    };\n  });\n\n  // Ask user which packages to update\n  const { selectedPackages } = await inquirer.prompt([\n    {\n      type: \"checkbox\",\n      name: \"selectedPackages\",\n      message: \"Select packages to update:\",\n      choices: choices,\n      pageSize: 15,\n      default: choices.map((c) => c.value), // Default select all\n    },\n  ]);\n\n  if (selectedPackages.length === 0) {\n    console.log(chalk.yellow(\"No packages selected for update.\"));\n    return;\n  }\n\n  // Create filter for selected packages only\n  const selectedFilter = selectedPackages.join(\" \");\n\n  console.log(chalk.cyan(\"\\nApplying updates for selected packages...\"));\n\n  try {\n    // Use ncu API to apply updates for selected packages\n    await ncuPackage.default({\n      packageFile: packageJsonPath,\n      upgrade: true,\n      filter: selectedFilter,\n    });\n\n    console.log(chalk.green(`✓ Updated ${selectedPackages.length} packages in package.json`));\n    console.log(chalk.green(\"Run 'npm install' to install updated packages\"));\n  } catch (error) {\n    console.error(chalk.red(\"Error applying updates:\"));\n    console.error(error instanceof Error ? error.message : String(error));\n  }\n};\n\n/**\n * Get current version of a package from package.json\n */\nconst getCurrentVersion = (packageName: string, packageJsonPath: string): string => {\n  try {\n    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, \"utf8\"));\n\n    // Check in different dependency sections\n    for (const section of [\n      \"dependencies\",\n      \"devDependencies\",\n      \"peerDependencies\",\n      \"optionalDependencies\",\n    ]) {\n      if (packageJson[section]?.[packageName]) {\n        return packageJson[section][packageName];\n      }\n    }\n\n    return \"unknown\";\n  } catch (error) {\n    return \"unknown\";\n  }\n};\n\n/**\n * Register the update command to the CLI program\n */\nexport const registerUpdateCommand = (program: Command): void => {\n  program\n    .command(\"update\")\n    .description(\"Interactive update for VoltAgent packages\")\n    .option(\"--apply\", \"Apply updates without interactive mode\")\n    .action(async (options) => {\n      try {\n        // Initialize spinner\n        const spinner = ora(\"Checking for updates...\").start();\n\n        // Check for updates using our utility\n        const filter = \"@voltagent*\";\n        const updates = await checkForUpdates(undefined, { filter });\n\n        spinner.stop();\n\n        // Track update check event\n        captureUpdateEvent({\n          hadUpdates: updates.hasUpdates,\n        });\n\n        if (!updates.hasUpdates) {\n          console.log(chalk.green(\"✓ All VoltAgent packages are up to date\"));\n          return;\n        }\n\n        // Show found updates\n        console.log(chalk.yellow(`Found ${updates.count} outdated VoltAgent packages:`));\n        Object.entries(updates.updates).forEach(([name, version]) => {\n          console.log(`  ${chalk.cyan(name)}: ${chalk.gray(\"→\")} ${chalk.green(version)}`);\n        });\n\n        // Apply updates directly if --apply flag is used\n        if (options.apply) {\n          console.log(chalk.cyan(\"\\nApplying updates...\"));\n\n          try {\n            // Use ncu API to apply updates\n            await ncuPackage.default({\n              packageFile: path.join(process.cwd(), \"package.json\"),\n              upgrade: true,\n              filter: filter,\n            });\n\n            console.log(chalk.green(\"✓ Updates applied to package.json\"));\n            console.log(chalk.green(\"Run 'npm install' to install updated packages\"));\n            return;\n          } catch (error) {\n            console.error(chalk.red(\"Error applying updates:\"));\n            console.error(error instanceof Error ? error.message : String(error));\n            return;\n          }\n        }\n\n        // Use our custom interactive update\n        console.log(); // Empty line\n        console.log(chalk.cyan(\"Starting interactive update...\"));\n\n        await interactiveUpdate(updates.updates);\n      } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        console.error(chalk.red(\"Error checking for updates:\"));\n        console.error(errorMessage);\n\n        // Track error event\n        captureError({\n          command: \"update\",\n          errorMessage,\n        });\n      }\n    });\n};\n", "import { Command } from \"commander\";\nimport chalk from \"chalk\";\nimport os from \"os\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { captureError, captureWhoamiEvent } from \"../utils/analytics\";\n\nexport const registerWhoamiCommand = (program: Command): void => {\n  program\n    .command(\"whoami\")\n    .description(\"Display system and user information\")\n    .action(async () => {\n      try {\n        console.log(chalk.cyan(\"System and User Information:\"));\n        console.log(chalk.blue(`Username: ${os.userInfo().username}`));\n        console.log(chalk.blue(`Hostname: ${os.hostname()}`));\n        console.log(chalk.blue(`Platform: ${os.platform()}`));\n        console.log(chalk.blue(`OS: ${os.type()} ${os.release()}`));\n        console.log(chalk.blue(`Architecture: ${os.arch()}`));\n        console.log(chalk.blue(`Home Directory: ${os.homedir()}`));\n\n        // List installed voltagent packages\n        console.log(chalk.cyan(\"\\nInstalled VoltAgent Packages:\"));\n\n        let numVoltPackages = 0;\n        const packageJsonPath = path.join(process.cwd(), \"package.json\");\n        if (fs.existsSync(packageJsonPath)) {\n          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, \"utf-8\"));\n          const dependencies = {\n            ...packageJson.dependencies,\n            ...packageJson.devDependencies,\n          };\n\n          const voltagentPackages = Object.entries(dependencies || {})\n            .filter(([name]) => name.includes(\"voltagent\"))\n            .map(([name, version]) => ({ name, version }));\n\n          numVoltPackages = voltagentPackages.length;\n\n          if (voltagentPackages.length > 0) {\n            voltagentPackages.forEach((pkg) => {\n              console.log(chalk.blue(`${pkg.name}: ${pkg.version}`));\n            });\n          } else {\n            console.log(chalk.yellow(\"No VoltAgent packages found in package.json\"));\n          }\n        } else {\n          console.log(chalk.yellow(\"No package.json found in current directory\"));\n        }\n\n        // Track whoami event\n        captureWhoamiEvent({\n          numVoltPackages,\n        });\n      } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        console.error(chalk.red(\"Error:\"), errorMessage);\n\n        // Track error event\n        captureError({\n          command: \"whoami\",\n          errorMessage,\n        });\n\n        process.exit(1);\n      }\n    });\n};\n", "import type { Command } from \"commander\";\nimport chalk from \"chalk\";\nimport { captureError } from \"../utils/analytics\";\n\nexport const registerAddCommand = (program: Command) => {\n  program\n    .command(\"add <agent-slug>\")\n    .description(\"Add a VoltAgent agent from the marketplace to your project.\")\n    .action(async (agentSlug: string) => {\n      try {\n        console.log(\n          chalk.yellow(\n            `\\n🚧 The 'add' command is coming soon! This feature will allow you to easily integrate agents like '${agentSlug}' into your project.\\n`,\n          ),\n        );\n        console.log(\n          chalk.cyan(\"\\nWant to be among the first to try it out and shape its development?\\n\"),\n        );\n        console.log(\n          chalk.cyan(\n            `Join the discussion on GitHub and become an early user: ${chalk.underline(\n              \"https://github.com/orgs/voltagent/discussions/74/\",\n            )}\\n`,\n          ),\n        );\n        console.log(chalk.gray(\"\\nWe appreciate your interest!\\n\"));\n      } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        console.error(chalk.red(\"\\nAn unexpected error occurred:\"));\n        console.error(errorMessage);\n\n        captureError({\n          command: \"add\",\n          errorMessage,\n        });\n\n        // Ensure PostHog events are sent before exiting with error\n        // Need to import posthogClient if you uncomment tracking above or want error tracking here\n        // await posthogClient.shutdown();\n        process.exit(1);\n      }\n    });\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,uBAAwB;AACxB,IAAAA,gBAAkB;AAClB,oBAAmB;;;ACFnB,mBAAkB;AAClB,gBAAe;AACf,kBAAiB;AACjB,2BAAyB;;;ACJzB,0BAAwB;AACxB,kBAA6B;AAC7B,gBAAe;AACf,oBAAmB;AAGnB,IAAM,sBAAsB,MAAe;AACzC,SACE,QAAQ,IAAI,iCAAiC,OAC7C,QAAQ,IAAI,iCAAiC;AAEjD;AAGA,IAAM,SAAS,IAAI,4BAAQ,mDAAmD;AAAA,EAC5E,MAAM;AAAA,EACN,SAAS;AAAA,EACT,eAAe;AAAA,EACf,cAAc;AAChB,CAAC;AAGD,IAAM,eAAe,MAAc;AACjC,MAAI;AAEF,UAAM,WAAW,UAAAC,QAAG,SAAS;AAC7B,UAAM,OAAO,UAAAA,QAAG,KAAK,EAAE;AACvB,UAAM,WAAW,UAAAA,QAAG,SAAS;AAC7B,UAAM,OAAO,UAAAA,QAAG,KAAK;AAErB,UAAM,aAAa,GAAG,YAAY,QAAQ,YAAY;AACtD,WAAO,cAAAC,QAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE,OAAO,KAAK,EAAE,UAAU,GAAG,EAAE;AAAA,EACrF,SAAS,OAAP;AAEA,eAAO,YAAAC,IAAO;AAAA,EAChB;AACF;AAGA,IAAM,YAAY,MAAM;AACtB,MAAI;AACF,WAAO;AAAA,MACL,aAAa,UAAAF,QAAG,SAAS;AAAA,MACzB,YAAY,UAAAA,QAAG,QAAQ;AAAA,MACvB,YAAY,UAAAA,QAAG,QAAQ;AAAA,MACvB,SAAS,UAAAA,QAAG,KAAK;AAAA,IACnB;AAAA,EACF,SAAS,OAAP;AAEA,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACF;AACF;AAGO,IAAM,mBAAmB,CAAC,YAE3B;AAEJ,MAAI,oBAAoB;AAAG;AAE3B,SAAO,QAAQ;AAAA,IACb,YAAY,aAAa;AAAA,IACzB,OAAO;AAAA,IACP,YAAY;AAAA,MACV,iBAAiB,QAAQ;AAAA,MACzB,YAAY,aAAa;AAAA,MACzB,GAAG,UAAU;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAGO,IAAM,qBAAqB,CAAC,YAE7B;AAEJ,MAAI,oBAAoB;AAAG;AAE3B,SAAO,QAAQ;AAAA,IACb,YAAY,aAAa;AAAA,IACzB,OAAO;AAAA,IACP,YAAY;AAAA,MACV,aAAa,QAAQ;AAAA,MACrB,YAAY,aAAa;AAAA,MACzB,GAAG,UAAU;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAGO,IAAM,eAAe,CAAC,YAGvB;AAEJ,MAAI,oBAAoB;AAAG;AAE3B,SAAO,QAAQ;AAAA,IACb,YAAY,aAAa;AAAA,IACzB,OAAO;AAAA,IACP,YAAY;AAAA,MACV,SAAS,QAAQ;AAAA,MACjB,eAAe,QAAQ;AAAA,MACvB,YAAY,aAAa;AAAA,MACzB,GAAG,UAAU;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAGO,IAAM,qBAAqB,CAAC,YAE7B;AAEJ,MAAI,oBAAoB;AAAG;AAE3B,SAAO,QAAQ;AAAA,IACb,YAAY,aAAa;AAAA,IACzB,OAAO;AAAA,IACP,YAAY;AAAA,MACV,mBAAmB,QAAQ;AAAA,MAC3B,YAAY,aAAa;AAAA,MACzB,GAAG,UAAU;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAEA,IAAO,oBAAQ;;;AD7HR,IAAM,sBAAsB,CAAC,YAA2B;AAC7D,UACG,QAAQ,MAAM,EACd,YAAY,wCAAwC,EACpD,OAAO,YAAY;AAClB,QAAI;AACF,cAAQ,IAAI,aAAAG,QAAM,KAAK,gDAAgD,CAAC;AAGxE,YAAM,kBAAkB,YAAAC,QAAK,KAAK,QAAQ,IAAI,GAAG,cAAc;AAC/D,UAAI,CAAC,UAAAC,QAAG,WAAW,eAAe,GAAG;AACnC,gBAAQ;AAAA,UACN,aAAAF,QAAM;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAGA,YAAM,cAAc,KAAK,MAAM,UAAAE,QAAG,aAAa,iBAAiB,OAAO,CAAC;AAGxE,YAAM,UAAU,YAAY,WAAW,CAAC;AAGxC,UAAI,iBAAiB;AACrB,UAAI;AAEF,YAAI,UAAAA,QAAG,WAAW,YAAAD,QAAK,KAAK,QAAQ,IAAI,GAAG,gBAAgB,CAAC,GAAG;AAC7D,2BAAiB;AAAA,QACnB,WAAW,UAAAC,QAAG,WAAW,YAAAD,QAAK,KAAK,QAAQ,IAAI,GAAG,WAAW,CAAC,GAAG;AAC/D,2BAAiB;AAAA,QACnB;AAAA,MACF,SAAS,OAAP;AAEA,yBAAiB;AAAA,MACnB;AAEA,cAAQ,IAAI,aAAAD,QAAM,KAAK,6BAA6B,gBAAgB,CAAC;AAGrE,UAAI,WAAW;AACf,UAAI,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,MAAM,QAAQ;AAClD,gBAAQ,MAAM,IAAI;AAClB,mBAAW;AAAA,MACb;AAEA,UAAI,CAAC,UAAU;AACb,gBAAQ,IAAI,aAAAA,QAAM,OAAO,2DAA2D,CAAC;AAAA,MACvF,OAAO;AAEL,oBAAY,UAAU;AACtB,kBAAAE,QAAG,cAAc,iBAAiB,KAAK,UAAU,aAAa,MAAM,CAAC,CAAC;AACtE,gBAAQ,IAAI,aAAAF,QAAM,MAAM,4CAAuC,CAAC;AAAA,MAClE;AAGA,UAAI,qBAAqB;AACzB,UAAI;AAEF,kBAAAE,QAAG;AAAA,UACD,YAAAD,QAAK,KAAK,QAAQ,IAAI,GAAG,gBAAgB,cAAc,KAAK;AAAA,UAC5D,UAAAC,QAAG,UAAU;AAAA,QACf;AACA,6BAAqB;AAAA,MACvB,SAAS,OAAP;AAEA,6BAAqB;AAAA,MACvB;AAEA,UAAI,CAAC,oBAAoB;AACvB,gBAAQ,IAAI,aAAAF,QAAM,KAAK,sCAAsC,CAAC;AAC9D,YAAI;AACF,gBAAM,iBACJ,mBAAmB,SACf,kCACA,mBAAmB,SACjB,uCACA;AAER,6CAAS,gBAAgB,EAAE,OAAO,UAAU,CAAC;AAC7C,kBAAQ,IAAI,aAAAA,QAAM,MAAM,+CAA0C,CAAC;AAAA,QACrE,SAAS,OAAP;AACA,kBAAQ;AAAA,YACN,aAAAA,QAAM,IAAI,mCAAmC;AAAA,YAC7C,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe,YAAAC,QAAK,KAAK,QAAQ,IAAI,GAAG,YAAY;AAC1D,UAAI,CAAC,UAAAC,QAAG,WAAW,YAAY,GAAG;AAChC,kBAAAA,QAAG,UAAU,YAAY;AAAA,MAC3B;AAEA,cAAQ,IAAI,aAAAF,QAAM,MAAM,+CAA0C,CAAC;AAEnE,cAAQ,IAAI,MAAM,aAAAA,QAAM,KAAK,mBAAmB,GAAG,aAAAA,QAAM,MAAM,cAAc,CAAC;AAG9E,uBAAiB;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAP;AACA,YAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,cAAQ,MAAM,aAAAA,QAAM,IAAI,QAAQ,GAAG,YAAY;AAG/C,mBAAa;AAAA,QACX,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAED,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACL;;;AE5HA,IAAAG,gBAAkB;AAClB,iBAAgB;AAChB,IAAAC,eAAiB;AACjB,IAAAC,aAAe;AACf,iBAA4B;AAC5B,sBAAqB;AAerB,IAAM,kBAAkB,OACtB,aACA,YAC0B;AAC1B,MAAI;AAEF,UAAM,UAAU,cAAc,aAAAC,QAAK,QAAQ,WAAW,IAAI,QAAQ,IAAI;AACtE,UAAM,kBAAkB,eAAe,aAAAA,QAAK,KAAK,SAAS,cAAc;AAGxE,QAAI,CAAC,WAAAC,QAAG,WAAW,eAAe,GAAG;AACnC,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,SAAS,CAAC;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAGA,UAAM,SAAS,MAAiB,mBAAQ;AAAA,MACtC,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,QAAQ,SAAS;AAAA,IACnB,CAAC;AAGD,UAAM,UAAU;AAChB,UAAM,QAAQ,OAAO,KAAK,OAAO,EAAE;AAEnC,QAAI,QAAQ,GAAG;AACb,YAAM,cAAc,OAAO,QAAQ,OAAO,EACvC,IAAI,CAAC,CAAC,MAAM,OAAO,MAAM,OAAO,eAAU,SAAS,EACnD,KAAK,IAAI;AAEZ,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA,SAAS,SAAS;AAAA,EAA6B;AAAA,MACjD;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,SAAS,CAAC;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF,SAAS,OAAP;AACA,YAAQ,MAAM,+BAA+B,KAAK;AAClD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS,CAAC;AAAA,MACV,OAAO;AAAA,MACP,SAAS,+BAA+B,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,IAC/F;AAAA,EACF;AACF;AAKA,IAAM,oBAAoB,OACxB,SACA,gBACkB;AAElB,QAAM,UAAU,cAAc,aAAAD,QAAK,QAAQ,WAAW,IAAI,QAAQ,IAAI;AACtE,QAAM,kBAAkB,eAAe,aAAAA,QAAK,KAAK,SAAS,cAAc;AAGxE,QAAM,UAAU,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,MAAM,OAAO,MAAM;AAC/D,WAAO;AAAA,MACL,MAAM,GAAG,cAAAE,QAAM,KAAK,IAAI,MAAM,cAAAA,QAAM,KAAK,kBAAkB,MAAM,eAAe,CAAC,YAAO,cAAAA,QAAM,MAAM,OAAO;AAAA,MAC3G,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AAGD,QAAM,EAAE,iBAAiB,IAAI,MAAM,gBAAAC,QAAS,OAAO;AAAA,IACjD;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,SAAS,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK;AAAA;AAAA,IACrC;AAAA,EACF,CAAC;AAED,MAAI,iBAAiB,WAAW,GAAG;AACjC,YAAQ,IAAI,cAAAD,QAAM,OAAO,kCAAkC,CAAC;AAC5D;AAAA,EACF;AAGA,QAAM,iBAAiB,iBAAiB,KAAK,GAAG;AAEhD,UAAQ,IAAI,cAAAA,QAAM,KAAK,6CAA6C,CAAC;AAErE,MAAI;AAEF,UAAiB,mBAAQ;AAAA,MACvB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,IACV,CAAC;AAED,YAAQ,IAAI,cAAAA,QAAM,MAAM,kBAAa,iBAAiB,iCAAiC,CAAC;AACxF,YAAQ,IAAI,cAAAA,QAAM,MAAM,+CAA+C,CAAC;AAAA,EAC1E,SAAS,OAAP;AACA,YAAQ,MAAM,cAAAA,QAAM,IAAI,yBAAyB,CAAC;AAClD,YAAQ,MAAM,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK,CAAC;AAAA,EACtE;AACF;AAKA,IAAM,oBAAoB,CAAC,aAAqB,oBAAoC;AAClF,MAAI;AACF,UAAM,cAAc,KAAK,MAAM,WAAAD,QAAG,aAAa,iBAAiB,MAAM,CAAC;AAGvE,eAAW,WAAW;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AACD,UAAI,YAAY,OAAO,IAAI,WAAW,GAAG;AACvC,eAAO,YAAY,OAAO,EAAE,WAAW;AAAA,MACzC;AAAA,IACF;AAEA,WAAO;AAAA,EACT,SAAS,OAAP;AACA,WAAO;AAAA,EACT;AACF;AAKO,IAAM,wBAAwB,CAAC,YAA2B;AAC/D,UACG,QAAQ,QAAQ,EAChB,YAAY,2CAA2C,EACvD,OAAO,WAAW,wCAAwC,EAC1D,OAAO,OAAO,YAAY;AACzB,QAAI;AAEF,YAAM,cAAU,WAAAG,SAAI,yBAAyB,EAAE,MAAM;AAGrD,YAAM,SAAS;AACf,YAAM,UAAU,MAAM,gBAAgB,QAAW,EAAE,OAAO,CAAC;AAE3D,cAAQ,KAAK;AAGb,yBAAmB;AAAA,QACjB,YAAY,QAAQ;AAAA,MACtB,CAAC;AAED,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,IAAI,cAAAF,QAAM,MAAM,8CAAyC,CAAC;AAClE;AAAA,MACF;AAGA,cAAQ,IAAI,cAAAA,QAAM,OAAO,SAAS,QAAQ,oCAAoC,CAAC;AAC/E,aAAO,QAAQ,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,OAAO,MAAM;AAC3D,gBAAQ,IAAI,KAAK,cAAAA,QAAM,KAAK,IAAI,MAAM,cAAAA,QAAM,KAAK,QAAG,KAAK,cAAAA,QAAM,MAAM,OAAO,GAAG;AAAA,MACjF,CAAC;AAGD,UAAI,QAAQ,OAAO;AACjB,gBAAQ,IAAI,cAAAA,QAAM,KAAK,uBAAuB,CAAC;AAE/C,YAAI;AAEF,gBAAiB,mBAAQ;AAAA,YACvB,aAAa,aAAAF,QAAK,KAAK,QAAQ,IAAI,GAAG,cAAc;AAAA,YACpD,SAAS;AAAA,YACT;AAAA,UACF,CAAC;AAED,kBAAQ,IAAI,cAAAE,QAAM,MAAM,wCAAmC,CAAC;AAC5D,kBAAQ,IAAI,cAAAA,QAAM,MAAM,+CAA+C,CAAC;AACxE;AAAA,QACF,SAAS,OAAP;AACA,kBAAQ,MAAM,cAAAA,QAAM,IAAI,yBAAyB,CAAC;AAClD,kBAAQ,MAAM,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK,CAAC;AACpE;AAAA,QACF;AAAA,MACF;AAGA,cAAQ,IAAI;AACZ,cAAQ,IAAI,cAAAA,QAAM,KAAK,gCAAgC,CAAC;AAExD,YAAM,kBAAkB,QAAQ,OAAO;AAAA,IACzC,SAAS,OAAP;AACA,YAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,cAAQ,MAAM,cAAAA,QAAM,IAAI,6BAA6B,CAAC;AACtD,cAAQ,MAAM,YAAY;AAG1B,mBAAa;AAAA,QACX,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACL;;;AC9OA,IAAAG,gBAAkB;AAClB,IAAAC,aAAe;AACf,IAAAC,aAAe;AACf,IAAAC,eAAiB;AAGV,IAAM,wBAAwB,CAAC,YAA2B;AAC/D,UACG,QAAQ,QAAQ,EAChB,YAAY,qCAAqC,EACjD,OAAO,YAAY;AAClB,QAAI;AACF,cAAQ,IAAI,cAAAC,QAAM,KAAK,8BAA8B,CAAC;AACtD,cAAQ,IAAI,cAAAA,QAAM,KAAK,aAAa,WAAAC,QAAG,SAAS,EAAE,UAAU,CAAC;AAC7D,cAAQ,IAAI,cAAAD,QAAM,KAAK,aAAa,WAAAC,QAAG,SAAS,GAAG,CAAC;AACpD,cAAQ,IAAI,cAAAD,QAAM,KAAK,aAAa,WAAAC,QAAG,SAAS,GAAG,CAAC;AACpD,cAAQ,IAAI,cAAAD,QAAM,KAAK,OAAO,WAAAC,QAAG,KAAK,KAAK,WAAAA,QAAG,QAAQ,GAAG,CAAC;AAC1D,cAAQ,IAAI,cAAAD,QAAM,KAAK,iBAAiB,WAAAC,QAAG,KAAK,GAAG,CAAC;AACpD,cAAQ,IAAI,cAAAD,QAAM,KAAK,mBAAmB,WAAAC,QAAG,QAAQ,GAAG,CAAC;AAGzD,cAAQ,IAAI,cAAAD,QAAM,KAAK,iCAAiC,CAAC;AAEzD,UAAI,kBAAkB;AACtB,YAAM,kBAAkB,aAAAE,QAAK,KAAK,QAAQ,IAAI,GAAG,cAAc;AAC/D,UAAI,WAAAC,QAAG,WAAW,eAAe,GAAG;AAClC,cAAM,cAAc,KAAK,MAAM,WAAAA,QAAG,aAAa,iBAAiB,OAAO,CAAC;AACxE,cAAM,eAAe;AAAA,UACnB,GAAG,YAAY;AAAA,UACf,GAAG,YAAY;AAAA,QACjB;AAEA,cAAM,oBAAoB,OAAO,QAAQ,gBAAgB,CAAC,CAAC,EACxD,OAAO,CAAC,CAAC,IAAI,MAAM,KAAK,SAAS,WAAW,CAAC,EAC7C,IAAI,CAAC,CAAC,MAAM,OAAO,OAAO,EAAE,MAAM,QAAQ,EAAE;AAE/C,0BAAkB,kBAAkB;AAEpC,YAAI,kBAAkB,SAAS,GAAG;AAChC,4BAAkB,QAAQ,CAAC,QAAQ;AACjC,oBAAQ,IAAI,cAAAH,QAAM,KAAK,GAAG,IAAI,SAAS,IAAI,SAAS,CAAC;AAAA,UACvD,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,IAAI,cAAAA,QAAM,OAAO,6CAA6C,CAAC;AAAA,QACzE;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,cAAAA,QAAM,OAAO,4CAA4C,CAAC;AAAA,MACxE;AAGA,yBAAmB;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAP;AACA,YAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,cAAQ,MAAM,cAAAA,QAAM,IAAI,QAAQ,GAAG,YAAY;AAG/C,mBAAa;AAAA,QACX,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAED,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACL;;;AClEA,IAAAI,gBAAkB;AAGX,IAAM,qBAAqB,CAAC,YAAqB;AACtD,UACG,QAAQ,kBAAkB,EAC1B,YAAY,6DAA6D,EACzE,OAAO,OAAO,cAAsB;AACnC,QAAI;AACF,cAAQ;AAAA,QACN,cAAAC,QAAM;AAAA,UACJ;AAAA,2GAAuG;AAAA;AAAA,QACzG;AAAA,MACF;AACA,cAAQ;AAAA,QACN,cAAAA,QAAM,KAAK,yEAAyE;AAAA,MACtF;AACA,cAAQ;AAAA,QACN,cAAAA,QAAM;AAAA,UACJ,2DAA2D,cAAAA,QAAM;AAAA,YAC/D;AAAA,UACF;AAAA;AAAA,QACF;AAAA,MACF;AACA,cAAQ,IAAI,cAAAA,QAAM,KAAK,kCAAkC,CAAC;AAAA,IAC5D,SAAS,OAAP;AACA,YAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,cAAQ,MAAM,cAAAA,QAAM,IAAI,iCAAiC,CAAC;AAC1D,cAAQ,MAAM,YAAY;AAE1B,mBAAa;AAAA,QACX,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAKD,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACL;;;AL/BA,IAAM,YAAY,MAAM;AAEtB,QAAM,UAAU,IAAI,yBAAQ;AAG5B,UACG,KAAK,WAAW,EAChB,YAAY,4CAA4C,EACxD,QAAQ,OAAO;AAGlB,sBAAoB,OAAO;AAC3B,wBAAsB,OAAO;AAC7B,wBAAsB,OAAO;AAC7B,qBAAmB,OAAO;AAE1B,SAAO;AACT;AAEA,IAAM,SAAS,YAAY;AACzB,MAAI;AAEF,YAAQ;AAAA,MACN,cAAAC,QAAM;AAAA,QACJ,cAAAC,QAAO,SAAS,iBAAiB;AAAA,UAC/B,MAAM;AAAA,UACN,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AAGA,UAAM,UAAU,UAAU;AAC1B,UAAM,QAAQ,WAAW,QAAQ,IAAI;AAGrC,UAAM,kBAAc,SAAS;AAAA,EAC/B,SAAS,OAAP;AACA,UAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,YAAQ,MAAM,cAAAD,QAAM,IAAI,+BAA+B,CAAC;AACxD,YAAQ,MAAM,YAAY;AAG1B,iBAAa;AAAA,MACX,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAGD,UAAM,kBAAc,SAAS;AAC7B,YAAQ,KAAK,CAAC;AAAA,EAChB;AACF;AAGA,OAAO,EAAE,MAAM,CAAC,UAAU;AACxB,QAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAC1E,UAAQ,MAAM,UAAU,cAAc;AAGtC,eAAa;AAAA,IACX,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AAGD,oBAAc,SAAS,EAAE,KAAK,MAAM;AAClC,YAAQ,KAAK,CAAC;AAAA,EAChB,CAAC;AACH,CAAC;", "names": ["import_chalk", "os", "crypto", "uuidv4", "chalk", "path", "fs", "import_chalk", "import_path", "import_fs", "path", "fs", "chalk", "inquirer", "ora", "import_chalk", "import_os", "import_fs", "import_path", "chalk", "os", "path", "fs", "import_chalk", "chalk", "chalk", "fi<PERSON>t"]}