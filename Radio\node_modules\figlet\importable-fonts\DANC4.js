export default `flf2a$ 4 4 48 0 20 0 0
DANC4 by <PERSON> <<EMAIL>> 9.2003
    This font is based on the "dancing men" cipher used in Sir <PERSON>'s Sherlock Holmes story "The Adventure of the Dancing Men" (DANC),
which was later published in the collection "The Return of Sherlock Holmes".
    The cipher ignores punctuation, and marks a space by a flag held by the
dancing man for the last letter before the space. The last figure of
an entire message carries no flag, as there is no need for one. In this
implementation of the cipher, figures with no flags represent lowercase
letters; those with flags, uppercase letters. Thus, when FIGletising, you
should type only the letters, and uppercase the *last* letter of every word
except the last. For example "Come, <PERSON>!" becomes comEelsie.
    The messages in dancing-men cipher in DANC didn't use the letters
FJKQUWXZ. What's more, editions of DANC vary in what mistakes they make in
the cipher, though they are consistent in using the same figure for P & V.
To resolve this problem and supply the missing figures, I follow the TrueType
font designed in 1994 by <PERSON> of Sweden.
    The positions of the flags have been altered to keep this a 4-line FIGfont.



@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
   \\O    \\O/   \\O/   \\O     \\O/     O/ '\\   /\`@
.___|     Y     Y___, |      Y     <|    \\ /  @
    |    / \\    |    / \\    / \\    / \\    X   @
    |_ ./   \\, _|   _\\ /_ ./   \\, _| |_  /O\\# @@
  \\O/     O/ '\\   /\`@
   Y_    <|    \\ /  @
  /  |   / \\    Y   @
./   |_ _| |_   O\\# @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
   \\O  |_O     \\O    |_O_|#@
.___|   _|>     |_    _|   @
    | _|  \\    /  | _|  \\  @
    |_    |_ ./   |_    |_ @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
 \\O  @
  |\\ @
 / \\ @
_\\ /_@@
   O/  @
  /|   @
  / \\  @
./   \\,@@
  O  @
 /|  @
 / \\ @
_\\ /_@@
   O   @
  /|\\  @
  / \\  @
./   \\,@@
  O  @
  |\\ @
 / \\ @
_\\ /_@@
  \\O   @
   |\\  @
  / \\  @
./   \\,@@
  O/ @
 /|  @
 / \\ @
_\\ /_@@
   O   @
  /|   @
  / \\  @
./   \\,@@
  O  @
 /|\\ @
 / \\ @
_\\ /_@@
   O   @
   |\\  @
  / \\  @
./   \\,@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
  \\O/ '\\   /\`@
   Y_   \\ /  @
  /  |   X   @
./   |_ /O\\# @@
  \\O/# @
   Y_  @
  /  | @
./   |_@@
  O  @
  |# @
 / \\ @
_\\ /_@@
 \\O  @
  |# @
 / \\ @
_\\ /_@@
'\\   /\`@
  \\ /  @
   Y   @
   O\\# @@
  \\O/# @
   Y   @
  / \\  @
./   \\,@@
   O/# @
   |_  @
  /  | @
./   |_@@
'\\   /\`@
  \\ /  @
   Y   @
  /O#  @@
\\O/#@
 Y  @
 |  @
_|_ @@
   \\O/#@
.___Y  @
    |  @
    |_ @@
  \\O @
  _|#@
_| | @
   |_@@
   O/# @
   |   @
  / \\  @
./   \\,@@
 |_O  @
  _#> @
_|  \\ @
    |_@@
 \\O/#@
  Y  @
 / \\ @
_| |_@@
  O/#@
 <|  @
 / \\ @
_| |_@@
  \\O/#@
  _Y  @
_| |  @
   |_ @@
   \\O @
.___|#@
    | @
    |_@@
\\O    @
 |#__,@
 |    @
_|    @@
\\O/#  @
 Y___,@
 |    @
_|    @@
 |_O_|#@
  _|   @
_|  \\  @
    |_ @@
'\\   /\`@
  \\ /  @
   X   @
  /O\\# @@
  \\O#  @
   |_  @
  /  | @
./   |_@@
    O/#@
.___|  @
    |  @
    |_ @@
   O/#@
  _|  @
_| |  @
   |_ @@
  \\O   @
   |#  @
  / \\  @
./   \\,@@
   O/# @
  _|   @
_|  \\  @
     \\,@@
 O/#  @
 |___,@
 |    @
_|    @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
  \\O/  @
   Y_  @
  /  | @
./   |_@@
  O  @
  |  @
 / \\ @
_\\ /_@@
 \\O  @
  |  @
 / \\ @
_\\ /_@@
'\\   /\`@
  \\ /  @
   Y   @
   O\\  @@
  \\O/  @
   Y   @
  / \\  @
./   \\,@@
   O/  @
   |_  @
  /  | @
./   |_@@
'\\   /\`@
  \\ /  @
   Y   @
  /O   @@
\\O/@
 Y @
 | @
_|_@@
   \\O/@
.___Y @
    | @
    |_@@
  \\O @
  _| @
_| | @
   |_@@
   O/  @
   |   @
  / \\  @
./   \\,@@
 |_O  @
  _|> @
_|  \\ @
    |_@@
 \\O/ @
  Y  @
 / \\ @
_| |_@@
  O/ @
 <|  @
 / \\ @
_| |_@@
  \\O/@
  _Y @
_| | @
   |_@@
   \\O @
.___| @
    | @
    |_@@
\\O    @
 |___,@
 |    @
_|    @@
\\O/   @
 Y___,@
 |    @
_|    @@
 |_O_|@
  _|  @
_|  \\ @
    |_@@
'\\   /\`@
  \\ /  @
   X   @
  /O\\  @@
  \\O   @
   |_  @
  /  | @
./   |_@@
    O/@
.___| @
    | @
    |_@@
   O/@
  _| @
_| | @
   |_@@
  \\O   @
   |   @
  / \\  @
./   \\,@@
   O/  @
  _|   @
_|  \\  @
     \\,@@
 O/   @
 |___,@
 |    @
_|    @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
  \\O/     \\O/# @
   Y_      Y   @
  /  |    / \\  @
./   |_ ./   \\,@@
  \\O/   \\O/# @
  _Y     Y   @
_| |    / \\  @
   |_ ./   \\,@@
  \\O      \\O/# @
   |_      Y   @
  /  |    / \\  @
./   |_ ./   \\,@@
  \\O/     \\O/  @
   Y_      Y   @
  /  |    / \\  @
./   |_ ./   \\,@@
  \\O/   \\O/  @
  _Y     Y   @
_| |    / \\  @
   |_ ./   \\,@@
  \\O      \\O/  @
   |_      Y   @
  /  |    / \\  @
./   |_ ./   \\,@@
 |_O_| |_O_|@
  _|    _|  @
_|  \\ _|  \\ @
    |_    |_@@
`