export default `flf2a$ 4 3 8 -1 20
Cyberfont - medium
Figlet conversion by <PERSON>, <EMAIL>, 8-11-94
From: <EMAIL> (Lennert Stock)
Date: 15 Jul 1994 00:04:25 GMT

Here are some fonts. Non-figlet I'm afraid, if you wanna convert them, be
my guest. I posted the isometric fonts before.

------------------------------------------------------------------------------

     .x%%%%%%x.                                             .x%%%%%%x.
    ,%%%%%%%%%%.                                           .%%%%%%%%%%.
   ,%%%'  )'  \\)                                           :(  \`(  \`%%%.
  ,%x%)________) --------- L e n n e r t   S t o c k       ( _   __ (%x%.
  (%%%~^88P~88P|                                           |~=> .=-~ %%%)
  (%%::. .:,\\ .'                                           \`. /,:. .::%%)
  \`;%:\`\\. \`-' |                                             | \`-' ./':%:'
   \`\`x\`. -===.'                   <EMAIL> -------- \`.===- .'x''
    / \`:\`.__.;                                               :.__.':' \\
 .d8b.     ..\`.                                             .'..     .d8b.
$ $@
$ $@
$ $@
$ $@@
  /@
 / @
.  @
   @@
..@
''@
  @
  @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
. @
' @
  @
  @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
  @
  @
. @
' @@
   @
__ @
   @
   @@
 @
 @
.@
 @@
  / @
 /  @
/   @
    @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
$@
.@
.@
 @@
$@
.@
,@
 @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
__.@
 _]@
 . @
   @@
@
@
@
@@
____ @
|__| @
|  | @
     @@
___  @
|__] @
|__] @
     @@
____ @
|    @
|___ @
     @@
___  @
|  \\ @
|__/ @
     @@
____ @
|___ @
|___ @
     @@
____ @
|___ @
|    @
     @@
____ @
| __ @
|__] @
     @@
_  _ @
|__| @
|  | @
     @@
_ @
| @
| @
  @@
 _ @
 | @
_| @
   @@
_  _ @
|_/  @
| \\_ @
     @@
_    @
|    @
|___ @
     @@
_  _ @
|\\/| @
|  | @
     @@
_  _ @
|\\ | @
| \\| @
     @@
____ @
|  | @
|__| @
     @@
___  @
|__] @
|    @
     @@
____ @
|  | @
|_\\| @
     @@
____ @
|__/ @
|  \\ @
     @@
____ @
[__  @
___] @
     @@
___ @
 |  @
 |  @
    @@
_  _ @
|  | @
|__| @
     @@
_  _ @
|  | @
 \\/  @
     @@
_ _ _ @
| | | @
|_|_| @
      @@
_  _ @
 \\/  @
_/\\_ @
     @@
_   _ @
 \\_/  @
  |   @
      @@
___  @
  /  @
 /__ @
     @@
@
@
@
@@
\\   @
 \\  @
  \\ @
    @@
@
@
@
@@
@
@
@
@@
    @
    @
___ @
    @@
. @
\` @
  @
  @@
____ @
|__| @
|  | @
     @@
___  @
|__] @
|__] @
     @@
____ @
|    @
|___ @
     @@
___  @
|  \\ @
|__/ @
     @@
____ @
|___ @
|___ @
     @@
____ @
|___ @
|    @
     @@
____ @
| __ @
|__] @
     @@
_  _ @
|__| @
|  | @
     @@
_ @
| @
| @
  @@
 _ @
 | @
_| @
   @@
_  _ @
|_/  @
| \\_ @
     @@
_    @
|    @
|___ @
     @@
_  _ @
|\\/| @
|  | @
     @@
_  _ @
|\\ | @
| \\| @
     @@
____ @
|  | @
|__| @
     @@
___  @
|__] @
|    @
     @@
____ @
|  | @
|_\\| @
     @@
____ @
|__/ @
|  \\ @
     @@
____ @
[__  @
___] @
     @@
___ @
 |  @
 |  @
    @@
_  _ @
|  | @
|__| @
     @@
_  _ @
|  | @
 \\/  @
     @@
_ _ _ @
| | | @
|_|_| @
      @@
_  _ @
 \\/  @
_/\\_ @
     @@
_   _ @
 \\_/  @
  |   @
      @@
___  @
  /  @
 /__ @
     @@
@
@
@
@@
| @
| @
| @
| @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
`