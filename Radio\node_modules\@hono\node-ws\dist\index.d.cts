import { Hono } from 'hono';
import { UpgradeWebSocket } from 'hono/ws';
import { WebSocket } from 'ws';
import { Server } from 'node:http';
import { Http2Server, Http2SecureServer } from 'node:http2';

interface NodeWebSocket {
    upgradeWebSocket: UpgradeWebSocket<WebSocket, {
        onError: (err: unknown) => void;
    }>;
    injectWebSocket(server: Server | Http2Server | Http2SecureServer): void;
}
interface NodeWebSocketInit {
    app: Hono<any, any, any>;
    baseUrl?: string | URL;
}
/**
 * Create WebSockets for Node.js
 * @param init Options
 * @returns NodeWebSocket
 */
declare const createNodeWebSocket: (init: NodeWebSocketInit) => NodeWebSocket;

export { type NodeWebSocket, type NodeWebSocketInit, createNodeWebSocket };
