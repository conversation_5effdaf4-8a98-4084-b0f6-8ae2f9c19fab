export default `flf2a$ 1 1 11 -1 11 0

binary.flf (C) 1994 by <PERSON> (<EMAIL>) 94/12/05

figlet 2.1 font, includes ISO Latin-1, excludes "-D" option chars.

Spaces are not shown as binary.  If this is required, change "$@" below
to "00100000 @".  Change to "@" to remove the hardspace between words.
Note that figlet always removes spaces when it moves words to a new line.

Try option "-m 0" to remove the space between letters (octets).

$@
00100001 @
00100010 @
00100011 @
00100100 @
00100101 @
00100110 @
00100111 @
00101000 @
00101001 @
00101010 @
00101011 @
00101100 @
00101101 @
00101110 @
00101111 @
00110000 @
00110001 @
00110010 @
00110011 @
00110100 @
00110101 @
00110110 @
00110111 @
00111000 @
00111001 @
00111010 @
00111011 @
00111100 @
00111101 @
00111110 @
00111111 @
01000000 @
01000001 @
01000010 @
01000011 @
01000100 @
01000101 @
01000110 @
01000111 @
01001000 @
01001001 @
01001010 @
01001011 @
01001100 @
01001101 @
01001110 @
01001111 @
01010000 @
01010001 @
01010010 @
01010011 @
01010100 @
01010101 @
01010110 @
01010111 @
01011000 @
01011001 @
01011010 @
01011011 @
01011100 @
01011101 @
01011110 @
01011111 @
01100000 @
01100001 @
01100010 @
01100011 @
01100100 @
01100101 @
01100110 @
01100111 @
01101000 @
01101001 @
01101010 @
01101011 @
01101100 @
01101101 @
01101110 @
01101111 @
01110000 @
01110001 @
01110010 @
01110011 @
01110100 @
01110101 @
01110110 @
01110111 @
01111000 @
01111001 @
01111010 @
01111011 @
01111100 @
01111101 @
01111110 @
@
@
@
@
@
@
@
127
01111111 @
128
10000000 @
129
10000001 @
130
10000010 @
131
10000011 @
132
10000100 @
133
10000101 @
134
10000110 @
135
10000111 @
136
10001000 @
137
10001001 @
138
10001010 @
139
10001011 @
140
10001100 @
141
10001101 @
142
10001110 @
143
10001111 @
144
10010000 @
145
10010001 @
146
10010010 @
147
10010011 @
148
10010100 @
149
10010101 @
150
10010110 @
151
10010111 @
152
10011000 @
153
10011001 @
154
10011010 @
155
10011011 @
156
10011100 @
157
10011101 @
158
10011110 @
159
10011111 @
160
10100000 @
161
10100001 @
162
10100010 @
163
10100011 @
164
10100100 @
165
10100101 @
166
10100110 @
167
10100111 @
168
10101000 @
169
10101001 @
170
10101010 @
171
10101011 @
172
10101100 @
173
10101101 @
174
10101110 @
175
10101111 @
176
10110000 @
177
10110001 @
178
10110010 @
179
10110011 @
180
10110100 @
181
10110101 @
182
10110110 @
183
10110111 @
184
10111000 @
185
10111001 @
186
10111010 @
187
10111011 @
188
10111100 @
189
10111101 @
190
10111110 @
191
10111111 @
192
11000000 @
193
11000001 @
194
11000010 @
195
11000011 @
196
11000100 @
197
11000101 @
198
11000110 @
199
11000111 @
200
11001000 @
201
11001001 @
202
11001010 @
203
11001011 @
204
11001100 @
205
11001101 @
206
11001110 @
207
11001111 @
208
11010000 @
209
11010001 @
210
11010010 @
211
11010011 @
212
11010100 @
213
11010101 @
214
11010110 @
215
11010111 @
216
11011000 @
217
11011001 @
218
11011010 @
219
11011011 @
220
11011100 @
221
11011101 @
222
11011110 @
223
11011111 @
224
11100000 @
225
11100001 @
226
11100010 @
227
11100011 @
228
11100100 @
229
11100101 @
230
11100110 @
231
11100111 @
232
11101000 @
233
11101001 @
234
11101010 @
235
11101011 @
236
11101100 @
237
11101101 @
238
11101110 @
239
11101111 @
240
11110000 @
241
11110001 @
242
11110010 @
243
11110011 @
244
11110100 @
245
11110101 @
246
11110110 @
247
11110111 @
248
11111000 @
249
11111001 @
250
11111010 @
251
11111011 @
252
11111100 @
253
11111101 @
254
11111110 @
255
11111111 @
`