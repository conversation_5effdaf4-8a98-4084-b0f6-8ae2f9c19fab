
> @n8n/json-schema-to-zod@1.1.0 build /home/<USER>/work/n8n/n8n/packages/@n8n/json-schema-to-zod
> rimraf ./dist && pnpm run build:types && pnpm run build:cjs && pnpm run build:esm


> @n8n/json-schema-to-zod@1.1.0 build:types /home/<USER>/work/n8n/n8n/packages/@n8n/json-schema-to-zod
> tsc -p tsconfig.types.json


> @n8n/json-schema-to-zod@1.1.0 build:cjs /home/<USER>/work/n8n/n8n/packages/@n8n/json-schema-to-zod
> tsc -p tsconfig.cjs.json && node postcjs.js


> @n8n/json-schema-to-zod@1.1.0 build:esm /home/<USER>/work/n8n/n8n/packages/@n8n/json-schema-to-zod
> tsc -p tsconfig.esm.json && node postesm.js

