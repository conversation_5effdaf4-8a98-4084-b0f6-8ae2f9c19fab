{"name": "@voltagent/core", "version": "0.1.12", "description": "VoltAgent Core - AI agent framework for JavaScript", "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"@hono/node-server": "^1.14.0", "@hono/node-ws": "^1.1.1", "@hono/swagger-ui": "^0.5.1", "@hono/zod-openapi": "^0.19.6", "@libsql/client": "^0.15.0", "@modelcontextprotocol/sdk": "^1.10.1", "@n8n/json-schema-to-zod": "^1.1.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@types/ws": "^8.18.1", "hono": "^4.7.7", "npm-check-updates": "^17.1.18", "uuid": "^9.0.1", "ws": "^8.18.1", "zod": "^3.24.2"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.11", "@types/uuid": "^9.0.8", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsup": "^6.7.0", "typescript": "^5.0.4"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}}