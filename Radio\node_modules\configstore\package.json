{"name": "configstore", "version": "5.0.1", "description": "Easily load and save config without having to think about where and how", "license": "BSD-2-<PERSON><PERSON>", "repository": "yeoman/configstore", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["config", "store", "storage", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "dependencies": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}, "devDependencies": {"ava": "^2.1.0", "xo": "^0.24.0"}}