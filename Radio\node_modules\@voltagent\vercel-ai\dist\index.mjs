var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/index.ts
import { generateObject, generateText, streamObject, streamText } from "ai";

// src/utils/index.ts
import { tool } from "ai";
var convertToolsForSDK = /* @__PURE__ */ __name((tools) => {
  if (!tools || tools.length === 0) {
    return void 0;
  }
  const toolsMap = {};
  for (const agentTool of tools) {
    const sdkTool = tool({
      description: agentTool.description,
      parameters: agentTool.parameters,
      execute: agentTool.execute
    });
    toolsMap[agentTool.name] = sdkTool;
  }
  return toolsMap;
}, "convertToolsForSDK");

// src/index.ts
var VercelAIProvider = class {
  // @ts-ignore
  constructor(options) {
    this.options = options;
    __publicField(this, "getModelIdentifier", /* @__PURE__ */ __name((model) => {
      return model.modelId;
    }, "getModelIdentifier"));
    __publicField(this, "toMessage", /* @__PURE__ */ __name((message) => {
      return message;
    }, "toMessage"));
    __publicField(this, "createStepFromChunk", /* @__PURE__ */ __name((chunk) => {
      if (chunk.type === "text" && chunk.text) {
        return {
          id: "",
          type: "text",
          content: chunk.text,
          role: "assistant",
          usage: chunk.usage || void 0
        };
      }
      if (chunk.type === "tool-call" || chunk.type === "tool_call") {
        return {
          id: chunk.toolCallId,
          type: "tool_call",
          name: chunk.toolName,
          arguments: chunk.args,
          content: JSON.stringify([
            {
              type: "tool-call",
              toolCallId: chunk.toolCallId,
              toolName: chunk.toolName,
              args: chunk.args
            }
          ]),
          role: "assistant",
          usage: chunk.usage || void 0
        };
      }
      if (chunk.type === "tool-result" || chunk.type === "tool_result") {
        return {
          id: chunk.toolCallId,
          type: "tool_result",
          name: chunk.toolName,
          result: chunk.result,
          content: JSON.stringify([
            {
              type: "tool-result",
              toolCallId: chunk.toolCallId,
              result: chunk.result
            }
          ]),
          role: "assistant",
          usage: chunk.usage || void 0
        };
      }
      return null;
    }, "createStepFromChunk"));
    __publicField(this, "generateText", /* @__PURE__ */ __name((options) => __async(this, null, function* () {
      const vercelMessages = options.messages.map(this.toMessage);
      const vercelTools = options.tools ? convertToolsForSDK(options.tools) : void 0;
      const onStepFinish = options.onStepFinish ? (result) => __async(this, null, function* () {
        if (options.onStepFinish) {
          if (result.text) {
            const step = this.createStepFromChunk({
              type: "text",
              text: result.text,
              usage: result.usage
            });
            if (step)
              yield options.onStepFinish(step);
          }
          if (result.toolCalls && result.toolCalls.length > 0) {
            for (const toolCall of result.toolCalls) {
              const step = this.createStepFromChunk({
                type: "tool-call",
                toolCallId: toolCall.toolCallId,
                toolName: toolCall.toolName,
                args: toolCall.args,
                usage: result.usage
              });
              if (step)
                yield options.onStepFinish(step);
            }
          }
          if (result.toolResults && result.toolResults.length > 0) {
            for (const toolResult of result.toolResults) {
              const step = this.createStepFromChunk({
                type: "tool-result",
                toolCallId: toolResult.toolCallId,
                toolName: toolResult.toolName,
                result: toolResult.result,
                usage: result.usage
              });
              if (step)
                yield options.onStepFinish(step);
            }
          }
        }
      }) : void 0;
      try {
        const result = yield generateText(__spreadProps(__spreadValues({}, options.provider), {
          messages: vercelMessages,
          model: options.model,
          tools: vercelTools,
          maxSteps: options.maxSteps,
          abortSignal: options.signal,
          onStepFinish
        }));
        return {
          provider: result,
          text: result.text || "",
          usage: result.usage ? {
            promptTokens: result.usage.promptTokens,
            completionTokens: result.usage.completionTokens,
            totalTokens: result.usage.totalTokens
          } : void 0,
          toolCalls: result.toolCalls,
          toolResults: result.toolResults,
          finishReason: result.finishReason
        };
      } catch (sdkError) {
        const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, "llm_generate");
        throw voltagentErr;
      }
    }), "generateText"));
    __publicField(this, "generateObject", /* @__PURE__ */ __name((options) => __async(this, null, function* () {
      const vercelMessages = options.messages.map(this.toMessage);
      const onFinish = options.onStepFinish ? (result) => __async(this, null, function* () {
        if (options.onStepFinish) {
          const jsonResult = typeof result.object === "string" ? result.object : JSON.stringify(result.object);
          const step = this.createStepFromChunk({
            type: "text",
            text: jsonResult,
            usage: result.usage
          });
          if (step)
            yield options.onStepFinish(step);
        }
      }) : void 0;
      try {
        const result = yield generateObject(__spreadProps(__spreadValues({}, options.provider), {
          messages: vercelMessages,
          model: options.model,
          schema: options.schema,
          abortSignal: options.signal
        }));
        yield onFinish == null ? void 0 : onFinish(result);
        return {
          provider: result,
          object: result.object,
          usage: result.usage ? {
            promptTokens: result.usage.promptTokens,
            completionTokens: result.usage.completionTokens,
            totalTokens: result.usage.totalTokens
          } : void 0,
          finishReason: result.finishReason
        };
      } catch (sdkError) {
        const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, "object_generate");
        throw voltagentErr;
      }
    }), "generateObject"));
    this.generateText = this.generateText.bind(this);
    this.streamText = this.streamText.bind(this);
    this.generateObject = this.generateObject.bind(this);
    this.streamObject = this.streamObject.bind(this);
    this.toMessage = this.toMessage.bind(this);
    this.createStepFromChunk = this.createStepFromChunk.bind(this);
    this.getModelIdentifier = this.getModelIdentifier.bind(this);
  }
  /**
   * Creates a standardized VoltAgentError from a raw Vercel SDK error object.
   */
  _createVoltagentErrorFromSdkError(sdkError, errorStage = "llm_stream") {
    var _a;
    const originalError = (_a = sdkError.error) != null ? _a : sdkError;
    let voltagentErr;
    const potentialToolCallId = originalError == null ? void 0 : originalError.toolCallId;
    const potentialToolName = originalError == null ? void 0 : originalError.toolName;
    if (potentialToolCallId && potentialToolName) {
      const toolErrorDetails = {
        toolCallId: potentialToolCallId,
        toolName: potentialToolName,
        toolArguments: originalError == null ? void 0 : originalError.args,
        toolExecutionError: originalError
      };
      voltagentErr = {
        message: `Error during Vercel SDK operation (tool '${potentialToolName}'): ${originalError instanceof Error ? originalError.message : "Unknown tool error"}`,
        originalError,
        toolError: toolErrorDetails,
        stage: "tool_execution",
        code: originalError == null ? void 0 : originalError.code
      };
    } else {
      voltagentErr = {
        message: originalError instanceof Error ? originalError.message : `An unknown error occurred during Vercel AI operation (stage: ${errorStage})`,
        originalError,
        toolError: void 0,
        stage: errorStage,
        code: originalError == null ? void 0 : originalError.code
      };
    }
    return voltagentErr;
  }
  streamText(options) {
    return __async(this, null, function* () {
      const vercelMessages = options.messages.map(this.toMessage);
      const vercelTools = options.tools ? convertToolsForSDK(options.tools) : void 0;
      const onStepFinish = options.onStepFinish ? (result2) => __async(this, null, function* () {
        if (options.onStepFinish) {
          if (result2.text) {
            const step = this.createStepFromChunk({
              type: "text",
              text: result2.text,
              usage: result2.usage
            });
            if (step)
              yield options.onStepFinish(step);
          }
          if (result2.toolCalls && result2.toolCalls.length > 0) {
            for (const toolCall of result2.toolCalls) {
              const step = this.createStepFromChunk({
                type: "tool-call",
                toolCallId: toolCall.toolCallId,
                toolName: toolCall.toolName,
                args: toolCall.args,
                usage: result2.usage
              });
              if (step)
                yield options.onStepFinish(step);
            }
          }
          if (result2.toolResults && result2.toolResults.length > 0) {
            for (const toolResult of result2.toolResults) {
              const step = this.createStepFromChunk({
                type: "tool-result",
                toolCallId: toolResult.toolCallId,
                toolName: toolResult.toolName,
                result: toolResult.result,
                usage: result2.usage
              });
              if (step)
                yield options.onStepFinish(step);
            }
          }
        }
      }) : void 0;
      const result = streamText(__spreadProps(__spreadValues({}, options.provider), {
        messages: vercelMessages,
        model: options.model,
        tools: vercelTools,
        maxSteps: options.maxSteps,
        abortSignal: options.signal,
        onStepFinish,
        onChunk: (_0) => __async(this, [_0], function* ({ chunk }) {
          if (options == null ? void 0 : options.onChunk) {
            const step = this.createStepFromChunk(chunk);
            if (step)
              yield options.onChunk(step);
          }
        }),
        onFinish: options.onFinish ? (result2) => __async(this, null, function* () {
          var _a;
          (_a = options.onFinish) == null ? void 0 : _a.call(options, {
            text: result2.text,
            usage: result2.usage,
            finishReason: result2.finishReason,
            warnings: result2.warnings,
            providerResponse: result2
          });
        }) : void 0,
        onError: (sdkError) => {
          const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, "llm_stream");
          if (options.onError) {
            options.onError(voltagentErr);
          }
        }
      }));
      return {
        provider: result,
        textStream: result.textStream
      };
    });
  }
  streamObject(options) {
    return __async(this, null, function* () {
      const vercelMessages = options.messages.map(this.toMessage);
      const sdkOnFinish = /* @__PURE__ */ __name((event) => __async(this, null, function* () {
        if (options.onStepFinish) {
          const jsonResult = event.object ? JSON.stringify(event.object) : "";
          const step = this.createStepFromChunk({
            type: "text",
            // Simulate as a text step containing the final JSON
            text: jsonResult,
            usage: event.usage
            // Use usage from the event
          });
          if (step)
            yield options.onStepFinish(step);
        }
        if (options.onFinish && event.object) {
          let mappedUsage = void 0;
          if (event.usage) {
            mappedUsage = {
              promptTokens: event.usage.promptTokens,
              completionTokens: event.usage.completionTokens,
              totalTokens: event.usage.totalTokens
            };
          }
          const finishResult = {
            object: event.object,
            // The final object from the event
            usage: mappedUsage,
            // Mapped usage info
            warnings: event.warnings,
            providerResponse: event
            // Include the original SDK event object
            // finishReason is not typically available in Vercel's streamObject finish event
          };
          yield options.onFinish(finishResult);
        }
      }), "sdkOnFinish");
      const result = streamObject(__spreadProps(__spreadValues(__spreadProps(__spreadValues({}, options.provider), {
        messages: vercelMessages,
        model: options.model,
        schema: options.schema,
        abortSignal: options.signal
      }), options.onStepFinish || options.onFinish ? { onFinish: sdkOnFinish } : {}), {
        onError: (sdkError) => {
          const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, "object_stream");
          if (options.onError) {
            options.onError(voltagentErr);
          }
        }
      }));
      const partialObjectStream = result.partialObjectStream;
      return {
        provider: __spreadProps(__spreadValues({}, result), { partialObjectStream }),
        objectStream: partialObjectStream
      };
    });
  }
};
__name(VercelAIProvider, "VercelAIProvider");
export {
  VercelAIProvider
};
//# sourceMappingURL=index.mjs.map