{"name": "package-json", "version": "6.5.0", "description": "Get metadata of a package from the npm registry", "license": "MIT", "repository": "sindresorhus/package-json", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["npm", "registry", "package", "pkg", "package.json", "json", "module", "scope", "scoped"], "dependencies": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "devDependencies": {"@types/node": "^12.6.8", "ava": "^2.2.0", "mock-private-registry": "^1.1.2", "tsd": "^0.7.4", "xo": "^0.24.0"}}