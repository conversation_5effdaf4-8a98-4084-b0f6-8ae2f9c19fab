{"name": "latest-version", "version": "5.1.0", "description": "Get the latest version of an npm package", "license": "MIT", "repository": "sindresorhus/latest-version", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["latest", "version", "npm", "pkg", "package", "package.json", "current", "module"], "dependencies": {"package-json": "^6.3.0"}, "devDependencies": {"ava": "^1.4.1", "semver": "^6.0.0", "semver-regex": "^2.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}