{"name": "voltagent-example-with-mcp", "version": "0.1.0", "private": true, "description": "VoltAgent example demonstrating MCP integration with a filesystem server.", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "npx tsx watch -r dotenv/config ./src", "start": "node dist/index.js", "volt": "volt"}, "dependencies": {"@ai-sdk/openai": "^1.3.10", "@voltagent/cli": "^0.1.4", "@voltagent/core": "^0.1.12", "@voltagent/vercel-ai": "^0.1.5", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.5", "dotenv": "^16.3.1", "tsx": "^4.19.3", "typescript": "^5.8.2"}}