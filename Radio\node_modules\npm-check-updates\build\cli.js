#!/usr/bin/env node
"use strict";const E=require("./index.js"),Z=require("node:events"),ee=require("node:child_process"),te=require("node:path"),ie=require("node:fs"),ne=require("node:process");var x={},S={},j={},W;function q(){if(W)return j;W=1;class $ extends Error{constructor(s,r,l){super(l),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=r,this.exitCode=s,this.nestedError=void 0}}class w extends ${constructor(s){super(1,"commander.invalidArgument",s),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}}return j.CommanderError=$,j.InvalidArgumentError=w,j}var R;function M(){if(R)return S;R=1;const{InvalidArgumentError:$}=q();class w{constructor(r,l){switch(this.description=l||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,r[0]){case"<":this.required=!0,this._name=r.slice(1,-1);break;case"[":this.required=!1,this._name=r.slice(1,-1);break;default:this.required=!0,this._name=r;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(r,l){return l===this.defaultValue||!Array.isArray(l)?[r]:l.concat(r)}default(r,l){return this.defaultValue=r,this.defaultValueDescription=l,this}argParser(r){return this.parseArg=r,this}choices(r){return this.argChoices=r.slice(),this.parseArg=(l,a)=>{if(!this.argChoices.includes(l))throw new $(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(l,a):l},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}}function y(s){const r=s.name()+(s.variadic===!0?"...":"");return s.required?"<"+r+">":"["+r+"]"}return S.Argument=w,S.humanReadableArgName=y,S}var F={},N={},B;function Q(){if(B)return N;B=1;const{humanReadableArgName:$}=M();class w{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1,this.showGlobalOptions=!1}visibleCommands(s){const r=s.commands.filter(a=>!a._hidden),l=s._getHelpCommand();return l&&!l._hidden&&r.push(l),this.sortSubcommands&&r.sort((a,u)=>a.name().localeCompare(u.name())),r}compareOptions(s,r){const l=a=>a.short?a.short.replace(/^-/,""):a.long.replace(/^--/,"");return l(s).localeCompare(l(r))}visibleOptions(s){const r=s.options.filter(a=>!a.hidden),l=s._getHelpOption();if(l&&!l.hidden){const a=l.short&&s._findOption(l.short),u=l.long&&s._findOption(l.long);!a&&!u?r.push(l):l.long&&!u?r.push(s.createOption(l.long,l.description)):l.short&&!a&&r.push(s.createOption(l.short,l.description))}return this.sortOptions&&r.sort(this.compareOptions),r}visibleGlobalOptions(s){if(!this.showGlobalOptions)return[];const r=[];for(let l=s.parent;l;l=l.parent){const a=l.options.filter(u=>!u.hidden);r.push(...a)}return this.sortOptions&&r.sort(this.compareOptions),r}visibleArguments(s){return s._argsDescription&&s.registeredArguments.forEach(r=>{r.description=r.description||s._argsDescription[r.name()]||""}),s.registeredArguments.find(r=>r.description)?s.registeredArguments:[]}subcommandTerm(s){const r=s.registeredArguments.map(l=>$(l)).join(" ");return s._name+(s._aliases[0]?"|"+s._aliases[0]:"")+(s.options.length?" [options]":"")+(r?" "+r:"")}optionTerm(s){return s.flags}argumentTerm(s){return s.name()}longestSubcommandTermLength(s,r){return r.visibleCommands(s).reduce((l,a)=>Math.max(l,r.subcommandTerm(a).length),0)}longestOptionTermLength(s,r){return r.visibleOptions(s).reduce((l,a)=>Math.max(l,r.optionTerm(a).length),0)}longestGlobalOptionTermLength(s,r){return r.visibleGlobalOptions(s).reduce((l,a)=>Math.max(l,r.optionTerm(a).length),0)}longestArgumentTermLength(s,r){return r.visibleArguments(s).reduce((l,a)=>Math.max(l,r.argumentTerm(a).length),0)}commandUsage(s){let r=s._name;s._aliases[0]&&(r=r+"|"+s._aliases[0]);let l="";for(let a=s.parent;a;a=a.parent)l=a.name()+" "+l;return l+r+" "+s.usage()}commandDescription(s){return s.description()}subcommandDescription(s){return s.summary()||s.description()}optionDescription(s){const r=[];return s.argChoices&&r.push(`choices: ${s.argChoices.map(l=>JSON.stringify(l)).join(", ")}`),s.defaultValue!==void 0&&(s.required||s.optional||s.isBoolean()&&typeof s.defaultValue=="boolean")&&r.push(`default: ${s.defaultValueDescription||JSON.stringify(s.defaultValue)}`),s.presetArg!==void 0&&s.optional&&r.push(`preset: ${JSON.stringify(s.presetArg)}`),s.envVar!==void 0&&r.push(`env: ${s.envVar}`),r.length>0?`${s.description} (${r.join(", ")})`:s.description}argumentDescription(s){const r=[];if(s.argChoices&&r.push(`choices: ${s.argChoices.map(l=>JSON.stringify(l)).join(", ")}`),s.defaultValue!==void 0&&r.push(`default: ${s.defaultValueDescription||JSON.stringify(s.defaultValue)}`),r.length>0){const l=`(${r.join(", ")})`;return s.description?`${s.description} ${l}`:l}return s.description}formatHelp(s,r){const l=r.padWidth(s,r),a=r.helpWidth||80,u=2,A=2;function p(t,n){if(n){const o=`${t.padEnd(l+A)}${n}`;return r.wrap(o,a-u,l+A)}return t}function _(t){return t.join(`
`).replace(/^/gm," ".repeat(u))}let h=[`Usage: ${r.commandUsage(s)}`,""];const d=r.commandDescription(s);d.length>0&&(h=h.concat([r.wrap(d,a,0),""]));const C=r.visibleArguments(s).map(t=>p(r.argumentTerm(t),r.argumentDescription(t)));C.length>0&&(h=h.concat(["Arguments:",_(C),""]));const g=r.visibleOptions(s).map(t=>p(r.optionTerm(t),r.optionDescription(t)));if(g.length>0&&(h=h.concat(["Options:",_(g),""])),this.showGlobalOptions){const t=r.visibleGlobalOptions(s).map(n=>p(r.optionTerm(n),r.optionDescription(n)));t.length>0&&(h=h.concat(["Global Options:",_(t),""]))}const e=r.visibleCommands(s).map(t=>p(r.subcommandTerm(t),r.subcommandDescription(t)));return e.length>0&&(h=h.concat(["Commands:",_(e),""])),h.join(`
`)}padWidth(s,r){return Math.max(r.longestOptionTermLength(s,r),r.longestGlobalOptionTermLength(s,r),r.longestSubcommandTermLength(s,r),r.longestArgumentTermLength(s,r))}wrap(s,r,l,a=40){const u=" \\f\\t\\v   -   　\uFEFF",A=new RegExp(`[\\n][${u}]+`);if(s.match(A))return s;const p=r-l;if(p<a)return s;const _=s.slice(0,l),h=s.slice(l).replace(`\r
`,`
`),d=" ".repeat(l),g="\\s​",e=new RegExp(`
|.{1,${p-1}}([${g}]|$)|[^${g}]+?([${g}]|$)`,"g"),t=h.match(e)||[];return _+t.map((n,o)=>n===`
`?"":(o>0?d:"")+n.trimEnd()).join(`
`)}}return N.Help=w,N}var P={},U;function X(){if(U)return P;U=1;const{InvalidArgumentError:$}=q();class w{constructor(a,u){this.flags=a,this.description=u||"",this.required=a.includes("<"),this.optional=a.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(a),this.mandatory=!1;const A=r(a);this.short=A.shortFlag,this.long=A.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.presetArg=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0,this.conflictsWith=[],this.implied=void 0}default(a,u){return this.defaultValue=a,this.defaultValueDescription=u,this}preset(a){return this.presetArg=a,this}conflicts(a){return this.conflictsWith=this.conflictsWith.concat(a),this}implies(a){let u=a;return typeof a=="string"&&(u={[a]:!0}),this.implied=Object.assign(this.implied||{},u),this}env(a){return this.envVar=a,this}argParser(a){return this.parseArg=a,this}makeOptionMandatory(a=!0){return this.mandatory=!!a,this}hideHelp(a=!0){return this.hidden=!!a,this}_concatValue(a,u){return u===this.defaultValue||!Array.isArray(u)?[a]:u.concat(a)}choices(a){return this.argChoices=a.slice(),this.parseArg=(u,A)=>{if(!this.argChoices.includes(u))throw new $(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(u,A):u},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return s(this.name().replace(/^no-/,""))}is(a){return this.short===a||this.long===a}isBoolean(){return!this.required&&!this.optional&&!this.negate}}class y{constructor(a){this.positiveOptions=new Map,this.negativeOptions=new Map,this.dualOptions=new Set,a.forEach(u=>{u.negate?this.negativeOptions.set(u.attributeName(),u):this.positiveOptions.set(u.attributeName(),u)}),this.negativeOptions.forEach((u,A)=>{this.positiveOptions.has(A)&&this.dualOptions.add(A)})}valueFromOption(a,u){const A=u.attributeName();if(!this.dualOptions.has(A))return!0;const p=this.negativeOptions.get(A).presetArg,_=p!==void 0?p:!1;return u.negate===(_===a)}}function s(l){return l.split("-").reduce((a,u)=>a+u[0].toUpperCase()+u.slice(1))}function r(l){let a,u;const A=l.split(/[ |,]+/);return A.length>1&&!/^[[<]/.test(A[1])&&(a=A.shift()),u=A.shift(),!a&&/^-[^-]$/.test(u)&&(a=u,u=void 0),{shortFlag:a,longFlag:u}}return P.Option=w,P.DualOptions=y,P}var T={},G;function re(){if(G)return T;G=1;const $=3;function w(s,r){if(Math.abs(s.length-r.length)>$)return Math.max(s.length,r.length);const l=[];for(let a=0;a<=s.length;a++)l[a]=[a];for(let a=0;a<=r.length;a++)l[0][a]=a;for(let a=1;a<=r.length;a++)for(let u=1;u<=s.length;u++){let A=1;s[u-1]===r[a-1]?A=0:A=1,l[u][a]=Math.min(l[u-1][a]+1,l[u][a-1]+1,l[u-1][a-1]+A),u>1&&a>1&&s[u-1]===r[a-2]&&s[u-2]===r[a-1]&&(l[u][a]=Math.min(l[u][a],l[u-2][a-2]+1))}return l[s.length][r.length]}function y(s,r){if(!r||r.length===0)return"";r=Array.from(new Set(r));const l=s.startsWith("--");l&&(s=s.slice(2),r=r.map(p=>p.slice(2)));let a=[],u=$;const A=.4;return r.forEach(p=>{if(p.length<=1)return;const _=w(s,p),h=Math.max(s.length,p.length);(h-_)/h>A&&(_<u?(u=_,a=[p]):_===u&&a.push(p))}),a.sort((p,_)=>p.localeCompare(_)),l&&(a=a.map(p=>`--${p}`)),a.length>1?`
(Did you mean one of ${a.join(", ")}?)`:a.length===1?`
(Did you mean ${a[0]}?)`:""}return T.suggestSimilar=y,T}var L;function se(){if(L)return F;L=1;const $=Z.EventEmitter,w=ee,y=te,s=ie,r=ne,{Argument:l,humanReadableArgName:a}=M(),{CommanderError:u}=q(),{Help:A}=Q(),{Option:p,DualOptions:_}=X(),{suggestSimilar:h}=re();class d extends ${constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this.registeredArguments=[],this._args=this.registeredArguments,this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._executableDir=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._summary="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!0,this._outputConfiguration={writeOut:t=>r.stdout.write(t),writeErr:t=>r.stderr.write(t),getOutHelpWidth:()=>r.stdout.isTTY?r.stdout.columns:void 0,getErrHelpWidth:()=>r.stderr.isTTY?r.stderr.columns:void 0,outputError:(t,n)=>n(t)},this._hidden=!1,this._helpOption=void 0,this._addImplicitHelpCommand=void 0,this._helpCommand=void 0,this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._helpOption=e._helpOption,this._helpCommand=e._helpCommand,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}_getCommandAndAncestors(){const e=[];for(let t=this;t;t=t.parent)e.push(t);return e}command(e,t,n){let o=t,i=n;typeof o=="object"&&o!==null&&(i=o,o=null),i=i||{};const[,c,m]=e.match(/([^ ]+) *(.*)/),f=this.createCommand(c);return o&&(f.description(o),f._executableHandler=!0),i.isDefault&&(this._defaultCommandName=f._name),f._hidden=!!(i.noHelp||i.hidden),f._executableFile=i.executableFile||null,m&&f.arguments(m),this._registerCommand(f),f.parent=this,f.copyInheritedSettings(this),o?this:f}createCommand(e){return new d(e)}createHelp(){return Object.assign(new A,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,t){if(!e._name)throw new Error(`Command passed to .addCommand() must have a name
- specify the name in Command constructor or using .name()`);return t=t||{},t.isDefault&&(this._defaultCommandName=e._name),(t.noHelp||t.hidden)&&(e._hidden=!0),this._registerCommand(e),e.parent=this,e._checkForBrokenPassThrough(),this}createArgument(e,t){return new l(e,t)}argument(e,t,n,o){const i=this.createArgument(e,t);return typeof n=="function"?i.default(o).argParser(n):i.default(n),this.addArgument(i),this}arguments(e){return e.trim().split(/ +/).forEach(t=>{this.argument(t)}),this}addArgument(e){const t=this.registeredArguments.slice(-1)[0];if(t&&t.variadic)throw new Error(`only the last argument can be variadic '${t.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this.registeredArguments.push(e),this}helpCommand(e,t){if(typeof e=="boolean")return this._addImplicitHelpCommand=e,this;e=e??"help [command]";const[,n,o]=e.match(/([^ ]+) *(.*)/),i=t??"display help for command",c=this.createCommand(n);return c.helpOption(!1),o&&c.arguments(o),i&&c.description(i),this._addImplicitHelpCommand=!0,this._helpCommand=c,this}addHelpCommand(e,t){return typeof e!="object"?(this.helpCommand(e,t),this):(this._addImplicitHelpCommand=!0,this._helpCommand=e,this)}_getHelpCommand(){return this._addImplicitHelpCommand??(this.commands.length&&!this._actionHandler&&!this._findCommand("help"))?(this._helpCommand===void 0&&this.helpCommand(void 0,void 0),this._helpCommand):null}hook(e,t){const n=["preSubcommand","preAction","postAction"];if(!n.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${n.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(t):this._lifeCycleHooks[e]=[t],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=t=>{if(t.code!=="commander.executeSubCommandAsync")throw t},this}_exit(e,t,n){this._exitCallback&&this._exitCallback(new u(e,t,n)),r.exit(e)}action(e){const t=n=>{const o=this.registeredArguments.length,i=n.slice(0,o);return this._storeOptionsAsProperties?i[o]=this:i[o]=this.opts(),i.push(this),e.apply(this,i)};return this._actionHandler=t,this}createOption(e,t){return new p(e,t)}_callParseArg(e,t,n,o){try{return e.parseArg(t,n)}catch(i){if(i.code==="commander.invalidArgument"){const c=`${o} ${i.message}`;this.error(c,{exitCode:i.exitCode,code:i.code})}throw i}}_registerOption(e){const t=e.short&&this._findOption(e.short)||e.long&&this._findOption(e.long);if(t){const n=e.long&&this._findOption(e.long)?e.long:e.short;throw new Error(`Cannot add option '${e.flags}'${this._name&&` to command '${this._name}'`} due to conflicting flag '${n}'
-  already used by option '${t.flags}'`)}this.options.push(e)}_registerCommand(e){const t=o=>[o.name()].concat(o.aliases()),n=t(e).find(o=>this._findCommand(o));if(n){const o=t(this._findCommand(n)).join("|"),i=t(e).join("|");throw new Error(`cannot add command '${i}' as already have command '${o}'`)}this.commands.push(e)}addOption(e){this._registerOption(e);const t=e.name(),n=e.attributeName();if(e.negate){const i=e.long.replace(/^--no-/,"--");this._findOption(i)||this.setOptionValueWithSource(n,e.defaultValue===void 0?!0:e.defaultValue,"default")}else e.defaultValue!==void 0&&this.setOptionValueWithSource(n,e.defaultValue,"default");const o=(i,c,m)=>{i==null&&e.presetArg!==void 0&&(i=e.presetArg);const f=this.getOptionValue(n);i!==null&&e.parseArg?i=this._callParseArg(e,i,f,c):i!==null&&e.variadic&&(i=e._concatValue(i,f)),i==null&&(e.negate?i=!1:e.isBoolean()||e.optional?i=!0:i=""),this.setOptionValueWithSource(n,i,m)};return this.on("option:"+t,i=>{const c=`error: option '${e.flags}' argument '${i}' is invalid.`;o(i,c,"cli")}),e.envVar&&this.on("optionEnv:"+t,i=>{const c=`error: option '${e.flags}' value '${i}' from env '${e.envVar}' is invalid.`;o(i,c,"env")}),this}_optionEx(e,t,n,o,i){if(typeof t=="object"&&t instanceof p)throw new Error("To add an Option object use addOption() instead of option() or requiredOption()");const c=this.createOption(t,n);if(c.makeOptionMandatory(!!e.mandatory),typeof o=="function")c.default(i).argParser(o);else if(o instanceof RegExp){const m=o;o=(f,b)=>{const O=m.exec(f);return O?O[0]:b},c.default(i).argParser(o)}else c.default(o);return this.addOption(c)}option(e,t,n,o){return this._optionEx({},e,t,n,o)}requiredOption(e,t,n,o){return this._optionEx({mandatory:!0},e,t,n,o)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){return this._passThroughOptions=!!e,this._checkForBrokenPassThrough(),this}_checkForBrokenPassThrough(){if(this.parent&&this._passThroughOptions&&!this.parent._enablePositionalOptions)throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`)}storeOptionsAsProperties(e=!0){if(this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");if(Object.keys(this._optionValues).length)throw new Error("call .storeOptionsAsProperties() before setting option values");return this._storeOptionsAsProperties=!!e,this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,t){return this.setOptionValueWithSource(e,t,void 0)}setOptionValueWithSource(e,t,n){return this._storeOptionsAsProperties?this[e]=t:this._optionValues[e]=t,this._optionValueSources[e]=n,this}getOptionValueSource(e){return this._optionValueSources[e]}getOptionValueSourceWithGlobals(e){let t;return this._getCommandAndAncestors().forEach(n=>{n.getOptionValueSource(e)!==void 0&&(t=n.getOptionValueSource(e))}),t}_prepareUserArgs(e,t){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");if(t=t||{},e===void 0&&t.from===void 0){r.versions?.electron&&(t.from="electron");const o=r.execArgv??[];(o.includes("-e")||o.includes("--eval")||o.includes("-p")||o.includes("--print"))&&(t.from="eval")}e===void 0&&(e=r.argv),this.rawArgs=e.slice();let n;switch(t.from){case void 0:case"node":this._scriptPath=e[1],n=e.slice(2);break;case"electron":r.defaultApp?(this._scriptPath=e[1],n=e.slice(2)):n=e.slice(1);break;case"user":n=e.slice(0);break;case"eval":n=e.slice(1);break;default:throw new Error(`unexpected parse option { from: '${t.from}' }`)}return!this._name&&this._scriptPath&&this.nameFromFilename(this._scriptPath),this._name=this._name||"program",n}parse(e,t){const n=this._prepareUserArgs(e,t);return this._parseCommand([],n),this}async parseAsync(e,t){const n=this._prepareUserArgs(e,t);return await this._parseCommand([],n),this}_executeSubCommand(e,t){t=t.slice();let n=!1;const o=[".js",".ts",".tsx",".mjs",".cjs"];function i(O,v){const V=y.resolve(O,v);if(s.existsSync(V))return V;if(o.includes(y.extname(v)))return;const k=o.find(D=>s.existsSync(`${V}${D}`));if(k)return`${V}${k}`}this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let c=e._executableFile||`${this._name}-${e._name}`,m=this._executableDir||"";if(this._scriptPath){let O;try{O=s.realpathSync(this._scriptPath)}catch{O=this._scriptPath}m=y.resolve(y.dirname(O),m)}if(m){let O=i(m,c);if(!O&&!e._executableFile&&this._scriptPath){const v=y.basename(this._scriptPath,y.extname(this._scriptPath));v!==this._name&&(O=i(m,`${v}-${e._name}`))}c=O||c}n=o.includes(y.extname(c));let f;r.platform!=="win32"?n?(t.unshift(c),t=C(r.execArgv).concat(t),f=w.spawn(r.argv[0],t,{stdio:"inherit"})):f=w.spawn(c,t,{stdio:"inherit"}):(t.unshift(c),t=C(r.execArgv).concat(t),f=w.spawn(r.execPath,t,{stdio:"inherit"})),f.killed||["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(v=>{r.on(v,()=>{f.killed===!1&&f.exitCode===null&&f.kill(v)})});const b=this._exitCallback;f.on("close",O=>{O=O??1,b?b(new u(O,"commander.executeSubCommandAsync","(close)")):r.exit(O)}),f.on("error",O=>{if(O.code==="ENOENT"){const v=m?`searched for local subcommand relative to directory '${m}'`:"no directory for search for local subcommand, use .executableDir() to supply a custom directory",V=`'${c}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name or path
 - ${v}`;throw new Error(V)}else if(O.code==="EACCES")throw new Error(`'${c}' not executable`);if(!b)r.exit(1);else{const v=new u(1,"commander.executeSubCommandAsync","(error)");v.nestedError=O,b(v)}}),this.runningCommand=f}_dispatchSubcommand(e,t,n){const o=this._findCommand(e);o||this.help({error:!0});let i;return i=this._chainOrCallSubCommandHook(i,o,"preSubcommand"),i=this._chainOrCall(i,()=>{if(o._executableHandler)this._executeSubCommand(o,t.concat(n));else return o._parseCommand(t,n)}),i}_dispatchHelpCommand(e){e||this.help();const t=this._findCommand(e);return t&&!t._executableHandler&&t.help(),this._dispatchSubcommand(e,[],[this._getHelpOption()?.long??this._getHelpOption()?.short??"--help"])}_checkNumberOfArguments(){this.registeredArguments.forEach((e,t)=>{e.required&&this.args[t]==null&&this.missingArgument(e.name())}),!(this.registeredArguments.length>0&&this.registeredArguments[this.registeredArguments.length-1].variadic)&&this.args.length>this.registeredArguments.length&&this._excessArguments(this.args)}_processArguments(){const e=(n,o,i)=>{let c=o;if(o!==null&&n.parseArg){const m=`error: command-argument value '${o}' is invalid for argument '${n.name()}'.`;c=this._callParseArg(n,o,i,m)}return c};this._checkNumberOfArguments();const t=[];this.registeredArguments.forEach((n,o)=>{let i=n.defaultValue;n.variadic?o<this.args.length?(i=this.args.slice(o),n.parseArg&&(i=i.reduce((c,m)=>e(n,m,c),n.defaultValue))):i===void 0&&(i=[]):o<this.args.length&&(i=this.args[o],n.parseArg&&(i=e(n,i,n.defaultValue))),t[o]=i}),this.processedArgs=t}_chainOrCall(e,t){return e&&e.then&&typeof e.then=="function"?e.then(()=>t()):t()}_chainOrCallHooks(e,t){let n=e;const o=[];return this._getCommandAndAncestors().reverse().filter(i=>i._lifeCycleHooks[t]!==void 0).forEach(i=>{i._lifeCycleHooks[t].forEach(c=>{o.push({hookedCommand:i,callback:c})})}),t==="postAction"&&o.reverse(),o.forEach(i=>{n=this._chainOrCall(n,()=>i.callback(i.hookedCommand,this))}),n}_chainOrCallSubCommandHook(e,t,n){let o=e;return this._lifeCycleHooks[n]!==void 0&&this._lifeCycleHooks[n].forEach(i=>{o=this._chainOrCall(o,()=>i(this,t))}),o}_parseCommand(e,t){const n=this.parseOptions(t);if(this._parseOptionsEnv(),this._parseOptionsImplied(),e=e.concat(n.operands),t=n.unknown,this.args=e.concat(t),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),t);if(this._getHelpCommand()&&e[0]===this._getHelpCommand().name())return this._dispatchHelpCommand(e[1]);if(this._defaultCommandName)return this._outputHelpIfRequested(t),this._dispatchSubcommand(this._defaultCommandName,e,t);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),this._outputHelpIfRequested(n.unknown),this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();const o=()=>{n.unknown.length>0&&this.unknownOption(n.unknown[0])},i=`command:${this.name()}`;if(this._actionHandler){o(),this._processArguments();let c;return c=this._chainOrCallHooks(c,"preAction"),c=this._chainOrCall(c,()=>this._actionHandler(this.processedArgs)),this.parent&&(c=this._chainOrCall(c,()=>{this.parent.emit(i,e,t)})),c=this._chainOrCallHooks(c,"postAction"),c}if(this.parent&&this.parent.listenerCount(i))o(),this._processArguments(),this.parent.emit(i,e,t);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,t);this.listenerCount("command:*")?this.emit("command:*",e,t):this.commands.length?this.unknownCommand():(o(),this._processArguments())}else this.commands.length?(o(),this.help({error:!0})):(o(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(t=>t._name===e||t._aliases.includes(e))}_findOption(e){return this.options.find(t=>t.is(e))}_checkForMissingMandatoryOptions(){this._getCommandAndAncestors().forEach(e=>{e.options.forEach(t=>{t.mandatory&&e.getOptionValue(t.attributeName())===void 0&&e.missingMandatoryOptionValue(t)})})}_checkForConflictingLocalOptions(){const e=this.options.filter(n=>{const o=n.attributeName();return this.getOptionValue(o)===void 0?!1:this.getOptionValueSource(o)!=="default"});e.filter(n=>n.conflictsWith.length>0).forEach(n=>{const o=e.find(i=>n.conflictsWith.includes(i.attributeName()));o&&this._conflictingOption(n,o)})}_checkForConflictingOptions(){this._getCommandAndAncestors().forEach(e=>{e._checkForConflictingLocalOptions()})}parseOptions(e){const t=[],n=[];let o=t;const i=e.slice();function c(f){return f.length>1&&f[0]==="-"}let m=null;for(;i.length;){const f=i.shift();if(f==="--"){o===n&&o.push(f),o.push(...i);break}if(m&&!c(f)){this.emit(`option:${m.name()}`,f);continue}if(m=null,c(f)){const b=this._findOption(f);if(b){if(b.required){const O=i.shift();O===void 0&&this.optionMissingArgument(b),this.emit(`option:${b.name()}`,O)}else if(b.optional){let O=null;i.length>0&&!c(i[0])&&(O=i.shift()),this.emit(`option:${b.name()}`,O)}else this.emit(`option:${b.name()}`);m=b.variadic?b:null;continue}}if(f.length>2&&f[0]==="-"&&f[1]!=="-"){const b=this._findOption(`-${f[1]}`);if(b){b.required||b.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${b.name()}`,f.slice(2)):(this.emit(`option:${b.name()}`),i.unshift(`-${f.slice(2)}`));continue}}if(/^--[^=]+=/.test(f)){const b=f.indexOf("="),O=this._findOption(f.slice(0,b));if(O&&(O.required||O.optional)){this.emit(`option:${O.name()}`,f.slice(b+1));continue}}if(c(f)&&(o=n),(this._enablePositionalOptions||this._passThroughOptions)&&t.length===0&&n.length===0){if(this._findCommand(f)){t.push(f),i.length>0&&n.push(...i);break}else if(this._getHelpCommand()&&f===this._getHelpCommand().name()){t.push(f),i.length>0&&t.push(...i);break}else if(this._defaultCommandName){n.push(f),i.length>0&&n.push(...i);break}}if(this._passThroughOptions){o.push(f),i.length>0&&o.push(...i);break}o.push(f)}return{operands:t,unknown:n}}opts(){if(this._storeOptionsAsProperties){const e={},t=this.options.length;for(let n=0;n<t;n++){const o=this.options[n].attributeName();e[o]=o===this._versionOptionName?this._version:this[o]}return e}return this._optionValues}optsWithGlobals(){return this._getCommandAndAncestors().reduce((e,t)=>Object.assign(e,t.opts()),{})}error(e,t){this._outputConfiguration.outputError(`${e}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0}));const n=t||{},o=n.exitCode||1,i=n.code||"commander.error";this._exit(o,i,e)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in r.env){const t=e.attributeName();(this.getOptionValue(t)===void 0||["default","config","env"].includes(this.getOptionValueSource(t)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,r.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}_parseOptionsImplied(){const e=new _(this.options),t=n=>this.getOptionValue(n)!==void 0&&!["default","implied"].includes(this.getOptionValueSource(n));this.options.filter(n=>n.implied!==void 0&&t(n.attributeName())&&e.valueFromOption(this.getOptionValue(n.attributeName()),n)).forEach(n=>{Object.keys(n.implied).filter(o=>!t(o)).forEach(o=>{this.setOptionValueWithSource(o,n.implied[o],"implied")})})}missingArgument(e){const t=`error: missing required argument '${e}'`;this.error(t,{code:"commander.missingArgument"})}optionMissingArgument(e){const t=`error: option '${e.flags}' argument missing`;this.error(t,{code:"commander.optionMissingArgument"})}missingMandatoryOptionValue(e){const t=`error: required option '${e.flags}' not specified`;this.error(t,{code:"commander.missingMandatoryOptionValue"})}_conflictingOption(e,t){const n=c=>{const m=c.attributeName(),f=this.getOptionValue(m),b=this.options.find(v=>v.negate&&m===v.attributeName()),O=this.options.find(v=>!v.negate&&m===v.attributeName());return b&&(b.presetArg===void 0&&f===!1||b.presetArg!==void 0&&f===b.presetArg)?b:O||c},o=c=>{const m=n(c),f=m.attributeName();return this.getOptionValueSource(f)==="env"?`environment variable '${m.envVar}'`:`option '${m.flags}'`},i=`error: ${o(e)} cannot be used with ${o(t)}`;this.error(i,{code:"commander.conflictingOption"})}unknownOption(e){if(this._allowUnknownOption)return;let t="";if(e.startsWith("--")&&this._showSuggestionAfterError){let o=[],i=this;do{const c=i.createHelp().visibleOptions(i).filter(m=>m.long).map(m=>m.long);o=o.concat(c),i=i.parent}while(i&&!i._enablePositionalOptions);t=h(e,o)}const n=`error: unknown option '${e}'${t}`;this.error(n,{code:"commander.unknownOption"})}_excessArguments(e){if(this._allowExcessArguments)return;const t=this.registeredArguments.length,n=t===1?"":"s",i=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${t} argument${n} but got ${e.length}.`;this.error(i,{code:"commander.excessArguments"})}unknownCommand(){const e=this.args[0];let t="";if(this._showSuggestionAfterError){const o=[];this.createHelp().visibleCommands(this).forEach(i=>{o.push(i.name()),i.alias()&&o.push(i.alias())}),t=h(e,o)}const n=`error: unknown command '${e}'${t}`;this.error(n,{code:"commander.unknownCommand"})}version(e,t,n){if(e===void 0)return this._version;this._version=e,t=t||"-V, --version",n=n||"output the version number";const o=this.createOption(t,n);return this._versionOptionName=o.attributeName(),this._registerOption(o),this.on("option:"+o.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,t){return e===void 0&&t===void 0?this._description:(this._description=e,t&&(this._argsDescription=t),this)}summary(e){return e===void 0?this._summary:(this._summary=e,this)}alias(e){if(e===void 0)return this._aliases[0];let t=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(t=this.commands[this.commands.length-1]),e===t._name)throw new Error("Command alias can't be the same as its name");const n=this.parent?._findCommand(e);if(n){const o=[n.name()].concat(n.aliases()).join("|");throw new Error(`cannot add alias '${e}' to command '${this.name()}' as already have command '${o}'`)}return t._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(t=>this.alias(t)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;const t=this.registeredArguments.map(n=>a(n));return[].concat(this.options.length||this._helpOption!==null?"[options]":[],this.commands.length?"[command]":[],this.registeredArguments.length?t:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}nameFromFilename(e){return this._name=y.basename(e,y.extname(e)),this}executableDir(e){return e===void 0?this._executableDir:(this._executableDir=e,this)}helpInformation(e){const t=this.createHelp();return t.helpWidth===void 0&&(t.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),t.formatHelp(this,t)}_getHelpContext(e){e=e||{};const t={error:!!e.error};let n;return t.error?n=o=>this._outputConfiguration.writeErr(o):n=o=>this._outputConfiguration.writeOut(o),t.write=e.write||n,t.command=this,t}outputHelp(e){let t;typeof e=="function"&&(t=e,e=void 0);const n=this._getHelpContext(e);this._getCommandAndAncestors().reverse().forEach(i=>i.emit("beforeAllHelp",n)),this.emit("beforeHelp",n);let o=this.helpInformation(n);if(t&&(o=t(o),typeof o!="string"&&!Buffer.isBuffer(o)))throw new Error("outputHelp callback must return a string or a Buffer");n.write(o),this._getHelpOption()?.long&&this.emit(this._getHelpOption().long),this.emit("afterHelp",n),this._getCommandAndAncestors().forEach(i=>i.emit("afterAllHelp",n))}helpOption(e,t){return typeof e=="boolean"?(e?this._helpOption=this._helpOption??void 0:this._helpOption=null,this):(e=e??"-h, --help",t=t??"display help for command",this._helpOption=this.createOption(e,t),this)}_getHelpOption(){return this._helpOption===void 0&&this.helpOption(void 0,void 0),this._helpOption}addHelpOption(e){return this._helpOption=e,this}help(e){this.outputHelp(e);let t=r.exitCode||0;t===0&&e&&typeof e!="function"&&e.error&&(t=1),this._exit(t,"commander.help","(outputHelp)")}addHelpText(e,t){const n=["beforeAll","before","after","afterAll"];if(!n.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${n.join("', '")}'`);const o=`${e}Help`;return this.on(o,i=>{let c;typeof t=="function"?c=t({error:i.error,command:i.command}):c=t,c&&i.write(`${c}
`)}),this}_outputHelpIfRequested(e){const t=this._getHelpOption();t&&e.find(o=>t.is(o))&&(this.outputHelp(),this._exit(0,"commander.helpDisplayed","(outputHelp)"))}}function C(g){return g.map(e=>{if(!e.startsWith("--inspect"))return e;let t,n="127.0.0.1",o="9229",i;return(i=e.match(/^(--inspect(-brk)?)$/))!==null?t=i[1]:(i=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(t=i[1],/^\d+$/.test(i[3])?o=i[3]:n=i[3]):(i=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(t=i[1],n=i[3],o=i[4]),t&&o!=="0"?`${t}=${n}:${parseInt(o)+1}`:e})}return F.Command=d,F}var J;function oe(){if(J)return x;J=1;const{Argument:$}=M(),{Command:w}=se(),{CommanderError:y,InvalidArgumentError:s}=q(),{Help:r}=Q(),{Option:l}=X();return x.program=new w,x.createCommand=a=>new w(a),x.createOption=(a,u)=>new l(a,u),x.createArgument=(a,u)=>new $(a,u),x.Command=w,x.Option=l,x.Argument=$,x.Help=r,x.CommanderError=y,x.InvalidArgumentError=s,x.InvalidOptionArgumentError=s,x}var ae=oe();const le=E.getDefaultExportFromCjs(ae),{program:H,createCommand:me,createArgument:ge,createOption:_e,CommanderError:Oe,InvalidArgumentError:Ae,InvalidOptionArgumentError:be,Command:Ce,Argument:we,Option:K,Help:ue}=le;var I,Y;function ce(){if(Y)return I;Y=1,I=w;function $(s){return s instanceof Buffer?Buffer.from(s):new s.constructor(s.buffer.slice(),s.byteOffset,s.length)}function w(s){if(s=s||{},s.circles)return y(s);const r=new Map;if(r.set(Date,p=>new Date(p)),r.set(Map,(p,_)=>new Map(a(Array.from(p),_))),r.set(Set,(p,_)=>new Set(a(Array.from(p),_))),s.constructorHandlers)for(const p of s.constructorHandlers)r.set(p[0],p[1]);let l=null;return s.proto?A:u;function a(p,_){const h=Object.keys(p),d=new Array(h.length);for(let C=0;C<h.length;C++){const g=h[C],e=p[g];typeof e!="object"||e===null?d[g]=e:e.constructor!==Object&&(l=r.get(e.constructor))?d[g]=l(e,_):ArrayBuffer.isView(e)?d[g]=$(e):d[g]=_(e)}return d}function u(p){if(typeof p!="object"||p===null)return p;if(Array.isArray(p))return a(p,u);if(p.constructor!==Object&&(l=r.get(p.constructor)))return l(p,u);const _={};for(const h in p){if(Object.hasOwnProperty.call(p,h)===!1)continue;const d=p[h];typeof d!="object"||d===null?_[h]=d:d.constructor!==Object&&(l=r.get(d.constructor))?_[h]=l(d,u):ArrayBuffer.isView(d)?_[h]=$(d):_[h]=u(d)}return _}function A(p){if(typeof p!="object"||p===null)return p;if(Array.isArray(p))return a(p,A);if(p.constructor!==Object&&(l=r.get(p.constructor)))return l(p,A);const _={};for(const h in p){const d=p[h];typeof d!="object"||d===null?_[h]=d:d.constructor!==Object&&(l=r.get(d.constructor))?_[h]=l(d,A):ArrayBuffer.isView(d)?_[h]=$(d):_[h]=A(d)}return _}}function y(s){const r=[],l=[],a=new Map;if(a.set(Date,h=>new Date(h)),a.set(Map,(h,d)=>new Map(A(Array.from(h),d))),a.set(Set,(h,d)=>new Set(A(Array.from(h),d))),s.constructorHandlers)for(const h of s.constructorHandlers)a.set(h[0],h[1]);let u=null;return s.proto?_:p;function A(h,d){const C=Object.keys(h),g=new Array(C.length);for(let e=0;e<C.length;e++){const t=C[e],n=h[t];if(typeof n!="object"||n===null)g[t]=n;else if(n.constructor!==Object&&(u=a.get(n.constructor)))g[t]=u(n,d);else if(ArrayBuffer.isView(n))g[t]=$(n);else{const o=r.indexOf(n);o!==-1?g[t]=l[o]:g[t]=d(n)}}return g}function p(h){if(typeof h!="object"||h===null)return h;if(Array.isArray(h))return A(h,p);if(h.constructor!==Object&&(u=a.get(h.constructor)))return u(h,p);const d={};r.push(h),l.push(d);for(const C in h){if(Object.hasOwnProperty.call(h,C)===!1)continue;const g=h[C];if(typeof g!="object"||g===null)d[C]=g;else if(g.constructor!==Object&&(u=a.get(g.constructor)))d[C]=u(g,p);else if(ArrayBuffer.isView(g))d[C]=$(g);else{const e=r.indexOf(g);e!==-1?d[C]=l[e]:d[C]=p(g)}}return r.pop(),l.pop(),d}function _(h){if(typeof h!="object"||h===null)return h;if(Array.isArray(h))return A(h,_);if(h.constructor!==Object&&(u=a.get(h.constructor)))return u(h,_);const d={};r.push(h),l.push(d);for(const C in h){const g=h[C];if(typeof g!="object"||g===null)d[C]=g;else if(g.constructor!==Object&&(u=a.get(g.constructor)))d[C]=u(g,_);else if(ArrayBuffer.isView(g))d[C]=$(g);else{const e=r.indexOf(g);e!==-1?d[C]=l[e]:d[C]=_(g)}}return r.pop(),l.pop(),d}}return I}var he=ce();const fe=E.getDefaultExportFromCjs(he),z="Output the version number of npm-check-updates.",pe=$=>$.replace(/`/g,""),de=fe();(async()=>{const{default:$}=await Promise.resolve().then(()=>require("./index-BlZRi20K.js")),w=$({pkg:E.pkg});if(w.update&&w.update.latest!==E.pkg.version){const{default:i}=await Promise.resolve().then(()=>require("./index-vtankfkr.js")),c=E.semver.parse(w.update.current)?.major,m=E.semver.parse(w.update.latest)?.major,b=(c&&m&&m>=c?new Array(m-c).fill(0).map((v,V)=>c+V+1):[]).map(v=>`${E.pkg.homepage??""}/releases/tag/v${v}.0.0`),O=`${E.pkg.homepage??""}/compare/v${w.update.current}...v${w.update.latest}`;w.notify({defer:!1,isGlobal:!0,message:`Update available ${i.dim("{currentVersion}")}${i.reset(" → ")}${w.update.type==="major"?i.red("{latestVersion}"):w.update.type==="minor"?i.yellow("{latestVersion}"):i.green("{latestVersion}")}
Run ${i.cyan("{updateCommand}")} to update
${i.dim.underline(w.update.type==="major"?b.map(v=>i.dim.underline(v)).join(`
`):O)}`})}const y=process.argv.slice(2),s=y.findIndex(i=>i==="--help"||i==="-h");if(s!==-1&&y[s+1]){const i=y[s+1].replace(/^-*/,"");i==="help"||i==="h"?console.info("Would you like some help with your help?"):(await E.chalkInit(),[...y.slice(0,s),...y.slice(s+1)].forEach(m=>{const f=m.replace(/^-*/,""),b=E.cliOptionsSorted.find(O=>f===O.long||f===O.short||f===`no-${O.long}`&&O.type==="boolean");console.info(b?E.renderExtendedHelp(b)+`
`:f==="version"||f==="v"||f==="V"?E.renderExtendedHelp({long:"version",short:"v",description:z,type:"string"})+`
`:`Unknown option: ${m}`)})),process.exit(0)}const r=new Set(E.cliOptionsSorted.filter(i=>i.cli===!1).map(i=>`--${i.long}`));H.description("[filter] is a list or regex of package names to check (all others will be ignored).").usage("[options] [filter]").configureHelp({optionTerm:i=>i.long&&r.has(i.long)?i.long.replace("--","")+"*":i.long==="--version"?"-v, -V, --version":i.flags.replace("[bool]",""),optionDescription:i=>i.long==="--version"?z:i.long==="--help"?`You're lookin' at it. Run "ncu --help <option>" for a specific option.`:ue.prototype.optionDescription(i)}).addOption(new K("-v, --versionAlias").hideHelp()).on("option:versionAlias",()=>{console.info(E.pkg.version),process.exit(0)}),E.cliOptionsSorted.forEach(({long:i,short:c,arg:m,description:f,default:b,help:O,parse:v,type:V})=>{const k=`${c?`-${c}, `:""}--${i}${m?` <${m}>`:""}`,D=`${pe(f)}${O?` Run "ncu --help ${i}" for details.`:""}`;H.option(k,D,v||b,v?b:void 0),V==="boolean"&&H.addOption(new K(`--no-${i}`).default(!1).hideHelp())}),H.version(E.pkg.version);const l=de(H._optionValues);H.parse(process.argv);const a=H.opts(),u=process.argv.slice(2),{color:A,configFileName:p,configFilePath:_,global:h,packageFile:d,mergeConfig:C}=a;await E.chalkInit(A);const g=!process.env.NCU_TESTS||_||C?await E.getNcuRc({configFileName:p,configFilePath:_,global:h,packageFile:d,options:{...a,cli:!0}}):null,e=(g?.args||[]).filter((i,c,m)=>(typeof i!="string"||!i.startsWith("-")||!u.includes(i))&&(typeof m[c-1]!="string"||!m[c-1].startsWith("-")||!u.includes(m[c-1]))),t=[...process.argv.slice(0,2),...e,...u];H._optionValues=l,H.parse(t);const n=H.opts(),o={...g&&Object.keys(g.config).length>0?{rcConfigPath:g.filePath}:null,...E.pickBy(H.opts(),i=>i!==void 0),args:H.args,...n.filter?{filter:n.filter}:null,...n.reject?{reject:n.reject}:null};E.run(o,{cli:!0})})();
//# sourceMappingURL=cli.js.map
