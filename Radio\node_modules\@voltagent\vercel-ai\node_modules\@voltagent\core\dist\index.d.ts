import { z } from 'zod';
import { Span } from '@opentelemetry/api';
import { SpanExporter } from '@opentelemetry/sdk-trace-base';
import { ClientCapabilities } from '@modelcontextprotocol/sdk/types.js';
import { EventEmitter } from 'node:events';

/**
 * Represents a collection of related tools with optional shared instructions.
 */
type Toolkit = {
    /**
     * Unique identifier name for the toolkit. Used for management and potentially logging.
     */
    name: string;
    /**
     * A brief description of what the toolkit does or what tools it contains.
     * Optional.
     */
    description?: string;
    /**
     * Shared instructions for the LLM on how to use the tools within this toolkit.
     * These instructions are intended to be added to the system prompt if `addInstructions` is true.
     * Optional.
     */
    instructions?: string;
    /**
     * Whether to automatically add the toolkit's `instructions` to the agent's system prompt.
     * If true, the instructions from individual tools within this toolkit might be ignored
     * by the Agent's system message generation logic to avoid redundancy.
     * Defaults to false.
     */
    addInstructions?: boolean;
    /**
     * An array of Tool instances that belong to this toolkit.
     */
    tools: Tool<ToolSchema>[];
};
/**
 * Helper function for creating a new toolkit.
 * Provides default values and ensures the basic structure is met.
 *
 * @param options - The configuration options for the toolkit.
 * @returns A Toolkit object.
 */
declare const createToolkit: (options: Toolkit) => Toolkit;

/**
 * Status of a tool at any given time
 */
type ToolStatus = "idle" | "working" | "error" | "completed";
/**
 * Tool status information
 */
type ToolStatusInfo = {
    name: string;
    status: ToolStatus;
    result?: any;
    error?: any;
    input?: any;
    output?: any;
    timestamp: Date;
    parameters?: any;
};
/**
 * Manager class to handle all tool-related operations, including Toolkits.
 */
declare class ToolManager {
    /**
     * Standalone tools managed by this manager.
     */
    private tools;
    /**
     * Toolkits managed by this manager.
     */
    private toolkits;
    /**
     * Creates a new ToolManager.
     * Accepts both individual tools and toolkits.
     */
    constructor(items?: (AgentTool | Toolkit)[]);
    /**
     * Get all individual tools and tools within toolkits as a flattened list.
     */
    getTools(): BaseTool[];
    /**
     * Get all toolkits managed by this manager.
     */
    getToolkits(): Toolkit[];
    /**
     * Add an individual tool to the manager.
     * If a standalone tool with the same name already exists, it will be replaced.
     * A warning is issued if the name conflicts with a tool inside a toolkit, but the standalone tool is still added/replaced.
     * @returns true if the tool was successfully added or replaced.
     */
    addTool(tool: AgentTool): boolean;
    /**
     * Add a toolkit to the manager.
     * If a toolkit with the same name already exists, it will be replaced.
     * Also checks if any tool within the toolkit conflicts with existing standalone tools or tools in other toolkits.
     * @returns true if the toolkit was successfully added or replaced.
     */
    addToolkit(toolkit: Toolkit): boolean;
    /**
     * Add multiple tools or toolkits to the manager.
     */
    addItems(items: (AgentTool | Toolkit)[]): void;
    /**
     * Remove a standalone tool by name. Does not remove tools from toolkits.
     * @returns true if the tool was removed, false if it wasn't found.
     */
    removeTool(toolName: string): boolean;
    /**
     * Remove a toolkit by name.
     * @returns true if the toolkit was removed, false if it wasn't found.
     */
    removeToolkit(toolkitName: string): boolean;
    /**
     * Prepare tools for text generation (includes tools from toolkits).
     */
    prepareToolsForGeneration(dynamicTools?: BaseTool[]): BaseTool[];
    /**
     * Get agent's tools (including those in toolkits) for API exposure.
     */
    getToolsForApi(): {
        name: string;
        description: string;
        parameters: any;
    }[];
    /**
     * Check if a tool with the given name exists (either standalone or in a toolkit).
     */
    hasTool(toolName: string): boolean;
    /**
     * Get a tool by name (searches standalone tools and tools within toolkits).
     * @param toolName The name of the tool to get
     * @returns The tool (as BaseTool) or undefined if not found
     */
    getToolByName(toolName: string): BaseTool | undefined;
    /**
     * Execute a tool by name
     * @param toolName The name of the tool to execute
     * @param args The arguments to pass to the tool
     * @param options Optional execution options like signal
     * @returns The result of the tool execution
     * @throws Error if the tool doesn't exist or fails to execute
     */
    executeTool(toolName: string, args: any, options?: ToolExecuteOptions): Promise<any>;
}

/**
 * Tool definition compatible with Vercel AI SDK
 */
type AgentTool = BaseTool;
/**
 * Tool options for creating a new tool
 */
type ToolOptions<T extends ToolSchema = ToolSchema> = {
    /**
     * Unique identifier for the tool
     */
    id?: string;
    /**
     * Name of the tool
     */
    name: string;
    /**
     * Description of the tool
     */
    description: string;
    /**
     * Tool parameter schema
     */
    parameters: T;
    /**
     * Function to execute when the tool is called
     */
    execute: (args: z.infer<T>, options?: ToolExecuteOptions) => Promise<unknown>;
};
/**
 * Tool class for defining tools that agents can use
 */
declare class Tool<T extends ToolSchema = ToolSchema> {
    /**
     * Unique identifier for the tool
     */
    readonly id: string;
    /**
     * Name of the tool
     */
    readonly name: string;
    /**
     * Description of the tool
     */
    readonly description: string;
    /**
     * Tool parameter schema
     */
    readonly parameters: T;
    /**
     * Function to execute when the tool is called
     */
    readonly execute: (args: z.infer<T>, options?: ToolExecuteOptions) => Promise<unknown>;
    /**
     * Create a new tool
     */
    constructor(options: ToolOptions<T>);
}
/**
 * Helper function for creating a new tool
 */
declare const createTool: <T extends ToolSchema>(options: ToolOptions<T>) => Tool<T>;
/**
 * Alias for createTool function
 */
declare const tool: <T extends ToolSchema>(options: ToolOptions<T>) => Tool<T>;

type EventStatus = AgentStatus;
type TimelineEventType = "memory" | "tool" | "agent" | "retriever";
/**
 * Types for tracked event functionality
 */
type EventUpdater = (updateOptions: {
    status?: AgentStatus;
    data?: Record<string, any>;
}) => Promise<AgentHistoryEntry | undefined>;

interface ExportAgentHistoryPayload {
    agent_id: string;
    project_id: string;
    history_id: string;
    timestamp: string;
    type: string;
    status: string;
    input: Record<string, unknown>;
    output?: Record<string, unknown>;
    error?: Record<string, unknown>;
    usage?: Record<string, unknown>;
    agent_snapshot?: Record<string, unknown>;
    steps?: HistoryStep[];
    userId?: string;
    conversationId?: string;
}
interface ExportTimelineEventPayload {
    history_id: string;
    event_id: string;
    event: TimelineEvent;
}
interface AgentHistoryUpdatableFields {
    input?: AgentHistoryEntry["input"];
    output?: string;
    status?: AgentStatus;
    usage?: UsageInfo;
    agent_snapshot?: Record<string, unknown>;
}
interface TimelineEventUpdatableFields {
    timestamp?: string;
    type?: TimelineEventType;
    name?: string;
    status?: EventStatus;
    error?: Record<string, unknown>;
    input?: Record<string, unknown>;
    output?: Record<string, unknown>;
}

/**
 * Options for configuring the VoltAgentExporter.
 */
interface VoltAgentExporterOptions {
    /**
     * The base URL for the VoltAgent Edge Functions.
     */
    baseUrl: string;
    /**
     * The public API key for the project, used to identify the project
     * when sending telemetry data.
     */
    publicKey: string;
    /**
     * The client's secret key (obtained once during project creation)
     * used for authenticating requests to the telemetry Edge Functions.
     * This will be sent as 'clientSecretKey' in the request body.
     */
    secretKey: string;
    /**
     * Optional fetch implementation. Defaults to global fetch.
     * Useful for environments where global fetch might not be available or needs to be polyfilled (e.g., some Node.js versions).
     */
    fetch?: typeof fetch;
}

declare class VoltAgentExporter {
    private apiClient;
    readonly publicKey: string;
    constructor(options: VoltAgentExporterOptions);
    /**
     * Exports a single agent history entry.
     * @param historyEntryData - The agent history data to export.
     *                           This should conform to ExportAgentHistoryPayload.
     * @returns A promise that resolves with the response from the telemetry service,
     *          typically including the ID of the created history entry.
     */
    exportHistoryEntry(historyEntryData: ExportAgentHistoryPayload): Promise<{
        historyEntryId: string;
    }>;
    /**
     * Exports a single timeline event.
     * (Placeholder for when the 'export-timeline-event' Edge Function is ready)
     * @param timelineEventData - The timeline event data to export.
     *                            This should conform to ExportTimelineEventPayload.
     * @returns A promise that resolves with the response from the telemetry service.
     */
    exportTimelineEvent(timelineEventData: ExportTimelineEventPayload): Promise<{
        timelineEventId: string;
    }>;
    /**
     * Exports history steps for a specific agent history entry.
     * @param project_id - The project ID associated with the history entry.
     * @param history_id - The ID of the history entry to export steps for.
     * @param steps - The steps data to export.
     * @returns A promise that resolves with the response from the telemetry service.
     */
    exportHistorySteps(project_id: string, history_id: string, steps: HistoryStep[]): Promise<void>;
    /**
     * Updates specific fields of an agent history entry.
     * @param project_id - The project ID associated with the history entry.
     * @param history_id - The ID of the history entry to update.
     * @param updates - An object containing the fields to update.
     *                  Should conform to Partial<AgentHistoryUpdatableFields>.
     * @returns A promise that resolves with the response from the telemetry service.
     */
    updateHistoryEntry(project_id: string, history_id: string, updates: Partial<AgentHistoryUpdatableFields>): Promise<void>;
    /**
     * Updates specific fields of a timeline event.
     * @param history_id - The ID of the parent history entry.
     * @param event_id - The ID of the timeline event to update.
     * @param updates - An object containing the fields to update.
     * @returns A promise that resolves when the operation is complete.
     */
    updateTimelineEvent(history_id: string, event_id: string, updates: TimelineEventUpdatableFields): Promise<void>;
}

/**
 * Step information for history
 */
interface HistoryStep {
    type: "message" | "tool_call" | "tool_result" | "text";
    name?: string;
    content?: string;
    arguments?: Record<string, unknown>;
}
/**
 * Timeline event for detailed history
 */
interface TimelineEvent {
    /**
     * Unique identifier for the event
     */
    id?: string;
    /**
     * Timestamp when the event occurred
     */
    timestamp: string;
    /**
     * Name of the event (e.g., "generating", "tool_calling", "tool_result", etc.)
     * In the new format, "componentName:operationName" style (e.g.: "memory:getMessages")
     */
    name: string;
    /**
     * ID of the affected Flow node
     * Added with the new format
     */
    affectedNodeId?: string;
    /**
     * Optional additional data specific to the event type
     * In the new format: { status, input, output, updatedAt etc. }
     */
    data?: Record<string, unknown>;
    /**
     * Optional timestamp for when the event was last updated
     */
    updatedAt?: string;
    /**
     * Type of the event
     */
    type: "memory" | "tool" | "agent" | "retriever";
}
/**
 * Agent history entry
 */
interface AgentHistoryEntry {
    /**
     * Unique identifier
     */
    id: string;
    /**
     * Timestamp of the entry
     */
    timestamp: Date;
    /**
     * Original input to the agent
     */
    input: string | Record<string, unknown> | BaseMessage[];
    /**
     * Final output from the agent
     */
    output: string;
    /**
     * Status of the history entry
     */
    status: AgentStatus;
    /**
     * Steps taken during generation
     */
    steps?: HistoryStep[];
    /**
     * Usage information returned by the LLM
     */
    usage?: UsageInfo;
    /**
     * Timeline events for detailed agent state history
     */
    events?: TimelineEvent[];
    /**
     * Sequence number for the history entry
     */
    _sequenceNumber?: number;
}
/**
 * Manages agent interaction history
 */
declare class HistoryManager {
    /**
     * Maximum number of history entries to keep
     * Set to 0 for unlimited
     */
    private maxEntries;
    /**
     * Agent ID for emitting events
     */
    private agentId?;
    /**
     * Memory manager for storing history entries
     */
    private memoryManager;
    /**
     * Optional VoltAgentExporter for sending telemetry data.
     */
    private voltAgentExporter?;
    /**
     * Create a new history manager
     *
     * @param agentId - Agent ID for emitting events and for storage
     * @param memoryManager - Memory manager instance to use
     * @param maxEntries - Maximum number of history entries to keep (0 = unlimited)
     * @param voltAgentExporter - Optional exporter for telemetry
     */
    constructor(agentId: string, memoryManager: MemoryManager, maxEntries?: number, voltAgentExporter?: VoltAgentExporter);
    /**
     * Set the agent ID for this history manager
     */
    setAgentId(agentId: string): void;
    /**
     * Sets the VoltAgentExporter for this history manager instance.
     * This allows the exporter to be set after the HistoryManager is created.
     */
    setExporter(exporter: VoltAgentExporter): void;
    /**
     * Checks if a VoltAgentExporter is configured for this history manager.
     * @returns True if an exporter is configured, false otherwise.
     */
    isExporterConfigured(): boolean;
    /**
     * Add a new history entry
     *
     * @param input - Input to the agent
     * @param output - Output from the agent
     * @param status - Status of the entry
     * @param steps - Steps taken during generation
     * @param options - Additional options for the entry
     * @param agentSnapshot - Optional agent snapshot for telemetry
     * @param userId - Optional userId for telemetry
     * @param conversationId - Optional conversationId for telemetry
     * @returns The new history entry
     */
    addEntry(input: string | Record<string, unknown> | BaseMessage[], output: string, status: AgentStatus, steps?: HistoryStep[], options?: Partial<Omit<AgentHistoryEntry, "id" | "timestamp" | "input" | "output" | "status" | "steps">>, agentSnapshot?: Record<string, unknown>, userId?: string, conversationId?: string): Promise<AgentHistoryEntry>;
    /**
     * Add a timeline event to an existing history entry
     *
     * @param entryId - ID of the entry to update
     * @param event - Timeline event to add
     * @returns The updated entry or undefined if not found
     */
    addEventToEntry(entryId: string, event: TimelineEvent): Promise<AgentHistoryEntry | undefined>;
    /**
     * Add steps to an existing history entry
     *
     * @param entryId - ID of the entry to update
     * @param steps - Steps to add
     * @returns The updated entry or undefined if not found
     */
    addStepsToEntry(entryId: string, steps: StepWithContent[]): Promise<AgentHistoryEntry | undefined>;
    /**
     * Get history entry by ID
     *
     * @param id - ID of the entry to find
     * @returns The history entry or undefined if not found
     */
    getEntryById(id: string): Promise<AgentHistoryEntry | undefined>;
    /**
     * Get all history entries
     *
     * @returns Array of history entries
     */
    getEntries(): Promise<AgentHistoryEntry[]>;
    /**
     * Get the latest history entry
     *
     * @returns The latest history entry or undefined if no entries
     */
    getLatestEntry(): Promise<AgentHistoryEntry | undefined>;
    /**
     * Clear all history entries
     */
    clear(): Promise<void>;
    /**
     * Update an existing history entry
     *
     * @param id - ID of the entry to update
     * @param updates - Partial entry with fields to update
     * @returns The updated entry or undefined if not found
     */
    updateEntry(id: string, updates: Partial<Omit<AgentHistoryEntry, "id" | "timestamp"> & {
        agent_snapshot?: Record<string, unknown>;
    }>): Promise<AgentHistoryEntry | undefined>;
    /**
     * Get a tracked event by ID
     *
     * @param historyId - ID of the history entry
     * @param eventId - ID of the event or _trackedEventId
     * @returns The tracked event or undefined if not found
     */
    getTrackedEvent(historyId: string, eventId: string): Promise<TimelineEvent | undefined>;
    /**
     * Update a tracked event by ID
     *
     * @param historyId - ID of the history entry
     * @param eventId - ID of the event or _trackedEventId
     * @param updates - Updates to apply to the event
     * @returns The updated history entry or undefined if not found
     */
    updateTrackedEvent(historyId: string, eventId: string, updates: {
        status?: AgentStatus;
        data?: Record<string, unknown>;
    }): Promise<AgentHistoryEntry | undefined>;
}

/**
 * Provider options type for LLM configurations
 */
type ProviderOptions = {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    seed?: number;
    stopSequences?: string[];
    extraOptions?: Record<string, unknown>;
    onStepFinish?: (step: StepWithContent) => Promise<void>;
    onFinish?: (result: unknown) => Promise<void>;
    onError?: (error: unknown) => Promise<void>;
    toolExecutionContext?: ToolExecutionContext;
    [key: string]: unknown;
};
/**
 * Agent configuration options
 */
type AgentOptions = {
    /**
     * Unique identifier for the agent
     * If not provided, a UUID will be generated
     */
    id?: string;
    /**
     * Agent name
     */
    name: string;
    /**
     * Memory storage for the agent (optional)
     * Set to false to explicitly disable memory
     */
    memory?: Memory | false;
    /**
     * Memory options for the agent
     */
    memoryOptions?: MemoryOptions;
    /**
     * Tools and/or Toolkits that the agent can use
     */
    tools?: (Tool<any> | Toolkit)[];
    /**
     * Sub-agents that this agent can delegate tasks to
     */
    subAgents?: any[];
    /**
     * Optional user-defined context to be passed around
     */
    userContext?: Map<string | symbol, unknown>;
    /**
     * Telemetry exporter for the agent
     * Used to send telemetry data to an external service
     */
    telemetryExporter?: VoltAgentExporter;
} & ({
    /**
     * @deprecated Use `instructions` instead.
     * Agent description (deprecated, use instructions)
     */
    description: string;
    /**
     * Agent instructions. This is the preferred field.
     */
    instructions?: string;
} | {
    /**
     * @deprecated Use `instructions` instead.
     * Agent description (deprecated, use instructions)
     */
    description?: undefined;
    /**
     * Agent instructions. This is the preferred field.
     * Required if description is not provided.
     */
    instructions: string;
});
/**
 * Provider instance type helper
 */
type ProviderInstance<T> = T extends {
    llm: infer P;
} ? P : never;
/**
 * Model type helper
 */
type ModelType<T> = T extends {
    llm: LLMProvider<any>;
} ? Parameters<T["llm"]["generateText"]>[0]["model"] : never;
/**
 * Infer generate text response type
 */
type InferGenerateTextResponse$1<T extends {
    llm: LLMProvider<any>;
}> = Awaited<ReturnType<T["llm"]["generateText"]>>;
/**
 * Infer stream text response type
 */
type InferStreamTextResponse<T extends {
    llm: LLMProvider<any>;
}> = Awaited<ReturnType<T["llm"]["streamText"]>>;
/**
 * Infer generate object response type
 */
type InferGenerateObjectResponse$1<T extends {
    llm: LLMProvider<any>;
}> = Awaited<ReturnType<T["llm"]["generateObject"]>>;
/**
 * Infer stream object response type
 */
type InferStreamObjectResponse<T extends {
    llm: LLMProvider<any>;
}> = Awaited<ReturnType<T["llm"]["streamObject"]>>;
/**
 * Common generate options - internal version that includes historyEntryId
 * Not exposed directly to users
 */
interface CommonGenerateOptions {
    provider?: ProviderOptions;
    conversationId?: string;
    userId?: string;
    contextLimit?: number;
    tools?: BaseTool[];
    signal?: AbortSignal;
    historyEntryId?: string;
    operationContext?: OperationContext;
    userContext?: Map<string | symbol, unknown>;
}
/**
 * Public-facing generate options for external users
 * Omits internal implementation details like historyEntryId and operationContext
 */
type PublicGenerateOptions = Omit<CommonGenerateOptions, "historyEntryId" | "operationContext">;
/**
 * Agent status information
 */
type AgentStatus = "idle" | "working" | "tool_calling" | "error" | "completed";
/**
 * Tool call definition
 */
type ToolCall$1 = {
    id: string;
    type: "function";
    function: {
        name: string;
        arguments: string;
    };
};
/**
 * Model tool call format
 */
type ModelToolCall = {
    type: "tool-call";
    toolCallId: string;
    toolName: string;
    args: Record<string, unknown>;
};
/**
 * Agent response format
 */
type AgentResponse = {
    /**
     * Response content
     */
    content: string;
    /**
     * Tool calls made by the model (if any)
     */
    toolCalls?: ToolCall$1[];
    /**
     * Additional metadata
     */
    metadata: {
        agentId: string;
        agentName: string;
        [key: string]: unknown;
    };
};
/**
 * Agent handoff options
 */
type AgentHandoffOptions = {
    /**
     * The task description to be handed off
     */
    task: string;
    /**
     * The target agent to hand off to
     */
    targetAgent: any;
    /**
     * The source agent that is handing off the task
     * Used for hooks and tracking the chain of delegation
     */
    sourceAgent?: any;
    /**
     * Additional context to provide to the target agent
     */
    context?: Record<string, unknown>;
    /**
     * The conversation ID to use for the handoff
     * If not provided, a new conversation ID will be generated
     */
    conversationId?: string;
    /**
     * The user ID to use for the handoff
     * This will be passed to the target agent's generateText method
     */
    userId?: string;
    /**
     * Shared context messages to pass to the target agent
     * These messages provide conversation history context
     */
    sharedContext?: BaseMessage[];
    /**
     * Parent agent ID
     */
    parentAgentId?: string;
    /**
     * Parent history entry ID
     */
    parentHistoryEntryId?: string;
    /**
     * Optional user-defined context to be passed from the supervisor agent
     */
    userContext?: Map<string | symbol, unknown>;
};
/**
 * Result of a handoff to another agent
 */
interface AgentHandoffResult {
    /**
     * Result text from the agent
     */
    result: string;
    /**
     * Conversation ID used for the interaction
     */
    conversationId: string;
    /**
     * Messages exchanged during the handoff
     */
    messages: BaseMessage[];
    /**
     * Status of the handoff operation
     */
    status?: "success" | "error";
    /**
     * Error information if the handoff failed
     */
    error?: Error | string;
}
/**
 * Context for a specific agent operation (e.g., one generateText call)
 */
type OperationContext = {
    /** Unique identifier for the operation (maps to historyEntryId) */
    readonly operationId: string;
    /** User-managed context map for this specific operation */
    readonly userContext: Map<string | symbol, any>;
    /** The history entry associated with this operation */
    historyEntry: AgentHistoryEntry;
    /** Map to store tool event updaters using tool call ID as key */
    eventUpdaters: Map<string, EventUpdater>;
    /** Whether this operation is still active */
    isActive: boolean;
    /** Parent agent ID if part of a delegation chain */
    parentAgentId?: string;
    /** Parent history entry ID if part of a delegation chain */
    parentHistoryEntryId?: string;
    /** The root OpenTelemetry span for this operation */
    otelSpan?: Span;
    /** Map to store active OpenTelemetry spans for tool calls within this operation */
    toolSpans?: Map<string, Span>;
};
/**
 * Tool execution context passed to tool.execute method
 * Includes operation-specific context and necessary identifiers
 * Extends base ToolExecuteOptions.
 */
type ToolExecutionContext = ToolExecuteOptions & {
    /** ID of the agent executing the tool */
    agentId: string;
    /** History ID associated with the current operation */
    historyEntryId: string;
};
/**
 * Specific information related to a tool execution error.
 */
interface ToolErrorInfo {
    /** The unique identifier of the tool call. */
    toolCallId: string;
    /** The name of the tool that was executed. */
    toolName: string;
    /** The original error thrown directly by the tool during execution (if available). */
    toolExecutionError?: unknown;
    /** The arguments passed to the tool when the error occurred (for debugging). */
    toolArguments?: unknown;
}
/**
 * Standardized error structure for Voltagent agent operations.
 * Providers should wrap their specific errors in this structure before
 * passing them to onError callbacks.
 */
interface VoltAgentError {
    /** A clear, human-readable error message. This could be a general message or derived from toolError info. */
    message: string;
    /** The original error object thrown by the provider or underlying system (if available). */
    originalError?: unknown;
    /** Optional error code or identifier from the provider. */
    code?: string | number;
    /** Additional metadata related to the error (e.g., retry info, request ID). */
    metadata?: Record<string, any>;
    /** Information about the step or stage where the error occurred (optional, e.g., 'llm_request', 'tool_execution', 'response_parsing'). */
    stage?: string;
    /** If the error occurred during tool execution, this field contains the relevant details. Otherwise, it's undefined. */
    toolError?: ToolErrorInfo;
}
/**
 * Type for onError callbacks in streaming operations.
 * Providers must pass an error conforming to the VoltAgentError structure.
 */
type StreamOnErrorCallback = (error: VoltAgentError) => Promise<void> | void;
/**
 * Standardized object structure passed to the onFinish callback
 * when streamText completes successfully.
 */
interface StreamTextFinishResult {
    /** The final, consolidated text output from the stream. */
    text: string;
    /** Token usage information (if available). */
    usage?: UsageInfo;
    /** The reason the stream finished (if available, e.g., 'stop', 'length', 'tool-calls'). */
    finishReason?: string;
    /** The original completion response object from the provider (if available). */
    providerResponse?: unknown;
    /** Any warnings generated during the completion (if available). */
    warnings?: unknown[];
}
/**
 * Type for the onFinish callback function for streamText.
 */
type StreamTextOnFinishCallback = (result: StreamTextFinishResult) => Promise<void> | void;
/**
 * Standardized object structure passed to the onFinish callback
 * when streamObject completes successfully.
 * @template TObject The expected type of the fully formed object.
 */
interface StreamObjectFinishResult<TObject> {
    /** The final, fully formed object from the stream. */
    object: TObject;
    /** Token usage information (if available). */
    usage?: UsageInfo;
    /** The original completion response object from the provider (if available). */
    providerResponse?: unknown;
    /** Any warnings generated during the completion (if available). */
    warnings?: unknown[];
    /** The reason the stream finished (if available). Although less common for object streams. */
    finishReason?: string;
}
/**
 * Type for the onFinish callback function for streamObject.
 * @template TObject The expected type of the fully formed object.
 */
type StreamObjectOnFinishCallback<TObject> = (result: StreamObjectFinishResult<TObject>) => Promise<void> | void;
/**
 * Standardized success result structure for generateText.
 */
interface StandardizedTextResult {
    /** The generated text. */
    text: string;
    /** Token usage information (if available). */
    usage?: UsageInfo;
    /** Original provider response (if needed). */
    providerResponse?: unknown;
    /** Finish reason (if available from provider). */
    finishReason?: string;
    /** Warnings (if available from provider). */
    warnings?: unknown[];
}
/**
 * Standardized success result structure for generateObject.
 * @template TObject The expected type of the generated object.
 */
interface StandardizedObjectResult<TObject> {
    /** The generated object. */
    object: TObject;
    /** Token usage information (if available). */
    usage?: UsageInfo;
    /** Original provider response (if needed). */
    providerResponse?: unknown;
    /** Finish reason (if available from provider). */
    finishReason?: string;
    /** Warnings (if available from provider). */
    warnings?: unknown[];
}
/**
 * Unified output type for the onEnd hook, representing the successful result
 * of any core agent operation. Use 'type guarding' or check specific fields
 * within the hook implementation to determine the concrete type.
 * Object types are generalized to 'unknown' here for the union.
 */
type AgentOperationOutput = StandardizedTextResult | StreamTextFinishResult | StandardizedObjectResult<unknown> | StreamObjectFinishResult<unknown>;

/**
 * Token usage information
 */
type UsageInfo = {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
};
/**
 * Base provider response type
 */
type ProviderResponse<TOriginalResponse> = {
    /**
     * Original response from the provider
     */
    provider: TOriginalResponse;
};
/**
 * Response type for text generation operations
 */
type ProviderTextResponse<TOriginalResponse> = {
    /**
     * Original response from the provider
     */
    provider: TOriginalResponse;
    /**
     * Text response content
     */
    text: string;
    /**
     * Token usage information
     */
    usage?: UsageInfo;
    /**
     * Tool calls in the response (if applicable)
     */
    toolCalls?: any[];
    /**
     * Tool results in the response (if applicable)
     */
    toolResults?: any[];
    /**
     * Finish reason (if applicable)
     */
    finishReason?: string;
};
/**
 * Response type for text streaming operations
 */
type ProviderTextStreamResponse<TOriginalResponse> = {
    /**
     * Original response from the provider
     */
    provider: TOriginalResponse;
    /**
     * Text stream for consuming the response
     */
    textStream: ReadableStream<string>;
};
/**
 * Response type for object generation operations
 */
type ProviderObjectResponse<TOriginalResponse, TObject> = {
    /**
     * Original response from the provider
     */
    provider: TOriginalResponse;
    /**
     * Generated object
     */
    object: TObject;
    /**
     * Token usage information
     */
    usage?: UsageInfo;
    /**
     * Finish reason (if applicable)
     */
    finishReason?: string;
};
/**
 * Response type for object streaming operations
 */
type ProviderObjectStreamResponse<TOriginalResponse, TObject> = {
    /**
     * Original response from the provider
     */
    provider: TOriginalResponse;
    /**
     * Object stream for consuming partial objects
     */
    objectStream: ReadableStream<Partial<TObject>>;
};
/**
 * Data content type for binary data
 */
type DataContent = string | Uint8Array | ArrayBuffer | Buffer;
/**
 * Text part of a message
 */
type TextPart = {
    type: "text";
    /**
     * The text content
     */
    text: string;
};
/**
 * Image part of a message
 */
type ImagePart = {
    type: "image";
    /**
     * Image data. Can either be:
     * - data: a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer
     * - URL: a URL that points to the image
     */
    image: DataContent | URL;
    /**
     * Optional mime type of the image
     */
    mimeType?: string;
};
/**
 * File part of a message
 */
type FilePart = {
    type: "file";
    /**
     * File data. Can either be:
     * - data: a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer
     * - URL: a URL that points to the file
     */
    data: DataContent | URL;
    /**
     * Optional filename of the file
     */
    filename?: string;
    /**
     * Mime type of the file
     */
    mimeType: string;
};
/**
 * Message content can be either a string or an array of parts
 */
type MessageContent = string | Array<TextPart | ImagePart | FilePart>;
/**
 * Message role types
 */
type MessageRole = "user" | "assistant" | "system" | "tool";
/**
 * Base message type
 */
type BaseMessage = {
    role: MessageRole;
    content: MessageContent;
};
type ToolSchema = z.ZodType;
type ToolExecuteOptions = {
    /**
     * Optional AbortSignal to abort the execution
     */
    signal?: AbortSignal;
    /**
     * The operation context associated with the agent invocation triggering this tool execution.
     * Provides access to operation-specific state like userContext.
     */
    operationContext?: OperationContext;
    /**
     * Additional options can be added in the future
     */
    [key: string]: any;
};
type BaseTool = Tool<any>;
type BaseToolCall = {
    name: string;
    arguments: Record<string, any>;
};
type ProviderParams<T> = T extends {
    doGenerate: (options: infer P) => any;
} ? P extends {
    messages: any;
    model: any;
} ? Omit<P, "messages" | "model" | "tools" | "maxSteps" | "schema"> : Record<string, never> : Record<string, never>;
type BaseLLMOptions<TModel, TProvider> = {
    messages: BaseMessage[];
    model: TModel;
    provider?: ProviderParams<TProvider>;
};
interface StepWithContent {
    id: string;
    type: "text" | "tool_call" | "tool_result";
    content: string;
    role: MessageRole;
    name?: string;
    arguments?: Record<string, any>;
    result?: any;
    usage?: UsageInfo;
}
type StepFinishCallback = (step: StepWithContent) => void | Promise<void>;
type StepChunkCallback = (chunk: any) => void | Promise<void>;
interface GenerateTextOptions<TModel> {
    messages: BaseMessage[];
    model: TModel;
    tools?: BaseTool[];
    maxSteps?: number;
    provider?: ProviderOptions;
    onStepFinish?: StepFinishCallback;
    signal?: AbortSignal;
    toolExecutionContext?: ToolExecutionContext;
}
interface StreamTextOptions<TModel> {
    messages: BaseMessage[];
    model: TModel;
    tools?: BaseTool[];
    maxSteps?: number;
    provider?: ProviderOptions;
    onStepFinish?: StepFinishCallback;
    onChunk?: StepChunkCallback;
    onFinish?: StreamTextOnFinishCallback;
    onError?: StreamOnErrorCallback;
    signal?: AbortSignal;
    toolExecutionContext?: ToolExecutionContext;
}
interface GenerateObjectOptions<TModel, TSchema extends z.ZodType> {
    messages: BaseMessage[];
    model: TModel;
    schema: TSchema;
    provider?: ProviderOptions;
    onStepFinish?: StepFinishCallback;
    signal?: AbortSignal;
    toolExecutionContext?: ToolExecutionContext;
}
interface StreamObjectOptions<TModel, TSchema extends z.ZodType> {
    messages: BaseMessage[];
    model: TModel;
    schema: TSchema;
    provider?: ProviderOptions;
    onStepFinish?: StepFinishCallback;
    onFinish?: StreamObjectOnFinishCallback<z.infer<TSchema>>;
    onError?: StreamOnErrorCallback;
    signal?: AbortSignal;
    toolExecutionContext?: ToolExecutionContext;
}
type InferStreamResponse<T> = T extends {
    streamText: (...args: any[]) => Promise<infer R>;
} ? R : unknown;
type InferMessage<T> = T extends {
    toMessage: (message: BaseMessage) => infer R;
} ? R : unknown;
type InferTool<T> = T extends {
    toTool?: (tool: BaseTool) => infer R;
} ? R : unknown;
type InferModel<T> = T extends {
    model: infer R;
} ? R : unknown;
type InferGenerateTextResponse<T> = T extends {
    generateText: (...args: any[]) => Promise<infer R>;
} ? R : unknown;
type InferGenerateObjectResponse<T> = T extends {
    generateObject: (...args: any[]) => Promise<infer R>;
} ? R : unknown;
type InferProviderParams<T> = T extends {
    generateText: (options: infer P) => any;
} ? P extends {
    messages: any;
    model: any;
    tools?: any;
    maxSteps?: any;
    schema?: any;
} ? Omit<P, "messages" | "model" | "tools" | "maxSteps" | "schema"> : Record<string, never> : Record<string, never>;
type LLMProvider<TProvider> = {
    /**
     * Generates a text response based on the provided options.
     * Implementers should catch underlying SDK/API errors and throw a VoltAgentError.
     * @throws {VoltAgentError} If an error occurs during generation.
     */
    generateText(options: GenerateTextOptions<InferModel<TProvider>>): Promise<ProviderTextResponse<InferGenerateTextResponse<TProvider>>>;
    streamText(options: StreamTextOptions<InferModel<TProvider>>): Promise<ProviderTextStreamResponse<InferStreamResponse<TProvider>>>;
    /**
     * Generates a structured object response based on the provided options and schema.
     * Implementers should catch underlying SDK/API errors and throw a VoltAgentError.
     * @throws {VoltAgentError} If an error occurs during generation.
     */
    generateObject<TSchema extends z.ZodType>(options: GenerateObjectOptions<InferModel<TProvider>, TSchema>): Promise<ProviderObjectResponse<InferGenerateObjectResponse<TProvider>, z.infer<TSchema>>>;
    streamObject<TSchema extends z.ZodType>(options: StreamObjectOptions<InferModel<TProvider>, TSchema>): Promise<ProviderObjectStreamResponse<InferStreamResponse<TProvider>, z.infer<TSchema>>>;
    toMessage(message: BaseMessage): InferMessage<TProvider>;
    toTool?: (tool: BaseTool) => InferTool<TProvider>;
    /**
     * Returns a string representation of the model identifier.
     * @param model The model object/identifier specific to this provider.
     * @returns The string name of the model.
     */
    getModelIdentifier(model: InferModel<TProvider>): string;
};

/**
 * Memory options
 */
type MemoryOptions = {
    /**
     * Maximum number of messages to store in the database
     * @default 100
     */
    storageLimit?: number;
};
/**
 * Options for filtering messages when retrieving from memory
 */
type MessageFilterOptions = {
    /**
     * User identifier
     */
    userId?: string;
    /**
     * Conversation identifier
     */
    conversationId?: string;
    /**
     * Maximum number of messages to retrieve
     */
    limit?: number;
    /**
     * Only retrieve messages before this timestamp
     */
    before?: number;
    /**
     * Only retrieve messages after this timestamp
     */
    after?: number;
    /**
     * Only retrieve messages with this role
     */
    role?: BaseMessage["role"];
};
/**
 * Conversation type
 */
type Conversation = {
    id: string;
    resourceId: string;
    title: string;
    metadata: Record<string, unknown>;
    createdAt: string;
    updatedAt: string;
};
/**
 * Input type for creating a conversation
 */
type CreateConversationInput = {
    id: string;
    resourceId: string;
    title: string;
    metadata: Record<string, unknown>;
};
/**
 * Memory interface for storing and retrieving messages
 */
type Memory = {
    /**
     * Add a message to memory
     */
    addMessage(message: BaseMessage, userId: string, conversationId?: string): Promise<void>;
    /**
     * Get messages from memory
     */
    getMessages(options: MessageFilterOptions): Promise<BaseMessage[]>;
    /**
     * Clear messages from memory
     */
    clearMessages(options: {
        userId: string;
        conversationId?: string;
    }): Promise<void>;
    /**
     * Create a new conversation
     */
    createConversation(conversation: CreateConversationInput): Promise<Conversation>;
    /**
     * Get a conversation by ID
     */
    getConversation(id: string): Promise<Conversation | null>;
    /**
     * Get conversations for a resource
     */
    getConversations(resourceId: string): Promise<Conversation[]>;
    /**
     * Update a conversation
     */
    updateConversation(id: string, updates: Partial<Omit<Conversation, "id" | "createdAt" | "updatedAt">>): Promise<Conversation>;
    /**
     * Delete a conversation
     */
    deleteConversation(id: string): Promise<void>;
    /**
     * Add or update a history entry
     * @param key Entry ID
     * @param value Entry data
     * @param agentId Agent ID for filtering
     */
    addHistoryEntry(key: string, value: any, agentId: string): Promise<void>;
    /**
     * Update an existing history entry
     * @param key Entry ID
     * @param value Updated entry data
     * @param agentId Agent ID for filtering
     */
    updateHistoryEntry(key: string, value: any, agentId: string): Promise<void>;
    /**
     * Add a history event
     * @param key Event ID
     * @param value Event data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    addHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history event
     * @param key Event ID
     * @param value Updated event data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    updateHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Add a history step
     * @param key Step ID
     * @param value Step data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    addHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history step
     * @param key Step ID
     * @param value Updated step data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    updateHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Get a history entry by ID
     * @param key Entry ID
     * @returns The history entry or undefined if not found
     */
    getHistoryEntry(key: string): Promise<any | undefined>;
    /**
     * Get a history event by ID
     * @param key Event ID
     * @returns The history event or undefined if not found
     */
    getHistoryEvent(key: string): Promise<any | undefined>;
    /**
     * Get a history step by ID
     * @param key Step ID
     * @returns The history step or undefined if not found
     */
    getHistoryStep(key: string): Promise<any | undefined>;
    /**
     * Get all history entries for an agent
     * @param agentId Agent ID
     * @returns Array of all history entries for the agent
     */
    getAllHistoryEntriesByAgent(agentId: string): Promise<any[]>;
};
/**
 * Memory-specific message type
 */
type MemoryMessage = BaseMessage & {
    id: string;
    type: "text" | "tool-call" | "tool-result";
    createdAt: string;
};

/**
 * Options for configuring the InMemoryStorage
 */
interface InMemoryStorageOptions extends MemoryOptions {
    /**
     * Whether to enable debug logging
     * @default false
     */
    debug?: boolean;
}
/**
 * A simple in-memory implementation of the Memory interface
 * Stores messages in memory, organized by user and conversation
 */
declare class InMemoryStorage implements Memory {
    private storage;
    private conversations;
    private historyEntries;
    private agentHistory;
    private options;
    /**
     * Create a new in-memory storage
     * @param options Configuration options
     */
    constructor(options?: InMemoryStorageOptions);
    /**
     * Get a history entry by ID
     */
    getHistoryEntry(key: string): Promise<any | undefined>;
    /**
     * Get a history event (not needed for in-memory, but required by interface)
     */
    getHistoryEvent(key: string): Promise<any | undefined>;
    /**
     * Get a history step (not needed for in-memory, but required by interface)
     */
    getHistoryStep(key: string): Promise<any | undefined>;
    /**
     * Add a history entry
     */
    addHistoryEntry(key: string, value: any, agentId: string): Promise<void>;
    /**
     * Update a history entry
     */
    updateHistoryEntry(key: string, value: any, agentId?: string): Promise<void>;
    /**
     * Add a history event
     */
    addHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history event
     */
    updateHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Add a history step
     */
    addHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history step
     */
    updateHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Get all history entries for an agent
     */
    getAllHistoryEntriesByAgent(agentId: string): Promise<any[]>;
    /**
     * Log a debug message if debug is enabled
     * @param message Message to log
     * @param data Additional data to log
     */
    private debug;
    /**
     * Get messages with filtering options
     * @param options Filtering options
     * @returns Filtered messages
     */
    getMessages(options?: MessageFilterOptions): Promise<MemoryMessage[]>;
    /**
     * Add a message to the conversation history
     * @param message Message to add
     * @param userId User identifier (optional, defaults to "default")
     * @param conversationId Conversation identifier (optional, defaults to "default")
     */
    addMessage(message: MemoryMessage, userId?: string, conversationId?: string): Promise<void>;
    /**
     * Clear all messages for a user and optionally a specific conversation
     * @param options Options specifying which messages to clear
     */
    clearMessages(options: {
        userId: string;
        conversationId?: string;
    }): Promise<void>;
    /**
     * Create a new conversation
     * @param conversation Conversation to create
     * @returns Created conversation
     */
    createConversation(conversation: CreateConversationInput): Promise<Conversation>;
    /**
     * Get a conversation by ID
     * @param id Conversation ID
     * @returns Conversation or null if not found
     */
    getConversation(id: string): Promise<Conversation | null>;
    /**
     * Get all conversations for a resource
     * @param resourceId Resource ID
     * @returns Array of conversations
     */
    getConversations(resourceId: string): Promise<Conversation[]>;
    /**
     * Update a conversation
     * @param id Conversation ID
     * @param updates Updates to apply
     * @returns Updated conversation
     */
    updateConversation(id: string, updates: Partial<Omit<Conversation, "id" | "createdAt" | "updatedAt">>): Promise<Conversation>;
    /**
     * Delete a conversation by ID
     * @param id Conversation ID
     */
    deleteConversation(id: string): Promise<void>;
}

/**
 * Options for configuring the LibSQLStorage
 */
interface LibSQLStorageOptions extends MemoryOptions {
    /**
     * LibSQL connection URL
     * Can be either a remote Turso URL or a local file path
     * @example "libsql://your-database.turso.io" for remote Turso
     * @example "file:memory.db" for local SQLite in current directory
     * @example "file:.voltagent/memory.db" for local SQLite in .voltagent folder
     */
    url: string;
    /**
     * Auth token for LibSQL/Turso
     * Not needed for local SQLite
     */
    authToken?: string;
    /**
     * Prefix for table names
     * @default "voltagent_memory"
     */
    tablePrefix?: string;
    /**
     * Whether to enable debug logging
     * @default false
     */
    debug?: boolean;
    /**
     * Storage limit for the LibSQLStorage
     * @default 100
     */
    storageLimit?: number;
}
/**
 * A LibSQL storage implementation of the Memory interface
 * Uses libsql/Turso to store and retrieve conversation history
 *
 * This implementation automatically handles both:
 * - Remote Turso databases (with libsql:// URLs)
 * - Local SQLite databases (with file: URLs)
 */
declare class LibSQLStorage implements Memory {
    private client;
    private options;
    private initialized;
    /**
     * Create a new LibSQL storage
     * @param options Configuration options
     */
    constructor(options: LibSQLStorageOptions);
    /**
     * Normalize the URL for SQLite database
     * - Ensures local files exist in the correct directory
     * - Creates the .voltagent directory if needed for default storage
     */
    private normalizeUrl;
    /**
     * Log a debug message if debug is enabled
     * @param message Message to log
     * @param data Additional data to log
     */
    private debug;
    /**
     * Initialize the database tables
     * @returns Promise that resolves when initialization is complete
     */
    private initializeDatabase;
    /**
     * Generate a unique ID for a message
     * @returns Unique ID
     */
    private generateId;
    /**
     * Get messages with filtering options
     * @param options Filtering options
     * @returns Filtered messages
     */
    getMessages(options?: MessageFilterOptions): Promise<MemoryMessage[]>;
    /**
     * Add a message to the conversation history
     * @param message Message to add
     * @param userId User identifier (optional, defaults to "default")
     * @param conversationId Conversation identifier (optional, defaults to "default")
     */
    addMessage(message: MemoryMessage, userId?: string, conversationId?: string): Promise<void>;
    /**
     * Clear messages from memory
     */
    clearMessages(options: {
        userId: string;
        conversationId?: string;
    }): Promise<void>;
    /**
     * Close the database connection
     */
    close(): void;
    /**
     * Add or update a history entry
     * @param key Entry ID
     * @param value Entry data
     * @param agentId Agent ID for filtering
     */
    addHistoryEntry(key: string, value: any, agentId: string): Promise<void>;
    /**
     * Update an existing history entry
     * @param key Entry ID
     * @param value Updated entry data
     * @param agentId Agent ID for filtering
     */
    updateHistoryEntry(key: string, value: any, agentId: string): Promise<void>;
    /**
     * Add a history event
     * @param key Event ID
     * @param value Event data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    addHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history event
     * @param key Event ID
     * @param value Updated event data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    updateHistoryEvent(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Add a history step
     * @param key Step ID
     * @param value Step data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    addHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Update a history step
     * @param key Step ID
     * @param value Updated step data
     * @param historyId Related history entry ID
     * @param agentId Agent ID for filtering
     */
    updateHistoryStep(key: string, value: any, historyId: string, agentId: string): Promise<void>;
    /**
     * Get a history entry by ID
     * @param key Entry ID
     * @returns The history entry or undefined if not found
     */
    getHistoryEntry(key: string): Promise<any | undefined>;
    /**
     * Get a history event by ID
     * @param key Event ID
     * @returns The history event or undefined if not found
     */
    getHistoryEvent(key: string): Promise<any | undefined>;
    /**
     * Get a history step by ID
     * @param key Step ID
     * @returns The history step or undefined if not found
     */
    getHistoryStep(key: string): Promise<any | undefined>;
    createConversation(conversation: CreateConversationInput): Promise<Conversation>;
    getConversation(id: string): Promise<Conversation | null>;
    getConversations(resourceId: string): Promise<Conversation[]>;
    updateConversation(id: string, updates: Partial<Omit<Conversation, "id" | "createdAt" | "updatedAt">>): Promise<Conversation>;
    deleteConversation(id: string): Promise<void>;
    /**
     * Get all history entries for an agent
     * @param agentId Agent ID
     * @returns Array of all history entries for the agent
     */
    getAllHistoryEntriesByAgent(agentId: string): Promise<any[]>;
}

/**
 * Manager class to handle all memory-related operations
 */
declare class MemoryManager {
    /**
     * The memory storage instance
     */
    private memory;
    /**
     * Memory configuration options
     */
    private options;
    /**
     * The ID of the resource (agent) that owns this memory manager
     */
    private resourceId;
    /**
     * Creates a new MemoryManager
     */
    constructor(resourceId: string, memory?: Memory | false, options?: MemoryOptions);
    /**
     * Create a tracked event for a memory operation
     *
     * @param context - Operation context with history entry info
     * @param operationName - Name of the memory operation
     * @param status - Current status of the memory operation
     * @param initialData - Initial data for the event
     * @returns An event updater function
     */
    private createMemoryEvent;
    /**
     * Save a message to memory
     */
    saveMessage(context: OperationContext, message: BaseMessage, userId?: string, conversationId?: string, type?: "text" | "tool-call" | "tool-result"): Promise<void>;
    /**
     * Get messages from memory
     */
    getMessages(context: OperationContext, userId?: string, conversationId?: string, limit?: number): Promise<BaseMessage[]>;
    /**
     * Create a step finish handler to save messages during generation
     */
    createStepFinishHandler(context: OperationContext, userId?: string, conversationId?: string): (() => void) | ((step: StepWithContent) => Promise<void>);
    /**
     * Prepare conversation context for message generation
     */
    prepareConversationContext(context: OperationContext, input: string | BaseMessage[], userId?: string, conversationIdParam?: string, contextLimit?: number): Promise<{
        messages: BaseMessage[];
        conversationId: string;
    }>;
    /**
     * Get the memory instance
     */
    getMemory(): Memory | undefined;
    /**
     * Get the memory options
     */
    getOptions(): MemoryOptions;
    /**
     * Get memory state for display in UI
     */
    getMemoryState(): Record<string, any>;
    /**
     * Store a history entry in memory storage
     *
     * @param agentId - The ID of the agent
     * @param entry - The history entry to store
     * @returns A promise that resolves when the entry is stored
     */
    storeHistoryEntry(agentId: string, entry: any): Promise<void>;
    /**
     * Get a history entry by ID with related events and steps
     *
     * @param agentId - The ID of the agent
     * @param entryId - The ID of the entry to retrieve
     * @returns A promise that resolves to the entry or undefined
     */
    getHistoryEntryById(agentId: string, entryId: string): Promise<any | undefined>;
    /**
     * Get all history entries for an agent
     *
     * @param agentId - The ID of the agent
     * @returns A promise that resolves to an array of entries
     */
    getAllHistoryEntries(agentId: string): Promise<any[]>;
    /**
     * Update a history entry
     *
     * @param agentId - The ID of the agent
     * @param entryId - The ID of the entry to update
     * @param updates - Partial entry with fields to update
     * @returns A promise that resolves to the updated entry or undefined
     */
    updateHistoryEntry(agentId: string, entryId: string, updates: any): Promise<any | undefined>;
    /**
     * Update an existing event in a history entry
     *
     * @param agentId - The ID of the agent
     * @param entryId - The ID of the history entry
     * @param eventId - The ID of the event to update
     * @param event - Updated event data
     * @returns A promise that resolves when the update is complete
     */
    updateEventInHistoryEntry(agentId: string, entryId: string, eventId: string, event: any): Promise<any | undefined>;
    /**
     * Add steps to a history entry
     *
     * @param agentId - The ID of the agent
     * @param entryId - The ID of the entry to update
     * @param steps - Steps to add
     * @returns A promise that resolves to the updated entry or undefined
     */
    addStepsToHistoryEntry(agentId: string, entryId: string, steps: any[]): Promise<any | undefined>;
    /**
     * Add an event to a history entry
     *
     * @param agentId - The ID of the agent
     * @param entryId - The ID of the entry to update
     * @param event - Timeline event to add
     * @returns A promise that resolves to the updated entry or undefined
     */
    addEventToHistoryEntry(agentId: string, entryId: string, event: any): Promise<any | undefined>;
}

interface OnStartHookArgs {
    agent: Agent<any>;
    context: OperationContext;
}
interface OnEndHookArgs {
    agent: Agent<any>;
    /** The standardized successful output object. Undefined on error. */
    output: AgentOperationOutput | undefined;
    /** The VoltAgentError object if the operation failed. Undefined on success. */
    error: VoltAgentError | undefined;
    context: OperationContext;
}
interface OnHandoffHookArgs {
    agent: Agent<any>;
    source: Agent<any>;
}
interface OnToolStartHookArgs {
    agent: Agent<any>;
    tool: AgentTool;
    context: OperationContext;
}
interface OnToolEndHookArgs {
    agent: Agent<any>;
    tool: AgentTool;
    /** The successful output from the tool. Undefined on error. */
    output: unknown | undefined;
    /** The VoltAgentError if the tool execution failed. Undefined on success. */
    error: VoltAgentError | undefined;
    context: OperationContext;
}
type AgentHookOnStart = (args: OnStartHookArgs) => Promise<void> | void;
type AgentHookOnEnd = (args: OnEndHookArgs) => Promise<void> | void;
type AgentHookOnHandoff = (args: OnHandoffHookArgs) => Promise<void> | void;
type AgentHookOnToolStart = (args: OnToolStartHookArgs) => Promise<void> | void;
type AgentHookOnToolEnd = (args: OnToolEndHookArgs) => Promise<void> | void;
/**
 * Type definition for agent hooks using single argument objects.
 */
type AgentHooks = {
    onStart?: AgentHookOnStart;
    onEnd?: AgentHookOnEnd;
    onHandoff?: AgentHookOnHandoff;
    onToolStart?: AgentHookOnToolStart;
    onToolEnd?: AgentHookOnToolEnd;
};
/**
 * Create hooks from an object literal.
 */
declare function createHooks(hooks?: Partial<AgentHooks>): AgentHooks;

/**
 * SubAgentManager - Manages sub-agents and delegation functionality for an Agent
 */
declare class SubAgentManager {
    /**
     * The name of the agent that owns this sub-agent manager
     */
    private agentName;
    /**
     * Sub-agents that the parent agent can delegate tasks to
     */
    private subAgents;
    /**
     * Creates a new SubAgentManager instance
     *
     * @param agentName - The name of the agent that owns this sub-agent manager
     * @param subAgents - Initial sub-agents to add
     */
    constructor(agentName: string, subAgents?: Agent<any>[]);
    /**
     * Add a sub-agent that the parent agent can delegate tasks to
     */
    addSubAgent(agent: Agent<any>): void;
    /**
     * Remove a sub-agent
     */
    removeSubAgent(agentId: string): void;
    /**
     * Unregister all sub-agents when parent agent is destroyed
     */
    unregisterAllSubAgents(): void;
    /**
     * Get all sub-agents
     */
    getSubAgents(): Agent<any>[];
    /**
     * Calculate maximum number of steps based on sub-agents
     * More sub-agents means more potential steps
     */
    calculateMaxSteps(): number;
    /**
     * Generate enhanced system message for supervisor role
     * @param baseDescription - The base description of the agent
     * @param agentsMemory - Optional string containing formatted memory from previous agent interactions
     */
    generateSupervisorSystemMessage(baseInstructions: string, agentsMemory?: string): string;
    /**
     * Check if the agent has sub-agents
     */
    hasSubAgents(): boolean;
    /**
     * Hand off a task to another agent
     */
    handoffTask(options: AgentHandoffOptions): Promise<AgentHandoffResult>;
    /**
     * Hand off a task to multiple agents in parallel
     */
    handoffToMultiple(options: Omit<AgentHandoffOptions, "targetAgent"> & {
        targetAgents: Agent<any>[];
        userContext?: Map<string | symbol, unknown>;
    }): Promise<AgentHandoffResult[]>;
    /**
     * Create a delegate tool for sub-agents
     */
    createDelegateTool(options?: Record<string, any>): BaseTool;
    /**
     * Get sub-agent details for API exposure
     */
    getSubAgentDetails(): Array<Record<string, any>>;
}

/**
 * Options for configuring the Retriever
 */
type RetrieverOptions = {
    /**
     * Name for the default tool created from this retriever
     * This is used for the pre-created 'tool' property
     * @default "search_knowledge"
     */
    toolName?: string;
    /**
     * Description for the default tool created from this retriever
     * This is used for the pre-created 'tool' property
     * @default "Searches for relevant information in the knowledge base based on the query."
     */
    toolDescription?: string;
    /**
     * Additional configuration specific to concrete retriever implementations
     */
    [key: string]: any;
};
/**
 * Retriever interface for retrieving relevant information
 */
type Retriever = {
    /**
     * Retrieve relevant documents based on input text
     * @param text The text to use for retrieval
     * @returns Promise resolving to an array of retrieval results
     */
    retrieve(text: string): Promise<string>;
    /**
     * Configuration options for the retriever
     * This is optional and may not be present in all implementations
     */
    options?: RetrieverOptions;
    /**
     * Pre-created tool for easy destructuring
     * This is optional and may not be present in all implementations
     */
    tool?: any;
};

/**
 * Abstract base class for Retriever implementations.
 * This class provides a common structure for different types of retrievers.
 */
declare abstract class BaseRetriever {
    /**
     * Options that configure the retriever's behavior
     */
    protected options: RetrieverOptions;
    /**
     * Ready-to-use tool property for direct destructuring
     * This can be used with object destructuring syntax
     *
     * @example
     * ```typescript
     * // ✅ You can use destructuring with the tool property
     * const { tool } = new SimpleRetriever();
     *
     * // And use it directly in an agent
     * const agent = new Agent({
     *   name: "RAG Agent",
     *   model: "gpt-4",
     *   provider,
     *   tools: [tool],
     * });
     * ```
     */
    readonly tool: AgentTool;
    /**
     * Constructor for the BaseRetriever class.
     * @param options - Configuration options for the retriever.
     */
    constructor(options?: RetrieverOptions);
    /**
     * Retrieve information based on input.
     * This method must be implemented by all concrete subclasses.
     *
     * @param input - The input to base the retrieval on, can be string or BaseMessage array
     * @returns A Promise that resolves to a formatted context string
     */
    abstract retrieve(input: string | BaseMessage[]): Promise<string>;
}

/**
 * ReadableStream type for voice responses
 */
type ReadableStreamType = ReadableStream | NodeJS.ReadableStream | any;
/**
 * Voice provider options
 */
type VoiceOptions = {
    /**
     * API key for the voice provider
     */
    apiKey?: string;
    /**
     * Model to use for speech recognition
     */
    speechModel?: string;
    /**
     * Model to use for text-to-speech
     */
    ttsModel?: string;
    /**
     * Voice ID to use for text-to-speech
     */
    voice?: string;
    /**
     * Additional provider-specific options
     */
    options?: Record<string, unknown>;
};
/**
 * Voice event types
 */
type VoiceEventType = "speaking" | "listening" | "error" | "connected" | "disconnected";
/**
 * Voice event data types
 */
type VoiceEventData = {
    speaking: {
        text: string;
        audio?: NodeJS.ReadableStream;
    };
    listening: {
        audio: NodeJS.ReadableStream;
    };
    error: {
        message: string;
        code?: string;
        details?: unknown;
    };
    connected: undefined;
    disconnected: undefined;
};
/**
 * Voice metadata
 */
type VoiceMetadata = {
    id: string;
    name: string;
    language: string;
    gender?: "male" | "female" | "neutral";
    metadata?: Record<string, unknown>;
};
/**
 * Base interface for voice providers
 */
type Voice = {
    /**
     * Convert text to speech
     */
    speak(text: string | NodeJS.ReadableStream, options?: {
        voice?: string;
        speed?: number;
        pitch?: number;
    }): Promise<NodeJS.ReadableStream>;
    /**
     * Convert speech to text
     */
    listen(audio: NodeJS.ReadableStream, options?: {
        language?: string;
        model?: string;
        stream?: boolean;
    }): Promise<string | ReadableStreamType>;
    /**
     * Connect to real-time voice service
     */
    connect(options?: Record<string, unknown>): Promise<void>;
    /**
     * Disconnect from real-time voice service
     */
    disconnect(): void;
    /**
     * Send audio data to real-time service
     */
    send(audioData: NodeJS.ReadableStream | Int16Array): Promise<void>;
    /**
     * Register event listener
     */
    on<E extends VoiceEventType>(event: E, callback: (data: VoiceEventData[E]) => void): void;
    /**
     * Remove event listener
     */
    off<E extends VoiceEventType>(event: E, callback: (data: VoiceEventData[E]) => void): void;
    /**
     * Get available voices
     */
    getVoices(): Promise<VoiceMetadata[]>;
};

/**
 * Agent class for interacting with AI models
 */
declare class Agent<TProvider extends {
    llm: LLMProvider<unknown>;
}> {
    /**
     * Unique identifier for the agent
     */
    readonly id: string;
    /**
     * Agent name
     */
    readonly name: string;
    /**
     * @deprecated Use `instructions` instead. Will be removed in a future version.
     */
    readonly description: string;
    /**
     * Agent instructions. This is the preferred field over `description`.
     */
    readonly instructions: string;
    /**
     * The LLM provider to use
     */
    readonly llm: ProviderInstance<TProvider>;
    /**
     * The AI model to use
     */
    readonly model: ModelType<TProvider>;
    /**
     * Hooks for agent lifecycle events
     */
    hooks: AgentHooks;
    /**
     * Voice provider for the agent
     */
    readonly voice?: Voice;
    /**
     * Indicates if the agent should format responses using Markdown.
     */
    readonly markdown: boolean;
    /**
     * Memory manager for the agent
     */
    protected memoryManager: MemoryManager;
    /**
     * Tool manager for the agent
     */
    protected toolManager: ToolManager;
    /**
     * Sub-agent manager for the agent
     */
    protected subAgentManager: SubAgentManager;
    /**
     * History manager for the agent
     */
    protected historyManager: HistoryManager;
    /**
     * Retriever for automatic RAG
     */
    private retriever?;
    /**
     * Create a new agent
     */
    constructor(options: AgentOptions & TProvider & {
        model: ModelType<TProvider>;
        subAgents?: Agent<any>[];
        maxHistoryEntries?: number;
        hooks?: AgentHooks;
        retriever?: BaseRetriever;
        voice?: Voice;
        markdown?: boolean;
        telemetryExporter?: VoltAgentExporter;
    });
    /**
     * Get the system message for the agent
     */
    protected getSystemMessage({ input, historyEntryId, contextMessages, }: {
        input?: string | BaseMessage[];
        historyEntryId: string;
        contextMessages: BaseMessage[];
    }): Promise<BaseMessage>;
    /**
     * Prepare agents memory for the supervisor system message
     * This fetches and formats recent interactions with sub-agents
     */
    private prepareAgentsMemory;
    /**
     * Add input to messages array based on type
     */
    private formatInputMessages;
    /**
     * Calculate maximum number of steps based on sub-agents
     */
    private calculateMaxSteps;
    /**
     * Prepare common options for text generation
     */
    private prepareTextOptions;
    /**
     * Initialize a new history entry
     * @param input User input
     * @param initialStatus Initial status
     * @param options Options including parent context
     * @returns Created operation context
     */
    private initializeHistory;
    /**
     * Get full agent state including tools status
     */
    getFullState(): {
        id: string;
        name: string;
        description: string;
        instructions: string;
        status: string;
        model: string;
        node_id: string;
        tools: {
            node_id: string;
            id: string;
            name: string;
            description: string;
            parameters: any;
            execute: (args: any, options?: ToolExecuteOptions) => Promise<unknown>;
        }[];
        subAgents: {
            node_id: string;
        }[];
        memory: {
            node_id: string;
        };
        retriever: {
            name: string;
            description: string;
            status: string;
            node_id: string;
        } | null;
    };
    /**
     * Get agent's history
     */
    getHistory(): Promise<AgentHistoryEntry[]>;
    /**
     * Add step to history immediately
     */
    private addStepToHistory;
    /**
     * Update history entry
     */
    private updateHistoryEntry;
    /**
     * Standard timeline event creator
     */
    private createStandardTimelineEvent;
    /**
     * Fix delete operator usage for better performance
     */
    private addToolEvent;
    /**
     * Agent event creator (update)
     */
    private addAgentEvent;
    /**
     * Helper method to enrich and end an OpenTelemetry span associated with a tool call.
     */
    private _endOtelToolSpan;
    /**
     * Generate a text response without streaming
     */
    generateText(input: string | BaseMessage[], options?: PublicGenerateOptions): Promise<InferGenerateTextResponse$1<TProvider>>;
    /**
     * Stream a text response
     */
    streamText(input: string | BaseMessage[], options?: PublicGenerateOptions): Promise<InferStreamTextResponse<TProvider>>;
    /**
     * Generate a structured object response
     */
    generateObject<T extends z.ZodType>(input: string | BaseMessage[], schema: T, options?: PublicGenerateOptions): Promise<InferGenerateObjectResponse$1<TProvider>>;
    /**
     * Stream a structured object response
     */
    streamObject<T extends z.ZodType>(input: string | BaseMessage[], schema: T, options?: PublicGenerateOptions): Promise<InferStreamObjectResponse<TProvider>>;
    /**
     * Add a sub-agent that this agent can delegate tasks to
     */
    addSubAgent(agent: Agent<any>): void;
    /**
     * Remove a sub-agent
     */
    removeSubAgent(agentId: string): void;
    /**
     * Get agent's tools for API exposure
     */
    getToolsForApi(): {
        name: string;
        description: string;
        parameters: any;
    }[];
    /**
     * Get all tools
     */
    getTools(): BaseTool[];
    /**
     * Get agent's model name for API exposure
     */
    getModelName(): string;
    /**
     * Get all sub-agents
     */
    getSubAgents(): Agent<any>[];
    /**
     * Unregister this agent
     */
    unregister(): void;
    /**
     * Get agent's history manager
     * This provides access to the history manager for direct event handling
     * @returns The history manager instance
     */
    getHistoryManager(): HistoryManager;
    /**
     * Checks if telemetry (VoltAgentExporter) is configured for this agent.
     * @returns True if telemetry is configured, false otherwise.
     */
    isTelemetryConfigured(): boolean;
    /**
     * Add one or more tools or toolkits to the agent.
     * Delegates to ToolManager's addItems method.
     * @returns Object containing added items (difficult to track precisely here, maybe simplify return)
     */
    addItems(items: (Tool<any> | Toolkit)[]): {
        added: (Tool<any> | Toolkit)[];
    };
    /**
     * @internal
     * Internal method to set the VoltAgentExporter on the agent's HistoryManager.
     * This is typically called by the main VoltAgent instance after it has initialized its exporter.
     */
    _INTERNAL_setVoltAgentExporter(exporter: VoltAgentExporter): void;
}

/**
 * Enum defining the next action to take after a reasoning step.
 */
declare enum NextAction {
    CONTINUE = "continue",
    VALIDATE = "validate",
    FINAL_ANSWER = "final_answer"
}
/**
 * Zod schema for the ReasoningStep data structure.
 */
declare const ReasoningStepSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["thought", "analysis"]>;
    title: z.ZodString;
    reasoning: z.ZodString;
    action: z.ZodOptional<z.ZodString>;
    result: z.ZodOptional<z.ZodString>;
    next_action: z.ZodOptional<z.ZodNativeEnum<typeof NextAction>>;
    confidence: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    timestamp: z.ZodString;
    historyEntryId: z.ZodString;
    agentId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    title: string;
    type: "thought" | "analysis";
    timestamp: string;
    historyEntryId: string;
    agentId: string;
    reasoning: string;
    confidence: number;
    action?: string | undefined;
    result?: string | undefined;
    next_action?: NextAction | undefined;
}, {
    id: string;
    title: string;
    type: "thought" | "analysis";
    timestamp: string;
    historyEntryId: string;
    agentId: string;
    reasoning: string;
    action?: string | undefined;
    result?: string | undefined;
    next_action?: NextAction | undefined;
    confidence?: number | undefined;
}>;
/**
 * TypeScript type inferred from the ReasoningStepSchema.
 */
type ReasoningStep = z.infer<typeof ReasoningStepSchema>;
/**
 * Options specific to reasoning tool execution, extending base ToolExecuteOptions.
 */
interface ReasoningToolExecuteOptions extends ToolExecuteOptions {
    agentId: string;
    historyEntryId: string;
}

declare const DEFAULT_INSTRUCTIONS = "\nYou are equipped with 'think' and 'analyze' capabilities to methodically tackle problems and organize your reasoning process. ALWAYS utilize 'think' before initiating any tool calls or formulating a response.\n\n1.  **Think** (Internal Workspace):\n    *   Objective: Employ the 'think' tool as an internal workspace to dissect complex issues, chart out solution paths, and determine the next steps in your reasoning. Use this to organize your internal thought process.\n    *   Method: Invoke 'think' repeatedly if necessary for problem decomposition. Articulate your rationale and specify the planned next step (e.g., \"initiate tool call,\" \"compute value,\" \"request clarification\").\n\n2.  **Analyze** (Assessment):\n    *   Objective: Assess the outcome of a thinking phase or a sequence of tool interactions. Determine if the outcome aligns with expectations, is adequate, or necessitates further exploration.\n    *   Method: Call 'analyze' following a series of tool uses or a completed thought sequence. Define the 'next_action' based on your assessment: 'continue' (further reasoning is required), 'validate' (if possible, seek external verification), or 'final_answer' (prepared to deliver the conclusion).\n    *   Justify your assessment, indicating whether the result is accurate/sufficient.\n\n## Core Principles\n*   **Initiate with Thought:** It is MANDATORY to use the 'think' tool prior to other tool interactions or response generation, except for trivial requests. Use 'think' multiple times for intricate problems.\n*   **Iterative Problem Solving:** Employ 'think' and 'analyze' in cycles to construct a transparent reasoning trajectory. The standard sequence is Think -> [Think -> ...] -> [Tool Calls if needed] -> [Analyze if needed] -> ... -> final_answer. Repeat this loop until a satisfactory resolution is achieved.\n*   **Parallel Tool Execution:** Following a 'think' step, multiple tool calls can be executed concurrently if required.\n*   **Maintain Internal Reasoning:** The steps involving 'think' and 'analyze' constitute your internal cognitive process. Do not expose these steps directly to the user unless specifically asked to elaborate on your reasoning.\n*   **Deliver Concise Conclusions:** Once your analysis concludes with 'next_action: final_answer', present a clear and precise final answer to the user, synthesized from your reasoning steps.\n";
declare const FEW_SHOT_EXAMPLES = "\n## Illustrations\n\n**Illustration 1: Basic Knowledge Retrieval**\n\n*User Query:* What is the tallest mountain in the world?\n\n*Agent's Internal Processing:*\n```json\n{\n  \"tool_call\": {\n    \"name\": \"think\",\n    \"arguments\": {\n      \"title\": \"Parse Request\",\n      \"thought\": \"The user is asking for the name of the world's highest peak. This is well-known geographical data.\",\n      \"action\": \"Recall or look up the tallest mountain.\",\n      \"confidence\": 0.98\n    }\n  }\n}\n```\n*--(Agent internally accesses the information)--*\n```json\n{\n  \"tool_call\": {\n    \"name\": \"analyze\",\n    \"arguments\": {\n      \"title\": \"Assess Information\",\n      \"result\": \"Mount Everest is recognized as the tallest mountain above sea level.\",\n      \"analysis\": \"The retrieved data directly answers the user's query accurately.\",\n      \"next_action\": \"final_answer\",\n      \"confidence\": 1.0\n    }\n  }\n}\n```\n\n*Agent's Final Response to User:*\nThe tallest mountain in the world is Mount Everest.\n\n**Illustration 2: Sequential Information Gathering**\n\n*User Query:* Who directed the movie 'Inception' and what year was it released?\n\n*Agent's Internal Processing:*\n```json\n{\n  \"tool_call\": {\n    \"name\": \"think\",\n    \"arguments\": {\n      \"title\": \"Outline Information Needs\",\n      \"thought\": \"The request asks for two specific details about the movie 'Inception': its director and release year. I'll handle these sequentially. First, find the director.\",\n      \"action\": \"Search for the director of 'Inception'.\",\n      \"confidence\": 0.95\n    }\n  }\n}\n```\n*--(Tool interaction: search(query=\"director of Inception\"))--*\n*--(Tool Outcome: \"Christopher Nolan\")--*\n```json\n{\n  \"tool_call\": {\n    \"name\": \"analyze\",\n    \"arguments\": {\n      \"title\": \"Evaluate Director Search\",\n      \"result\": \"The search identified Christopher Nolan as the director.\",\n      \"analysis\": \"This fulfills the first part of the request. Next, I need the release year.\",\n      \"next_action\": \"continue\",\n      \"confidence\": 1.0\n    }\n  }\n}\n```\n```json\n{\n  \"tool_call\": {\n    \"name\": \"think\",\n    \"arguments\": {\n      \"title\": \"Plan Release Year Retrieval\",\n      \"thought\": \"The subsequent step is to determine the release year for 'Inception'.\",\n      \"action\": \"Search for the release year of 'Inception'.\",\n      \"confidence\": 0.95\n    }\n  }\n}\n```\n*--(Tool interaction: search(query=\"release year of Inception\"))--*\n*--(Tool Outcome: \"2010\")--*\n```json\n{\n  \"tool_call\": {\n    \"name\": \"analyze\",\n    \"arguments\": {\n      \"title\": \"Evaluate Release Year Search\",\n      \"result\": \"The search indicated the release year was 2010.\",\n      \"analysis\": \"I have now obtained both the director's name and the release year. I am ready to formulate the final response.\",\n      \"next_action\": \"final_answer\",\n      \"confidence\": 1.0\n    }\n  }\n}\n```\n\n*Agent's Final Response to User:*\nThe movie 'Inception' was directed by Christopher Nolan and released in 2010.\n";
type CreateReasoningToolsOptions = {
    addInstructions?: boolean;
    think?: boolean;
    analyze?: boolean;
    addFewShot?: boolean;
    fewShotExamples?: string;
};
/**
 * Factory function to create a Toolkit containing reasoning tools and instructions.
 */
declare const createReasoningTools: (options?: CreateReasoningToolsOptions) => Toolkit;

/**
 * Basic type definitions for VoltAgent Core
 */
/**
 * Retry configuration for error handling
 */
interface RetryConfig {
    /**
     * Maximum number of retry attempts
     * @default 3
     */
    maxRetries?: number;
    /**
     * Initial delay between retries in milliseconds
     * @default 1000
     */
    initialDelay?: number;
    /**
     * Backoff multiplier for exponential backoff
     * @default 2
     */
    backoffMultiplier?: number;
    /**
     * Maximum delay between retries in milliseconds
     * @default 30000
     */
    maxDelay?: number;
    /**
     * Callback function called when an error occurs
     * Return true to retry, false to abort
     * @param error The error that occurred
     * @param attempt Current attempt number (1-based)
     * @param maxRetries Maximum number of retries
     * @returns Boolean indicating whether to retry
     */
    onError?: ((error: Error, attempt: number, maxRetries: number) => boolean | Promise<boolean>) | null;
}

/**
 * Prompt management utilities for agent prompt tuning
 */
type ExtractVariableNames<T extends string> = T extends `${string}{{${infer Param}}}${infer Rest}` ? Param | ExtractVariableNames<Rest> : never;
type AllowedVariableValue = string | number | boolean | undefined | null;
type TemplateVariables<T extends string> = {
    [K in ExtractVariableNames<T>]: AllowedVariableValue;
};
type PromptTemplate<T extends string> = [ExtractVariableNames<T>] extends [never] ? {
    template: T;
    variables?: Record<string, never>;
} : {
    template: T;
    variables: TemplateVariables<T>;
};
type PromptCreator<T extends string> = (extraVariables?: Partial<TemplateVariables<T>>) => string;
/**
 * Creates a type-safe, customizable prompt function from a template string.
 * Variable names are automatically inferred from the template `{{variable}}` syntax.
 *
 * @param template - The template string with `{{variable}}` placeholders.
 * @param variables - An object containing the default values for the template variables.
 * @returns A function that takes optional extra variables and returns the processed prompt string.
 */
declare const createPrompt: <T extends string>({ template, variables, }: PromptTemplate<T>) => PromptCreator<T>;

/**
 * Node types for agents, tools, and other components
 */
declare enum NodeType {
    AGENT = "agent",
    SUBAGENT = "agent",
    TOOL = "tool",
    MEMORY = "memory",
    MESSAGE = "message",
    OUTPUT = "output",
    RETRIEVER = "retriever"
}
/**
 * Standard node ID creation function
 * @param type Node type
 * @param name Main identifier (tool name, agent name, etc.)
 * @param ownerId Owner ID (optional)
 * @returns Standard formatted node ID
 */
declare const createNodeId: (type: NodeType, name: string, ownerId?: string) => string;
/**
 * Function to extract node type from NodeID
 * @param nodeId Node ID
 * @returns NodeType or null (if type cannot be found)
 */
declare const getNodeTypeFromNodeId: (nodeId: string) => NodeType | null;

/**
 * Tool call interface
 */
interface ToolCall {
    id: string;
    type: "function";
    function: {
        name: string;
        arguments: string;
    };
}
/**
 * Converts a Zod-like schema to a JSON representation usable in the UI
 * @param schema Any Zod schema object
 * @returns A JSON Schema compatible representation of the Zod schema
 */
declare function zodSchemaToJsonUI(schema: any): any;

type UpdateOptions = {
    filter?: string;
};
/**
 * Package update info with semver details
 */
type PackageUpdateInfo = {
    name: string;
    installed: string;
    latest: string;
    type: "major" | "minor" | "patch" | "latest";
    packageJson: string;
};
/**
 * Checks for dependency updates using npm-check-updates
 * @returns Object containing update information
 */
declare const checkForUpdates: (packagePath?: string, options?: UpdateOptions) => Promise<{
    hasUpdates: boolean;
    updates: PackageUpdateInfo[];
    count: number;
    message: string;
}>;
/**
 * Update all packages that have available updates using npm-check-updates
 * @param packagePath Optional path to package.json, uses current directory if not provided
 * @returns Result of the update operation
 */
declare const updateAllPackages: (packagePath?: string) => Promise<{
    success: boolean;
    message: string;
    updatedPackages?: string[];
}>;
/**
 * Update a single package to its latest version using npm-check-updates
 * @param packageName Name of the package to update
 * @param packagePath Optional path to package.json, uses current directory if not provided
 * @returns Result of the update operation
 */
declare const updateSinglePackage: (packageName: string, packagePath?: string) => Promise<{
    success: boolean;
    message: string;
    packageName: string;
}>;

declare function serializeValueForDebug(value: unknown): unknown;

/**
 * Creates an AgentTool from a retriever, allowing it to be used as a tool in an agent.
 * This is the preferred way to use a retriever as a tool, as it properly maintains the 'this' context.
 *
 * @param retriever - The retriever instance to convert to a tool
 * @param options - Options for customizing the tool
 * @returns An AgentTool that can be added to an agent's tools
 *
 * @example
 * ```typescript
 * const retriever = new SimpleRetriever();
 * const searchTool = createRetrieverTool(retriever, {
 *   name: "search_knowledge",
 *   description: "Searches the knowledge base for information"
 * });
 *
 * agent.addTool(searchTool);
 * ```
 */
declare const createRetrieverTool: (retriever: Retriever, options?: {
    name?: string;
    description?: string;
}) => AgentTool;

/**
 * Client information for MCP
 */
interface ClientInfo {
    /**
     * Client name
     */
    name: string;
    /**
     * Client version
     */
    version: string;
    /**
     * Allow additional properties for SDK compatibility
     */
    [key: string]: unknown;
}
/**
 * Transport error from MCP
 */
interface TransportError extends Error {
    /**
     * Error code
     */
    code?: string;
    /**
     * Error details
     */
    details?: unknown;
}
/**
 * Model Context Protocol (MCP) configuration options
 */
type MCPOptions = {
    /**
     * Whether MCP is enabled
     */
    enabled: boolean;
    /**
     * MCP API endpoint
     */
    endpoint?: string;
    /**
     * API key for MCP authentication
     */
    apiKey?: string;
    /**
     * Control parameters for MCP
     */
    controlParams?: Record<string, unknown>;
    /**
     * Whether to fall back to the provider if MCP fails
     */
    fallbackToProvider?: boolean;
    /**
     * Timeout in milliseconds for MCP requests
     * @default 30000
     */
    timeout?: number;
};
/**
 * Configuration for MCP client
 */
type MCPClientConfig = {
    /**
     * Client information
     */
    clientInfo: ClientInfo;
    /**
     * MCP server configuration
     */
    server: MCPServerConfig;
    /**
     * MCP capabilities
     */
    capabilities?: ClientCapabilities;
    /**
     * Timeout in milliseconds for MCP requests
     * @default 30000
     */
    timeout?: number;
};
/**
 * MCP server configuration options
 */
type MCPServerConfig = HTTPServerConfig | StdioServerConfig;
/**
 * HTTP-based MCP server configuration via SSE
 */
type HTTPServerConfig = {
    /**
     * Type of server connection
     */
    type: "http";
    /**
     * URL of the MCP server
     */
    url: string;
    /**
     * Request initialization options
     */
    requestInit?: RequestInit;
    /**
     * Event source initialization options
     */
    eventSourceInit?: EventSourceInit;
};
/**
 * Stdio-based MCP server configuration
 */
type StdioServerConfig = {
    /**
     * Type of server connection
     */
    type: "stdio";
    /**
     * Command to run the MCP server
     */
    command: string;
    /**
     * Arguments to pass to the command
     */
    args?: string[];
    /**
     * Environment variables for the MCP server process
     */
    env?: Record<string, string>;
    /**
     * Working directory for the MCP server process
     */
    cwd?: string;
};
/**
 * Tool call request
 */
type MCPToolCall = {
    /**
     * Name of the tool to call
     */
    name: string;
    /**
     * Arguments to pass to the tool
     */
    arguments: Record<string, unknown>;
};
/**
 * Tool call result
 */
type MCPToolResult = {
    /**
     * Result content from the tool
     */
    content: unknown;
};
/**
 * MCP client events
 */
interface MCPClientEvents {
    /**
     * Emitted when the client connects to the server
     */
    connect: () => void;
    /**
     * Emitted when the client disconnects from the server
     */
    disconnect: () => void;
    /**
     * Emitted when an error occurs
     */
    error: (error: Error | TransportError) => void;
    /**
     * Emitted when a tool call completes
     */
    toolCall: (name: string, args: Record<string, unknown>, result: unknown) => void;
}
/**
 * Map of toolset names to tools
 */
type ToolsetMap = Record<string, ToolsetWithTools>;
/**
 * A record of tools along with a helper method to convert them to an array.
 */
type ToolsetWithTools = Record<string, AnyToolConfig> & {
    /**
     * Converts the toolset to an array of BaseTool objects.
     */
    getTools: () => Tool<any>[];
};
/**
 * Any tool configuration
 */
type AnyToolConfig = Tool<any>;

/**
 * Client for interacting with Model Context Protocol (MCP) servers.
 * Wraps the official MCP SDK client to provide a higher-level interface.
 * Internal implementation differs from original source.
 */
declare class MCPClient extends EventEmitter {
    /**
     * Underlying MCP client instance from the SDK.
     */
    private client;
    /**
     * Communication channel (transport layer) for MCP interactions.
     */
    private transport;
    /**
     * Tracks the connection status to the server.
     */
    private connected;
    /**
     * Maximum time allowed for requests in milliseconds.
     */
    private readonly timeout;
    /**
     * Information identifying this client to the server.
     */
    private readonly clientInfo;
    /**
     * Creates a new MCP client instance.
     * @param config Configuration for the client, including server details and client identity.
     */
    constructor(config: MCPClientConfig);
    /**
     * Sets up handlers for events from the underlying SDK client.
     */
    private setupEventHandlers;
    /**
     * Establishes a connection to the configured MCP server.
     * Idempotent: does nothing if already connected.
     */
    connect(): Promise<void>;
    /**
     * Closes the connection to the MCP server.
     * Idempotent: does nothing if not connected.
     */
    disconnect(): Promise<void>;
    /**
     * Fetches the definitions of available tools from the server.
     * @returns A record mapping tool names to their definitions (schema, description).
     */
    listTools(): Promise<Record<string, unknown>>;
    /**
     * Builds executable Tool objects from the server's tool definitions.
     * These tools include an `execute` method for calling the remote tool.
     * @returns A record mapping namespaced tool names (`clientName_toolName`) to executable Tool objects.
     */
    getAgentTools(): Promise<Record<string, Tool<any>>>;
    /**
     * Executes a specified tool on the remote MCP server.
     * @param toolCall Details of the tool to call, including name and arguments.
     * @returns The result content returned by the tool.
     */
    callTool(toolCall: MCPToolCall): Promise<MCPToolResult>;
    /**
     * Retrieves a list of resource identifiers available on the server.
     * @returns A promise resolving to an array of resource ID strings.
     */
    listResources(): Promise<string[]>;
    /**
     * Ensures the client is connected before proceeding with an operation.
     * Attempts to connect if not currently connected.
     * @throws Error if connection attempt fails.
     */
    private ensureConnected;
    /**
     * Emits an 'error' event, ensuring the payload is always an Error object.
     * @param error The error encountered, can be of any type.
     */
    private emitError;
    /**
     * Type guard to check if a server configuration is for an HTTP server.
     * @param server The server configuration object.
     * @returns True if the configuration type is 'http', false otherwise.
     */
    private isHTTPServer;
    /**
     * Type guard to check if a server configuration is for a Stdio server.
     * @param server The server configuration object.
     * @returns True if the configuration type is 'stdio', false otherwise.
     */
    private isStdioServer;
    /**
     * Overrides EventEmitter's 'on' method for type-safe event listening.
     * Uses the original `MCPClientEvents` for event types.
     */
    on<E extends keyof MCPClientEvents>(event: E, listener: MCPClientEvents[E]): this;
    /**
     * Overrides EventEmitter's 'emit' method for type-safe event emission.
     * Uses the original `MCPClientEvents` for event types.
     */
    emit<E extends keyof MCPClientEvents>(event: E, ...args: Parameters<MCPClientEvents[E]>): boolean;
}

/**
 * Configuration manager for Model Context Protocol (MCP).
 * Handles multiple MCP server connections and tool management.
 * NOTE: This version does NOT manage singleton instances automatically.
 */
declare class MCPConfiguration<TServerKeys extends string = string> {
    /**
     * Map of server configurations keyed by server names.
     */
    private readonly serverConfigs;
    /**
     * Map of connected MCP clients keyed by server names (local cache).
     */
    private readonly mcpClientsById;
    /**
     * Creates a new, independent MCP configuration instance.
     * @param options Configuration options including server definitions.
     */
    constructor(options: {
        servers: Record<TServerKeys, MCPServerConfig>;
    });
    /**
     * Type guard to check if an object conforms to the basic structure of AnyToolConfig.
     */
    private isAnyToolConfigStructure;
    /**
     * Disconnects all associated MCP clients for THIS instance.
     */
    disconnect(): Promise<void>;
    /**
     * Retrieves agent-ready tools from all configured MCP servers for this instance.
     * @returns A flat array of all agent-ready tools.
     */
    getTools(): Promise<Tool<any>[]>;
    /**
     * Retrieves raw tool definitions from all configured MCP servers for this instance.
     * @returns A flat record of all raw tools keyed by their namespaced name.
     */
    getRawTools(): Promise<Record<string, AnyToolConfig>>;
    /**
     * Retrieves agent-ready toolsets grouped by server name for this instance.
     * @returns A record where keys are server names and values are agent-ready toolsets.
     */
    getToolsets(): Promise<Record<TServerKeys, ToolsetWithTools>>;
    /**
     * Retrieves raw tool definitions grouped by server name for this instance.
     * @returns A record where keys are server names and values are records of raw tools.
     */
    getRawToolsets(): Promise<Record<TServerKeys, Record<string, AnyToolConfig>>>;
    /**
     * Retrieves a specific connected MCP client by its server name for this instance.
     */
    getClient(serverName: TServerKeys): Promise<MCPClient | undefined>;
    /**
     * Retrieves all configured MCP clients for this instance, ensuring they are connected.
     */
    getClients(): Promise<Record<TServerKeys, MCPClient>>;
    /**
     * Internal helper to get/create/connect a client for this instance.
     * Manages the local mcpClientsById cache.
     */
    private getConnectedClient;
}

/**
 * Registry to manage and track agents
 */
declare class AgentRegistry {
    private static instance;
    private agents;
    private isInitialized;
    private globalVoltAgentExporter?;
    /**
     * Track parent-child relationships between agents (child -> parents)
     */
    private agentRelationships;
    private constructor();
    /**
     * Get the singleton instance of AgentRegistry
     */
    static getInstance(): AgentRegistry;
    /**
     * Initialize the registry
     */
    initialize(): void;
    /**
     * Register a new agent
     */
    registerAgent(agent: Agent<any>): void;
    /**
     * Get an agent by ID
     */
    getAgent(id: string): Agent<any> | undefined;
    /**
     * Get all registered agents
     */
    getAllAgents(): Agent<any>[];
    /**
     * Register a parent-child relationship between agents
     * @param parentId ID of the parent agent
     * @param childId ID of the child agent (sub-agent)
     */
    registerSubAgent(parentId: string, childId: string): void;
    /**
     * Remove a parent-child relationship
     * @param parentId ID of the parent agent
     * @param childId ID of the child agent
     */
    unregisterSubAgent(parentId: string, childId: string): void;
    /**
     * Get all parent agent IDs for a given child agent
     * @param childId ID of the child agent
     * @returns Array of parent agent IDs
     */
    getParentAgentIds(childId: string): string[];
    /**
     * Clear all parent-child relationships for an agent when it's removed
     * @param agentId ID of the agent being removed
     */
    clearAgentRelationships(agentId: string): void;
    /**
     * Remove an agent by ID
     */
    removeAgent(id: string): boolean;
    /**
     * Get agent count
     */
    getAgentCount(): number;
    /**
     * Check if registry is initialized
     */
    isRegistryInitialized(): boolean;
    /**
     * Set the global VoltAgentExporter instance.
     * This is typically called by the main VoltAgent instance.
     */
    setGlobalVoltAgentExporter(exporter: VoltAgentExporter): void;
    /**
     * Get the global VoltAgentExporter instance.
     */
    getGlobalVoltAgentExporter(): VoltAgentExporter | undefined;
}

type VoltAgentOptions = {
    agents: Record<string, Agent<any>>;
    port?: number;
    autoStart?: boolean;
    checkDependencies?: boolean;
    /**
     * Optional OpenTelemetry SpanExporter instance or array of instances,
     * or a VoltAgentExporter instance or array of instances.
     * If provided, VoltAgent will attempt to initialize and register
     * a NodeTracerProvider with a BatchSpanProcessor for the given exporter(s).
     * It's recommended to only provide this in one VoltAgent instance per application process.
     */
    telemetryExporter?: (SpanExporter | VoltAgentExporter) | (SpanExporter | VoltAgentExporter)[];
};
/**
 * Main VoltAgent class for managing agents and server
 */
declare class VoltAgent {
    private registry;
    private serverStarted;
    constructor(options: VoltAgentOptions);
    /**
     * Check for dependency updates
     */
    private checkDependencies;
    /**
     * Register an agent
     */
    registerAgent(agent: Agent<any>): void;
    /**
     * Register multiple agents
     */
    registerAgents(agents: Record<string, Agent<any>>): void;
    /**
     * Start the server
     */
    startServer(): Promise<void>;
    /**
     * Get all registered agents
     */
    getAgents(): Agent<any>[];
    /**
     * Get agent by ID
     */
    getAgent(id: string): Agent<any> | undefined;
    /**
     * Get agent count
     */
    getAgentCount(): number;
    private initializeGlobalTelemetry;
    shutdownTelemetry(): Promise<void>;
}

export { Agent, AgentHistoryEntry, AgentHookOnEnd, AgentHookOnHandoff, AgentHookOnStart, AgentHookOnToolEnd, AgentHookOnToolStart, AgentHooks, AgentOptions, AgentRegistry, AgentResponse, AgentTool, AllowedVariableValue, AnyToolConfig, BaseLLMOptions, BaseMessage, BaseRetriever, BaseTool, BaseToolCall, ClientInfo, Conversation, CreateConversationInput, CreateReasoningToolsOptions, DEFAULT_INSTRUCTIONS, DataContent, ExtractVariableNames, FEW_SHOT_EXAMPLES, FilePart, GenerateObjectOptions, GenerateTextOptions, HTTPServerConfig, ImagePart, InMemoryStorage, InferGenerateObjectResponse, InferGenerateTextResponse, InferMessage, InferModel, InferProviderParams, InferStreamResponse, InferTool, LLMProvider, LibSQLStorage, MCPClient, MCPClientConfig, MCPClientEvents, MCPConfiguration, MCPOptions, MCPServerConfig, MCPToolCall, MCPToolResult, Memory, MemoryManager, MemoryMessage, MemoryOptions, MessageContent, MessageFilterOptions, MessageRole, ModelToolCall, NextAction, NodeType, OnEndHookArgs, OnHandoffHookArgs, OnStartHookArgs, OnToolEndHookArgs, OnToolStartHookArgs, OperationContext, PackageUpdateInfo, PromptCreator, PromptTemplate, ProviderObjectResponse, ProviderObjectStreamResponse, ProviderParams, ProviderResponse, ProviderTextResponse, ProviderTextStreamResponse, ReadableStreamType, ReasoningStep, ReasoningStepSchema, ReasoningToolExecuteOptions, Retriever, RetrieverOptions, RetryConfig, StdioServerConfig, StepChunkCallback, StepFinishCallback, StepWithContent, StreamObjectFinishResult, StreamObjectOnFinishCallback, StreamObjectOptions, StreamTextFinishResult, StreamTextOnFinishCallback, StreamTextOptions, TemplateVariables, TextPart, Tool, ToolCall, ToolErrorInfo, ToolExecuteOptions, ToolExecutionContext, ToolManager, ToolOptions, ToolSchema, ToolStatus, ToolStatusInfo, Toolkit, ToolsetMap, ToolsetWithTools, TransportError, UsageInfo, Voice, VoiceEventData, VoiceEventType, VoiceMetadata, VoiceOptions, VoltAgent, VoltAgentError, VoltAgentExporter, VoltAgentExporterOptions, checkForUpdates, createHooks, createNodeId, createPrompt, createReasoningTools, createRetrieverTool, createTool, createToolkit, VoltAgent as default, getNodeTypeFromNodeId, serializeValueForDebug, tool, updateAllPackages, updateSinglePackage, zodSchemaToJsonUI };
