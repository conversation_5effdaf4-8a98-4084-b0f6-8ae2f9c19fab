import { LLMProvider, BaseMessage, StepWithContent, GenerateTextOptions, ProviderTextResponse, StreamTextOptions, ProviderTextStreamResponse, GenerateObjectOptions, ProviderObjectResponse, StreamObjectOptions, ProviderObjectStreamResponse } from '@voltagent/core';
import { LanguageModelV1CallOptions, LanguageModelV1, CoreMessage, GenerateTextResult, StreamTextResult, GenerateObjectResult, StreamObjectResult } from 'ai';
import { z } from 'zod';

type VercelProviderOptions = Omit<LanguageModelV1CallOptions, "messages" | "model" | "tools" | "maxSteps" | "schema">;

declare class VercelAIProvider implements LLMProvider<LanguageModelV1> {
    private options?;
    constructor(options?: VercelProviderOptions | undefined);
    getModelIdentifier: (model: LanguageModelV1) => string;
    toMessage: (message: BaseMessage) => CoreMessage;
    createStepFromChunk: (chunk: {
        type: string;
        [key: string]: any;
    }) => StepWithContent | null;
    /**
     * Creates a standardized VoltAgentError from a raw Vercel SDK error object.
     */
    private _createVoltagentErrorFromSdkError;
    generateText: (options: GenerateTextOptions<LanguageModelV1>) => Promise<ProviderTextResponse<GenerateTextResult<Record<string, any>, never>>>;
    streamText(options: StreamTextOptions<LanguageModelV1>): Promise<ProviderTextStreamResponse<StreamTextResult<Record<string, any>, never>>>;
    generateObject: <TSchema extends z.ZodType>(options: GenerateObjectOptions<LanguageModelV1, TSchema>) => Promise<ProviderObjectResponse<GenerateObjectResult<z.infer<TSchema>>, z.infer<TSchema>>>;
    streamObject<TSchema extends z.ZodType>(options: StreamObjectOptions<LanguageModelV1, TSchema>): Promise<ProviderObjectStreamResponse<StreamObjectResult<z.infer<TSchema>, unknown, never>, z.infer<TSchema>>>;
}

export { VercelAIProvider };
