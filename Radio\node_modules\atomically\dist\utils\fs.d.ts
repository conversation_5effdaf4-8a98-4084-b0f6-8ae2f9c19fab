/// <reference types="node" />
import * as fs from 'fs';
declare const FS: {
    chmodAttempt: typeof fs.chmod.__promisify__;
    chownAttempt: typeof fs.chown.__promisify__;
    closeAttempt: typeof fs.close.__promisify__;
    fsyncAttempt: typeof fs.fsync.__promisify__;
    mkdirAttempt: typeof fs.mkdir.__promisify__;
    realpathAttempt: typeof fs.realpath.__promisify__;
    statAttempt: typeof fs.stat.__promisify__;
    unlinkAttempt: typeof fs.unlink.__promisify__;
    closeRetry: import("../types").FN<[number], typeof fs.close.__promisify__>;
    fsyncRetry: import("../types").FN<[number], typeof fs.fsync.__promisify__>;
    openRetry: import("../types").FN<[number], typeof fs.open.__promisify__>;
    readFileRetry: import("../types").FN<[number], typeof fs.readFile.__promisify__>;
    renameRetry: import("../types").FN<[number], typeof fs.rename.__promisify__>;
    statRetry: import("../types").FN<[number], typeof fs.stat.__promisify__>;
    writeRetry: import("../types").FN<[number], typeof fs.write.__promisify__>;
    chmodSyncAttempt: typeof fs.chmodSync;
    chownSyncAttempt: typeof fs.chownSync;
    closeSyncAttempt: typeof fs.closeSync;
    mkdirSyncAttempt: typeof fs.mkdirSync;
    realpathSyncAttempt: typeof fs.realpathSync;
    statSyncAttempt: typeof fs.statSync;
    unlinkSyncAttempt: typeof fs.unlinkSync;
    closeSyncRetry: import("../types").FN<[number], typeof fs.closeSync>;
    fsyncSyncRetry: import("../types").FN<[number], typeof fs.fsyncSync>;
    openSyncRetry: import("../types").FN<[number], typeof fs.openSync>;
    readFileSyncRetry: import("../types").FN<[number], typeof fs.readFileSync>;
    renameSyncRetry: import("../types").FN<[number], typeof fs.renameSync>;
    statSyncRetry: import("../types").FN<[number], typeof fs.statSync>;
    writeSyncRetry: import("../types").FN<[number], typeof fs.writeSync>;
};
export default FS;
