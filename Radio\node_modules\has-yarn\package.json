{"name": "has-yarn", "version": "2.1.0", "description": "Check if a project is using Yarn", "license": "MIT", "repository": "sindresorhus/has-yarn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["yarn", "has", "detect", "is", "project", "app", "module", "package", "manager", "npm"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}