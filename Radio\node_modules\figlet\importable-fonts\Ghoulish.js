export default `flf2a$ 7 6 13 -1 16 0 7999 0
Author : <PERSON><PERSON>
Date   : 2004/10/1 15:09:40
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using <PERSON>av<PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

--- 

Font modified June 17, 2007 by patorjk 
This was to widen the space character and for spacing in characters like "&"
$   $#
$   $#
$   $#
$   $#
$   $#
$   $#
$   $##
.\`(   #
\\_ )  #
  )\\  #
  \\(  #
   _  #
  \`.( #
      ##
 _  _  #
).').' #
       #
       #
       #
       #
       ##
##
#
#
#
#
#
##
 )\\.-.    #
(  ,_.'   #
 \`-.\`-.   #
 .'/_\\ \\  #
 '.__,  ) #
      \\(  #
          ##
%#
 #
 #
 #
 #
 #
 ##
&#
 #
 #
 #
 #
 #
 ##
'#
 #
 #
 #
 #
 #
 ##
  )'. #
 (  / #
 ) /  #
(oO)  #
 ) \\  #
 (  \\ #
  ).' ##
.\`(   #
\\  )  #
 \\ (  #
 (Oo) #
 / (  #
/  )  #
\`.(   ##
*#
 #
 #
 #
 #
 #
 ##
   )\`.   #
  (  /   #
.-    -. #
'-    -' #
  /  )   #
  '.(    #
         ##
     #
     #
     #
 _)/ #
/  ) #
'.(  #
     ##
           #
 /(        #
 ) \\/(.-,, #
(      _  )#
 \`._.-' \\( #
           #
           ##
     #
     #
     #
 ,_  #
(  \\ #
 ).' #
     ##
  )\\  #
  \\ ) #
  //  #
 ((   #
 //   #
/(    #
)/    ##
  ,,-,,   #
 /,--, \\  #
/ \\  (  \\ #
\\  )  \\ / #
 \\ \`--\`(  #
  \`\`-\`)/  #
          ##
 .'(    #
/_  )   #
 ) (    #
 \\  )   #
 _) \\_  #
)__,__/ #
        ##
.\`\`\`./(  #
)_,-,  ) #
 .-'.-'  #
( (.-.,  #
/      ) #
)/\`._.'  #
         ##
 .---./(  #
(_.-,   ) #
  ,-\`  /  #
 _'-, (   #
 )\`-'  )  #
 \`._..(   #
          ##
  .'\`(  #
 //\`. ) #
//__/(  #
\`--,  ) #
    ) \\ #
     )/ #
        ##
  )\\.--. #
 (   ._. #
  \`-.\`.  #
,_   \\ \\ #
) '.,/ / #
'._,_.'  #
         ##
 )\`.     #
(  ( _   #
 ) \`' \`. #
(  .-. ( #
 \`. -  / #
   \\,-'  #
         ##
.\`\`\`./(  #
)_,-,  ) #
    / (  #
   _) /  #
  /  /   #
  './    #
         ##
   ,-,-.  #
 ,' _   ) #
(  '-' (  #
 )  _   ) #
(  '-' /  #
 )/._.'   #
          ##
 .-'\\    #
/  _ \`,  #
) \`-\`  ) #
\`._., (  #
    \`  ) #
     \\(  #
         ##
.'(  #
\\_ ) #
     #
 _   #
/  ) #
'.(  #
     ##
   _  #
  ).' #
      #
 _)/  #
/  )  #
'.(   #
      ##
     #
     #
/(_  #
) _( #
\\(   #
     #
     ##
 ,,-.      #
(    \`-._  #
 \`._.-._.' #
     .-,,  #
 _.-'    ) #
\`._.-._.'  #
           ##
     #
     #
 _)\\ #
)_ ( #
  )/ #
     #
     ##
 .\`\`\`./(  #
(  ,-,  ) #
 \`-\` / /  #
     )/   #
    _     #
   ).'    #
          ##
@#
 #
 #
 #
 #
 #
 ##
   /\`-.   #
 ,' _  \\  #
(  '-' (  #
 )   _  ) #
(  ,' ) \\ #
 )/    )/ #
          ##
   /(,-.  #
 ,' _   ) #
(  '-' (  #
 )  _   ) #
(  '-' /  #
 )/._.'   #
          ##
   )\\.-.  #
 ,' ,-,_) #
(  .   _  #
 ) '..' ) #
(  ,   (  #
 )/'._.'  #
          ##
   )\\.-.  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
 )\\.---.  #
(   ,-._( #
 \\  '-,   #
  ) ,-\`   #
 (  \`\`-.  #
  )..-.(  #
          ##
)\`-.--. #
) ,-._( #
\\ \`-._  #
 ) ,_(  #
(  \\    #
 ).'    #
        ##
   )\\.-.   #
 ,' ,-,_)  #
(  .   __  #
 ) '._\\ _) #
(  ,   (   #
 )/'._.'   #
           ##
     .'(  #
 ,') \\  ) #
(  '-' (  #
 ) .-.  ) #
(  ,  ) \\ #
 )/    )/ #
          ##
.'(  #
\\  ) #
) (  #
\\  ) #
 ) \\ #
  )/ #
     ##
  .-,.-.,-.#
  ).-, ,-.(#
      ))   #
 .-._((    #
(      )   #
 '._.\\(    #
           ##
    .'(  #
 ,')\\  ) #
(  '/ /  #
 )   (   #
(  .\\ \\  #
 )/  )/  #
         ##
 .')      #
( /       #
 ))       #
 )'._.-.  #
(       ) #
 )/,__.'  #
          ##
 )\\   )\\  #
(  ',/ /  #
 )    (   #
(  \\(\\ \\  #
 \`.) /  ) #
     '.(  #
          ##
 )\\  )\\  #
(  \\, /  #
 ) \\ (   #
( ( \\ \\  #
 \`.)/  ) #
    '.(  #
         ##
   .-./(  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
   /\`-.  #
 ,' _  \\ #
(  '-' ( #
 ) ,._.' #
(  '     #
 )/      #
         ##
  ,.-.    #
 /    \`,  #
 ) ,-.  ) #
( /_.\` (  #
 )   ,  ) #
 '._. \\(  #
          ##
   /\`-.  #
 ,' _  \\ #
(  '-' ( #
 ) ,_ .' #
(  ' ) \\ #
 )/   )/ #
         ##
  )\\.--.  #
 (   ._.' #
  \`-.\`.   #
 ,_ (  \\  #
(  '.)  ) #
 '._,_.'  #
          ##
.-,.-.,-. #
) ,, ,. ( #
\\( |(  )/ #
   ) \\    #
   \\ (    #
    )/    #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) './ /  #
(  ,  (   #
 )/..'    #
          ##
     .'(  #
 ,') \\  ) #
(  /(/ /  #
 )    (   #
(  .'\\ \\  #
 )/   )/  #
          ##
     .'(  #
 ,') \\  ) #
(  '/  /  #
 )     )  #
(  .'\\ \\  #
 )/   )/  #
          ##
)\\    /( #
\\ (_.' / #
 )  _.'  #
 / /     #
(  \\     #
 ).'     #
         ##
.\`\`\`./(    #
)_,-,  )   #
    / / _  #
   / \`-\` ) #
  (     (  #
   ).',,'  #
           ##
  )'. #
 ( _/ #
 /(   #
<  >  #
 \\(_  #
 (  \\ #
  ).' ##
 /(   #
( /   #
 \\\\   #
  ))  #
  \\\\  #
   )\\ #
   \\( ##
.'(   #
\\_ )  #
  )\\  #
 <  > #
 _)/  #
/  )  #
'.(   ##
  )\\   #
 /_ \\  #
/ )\\ \\ #
\\(  )/ #
       #
       #
       ##
           #
        )\\ #
 ,,-.)\\/ ( #
(  _      )#
 )/ \`-._.' #
           #
           ##
\`#
 #
 #
 #
 #
 #
 ##
   /\`-.   #
 ,' _  \\  #
(  '-' (  #
 )   _  ) #
(  ,' ) \\ #
 )/    )/ #
          ##
   /(,-.  #
 ,' _   ) #
(  '-' (  #
 )  _   ) #
(  '-' /  #
 )/._.'   #
          ##
   )\\.-.  #
 ,' ,-,_) #
(  .   _  #
 ) '..' ) #
(  ,   (  #
 )/'._.'  #
          ##
   )\\.-.  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
 )\\.---.  #
(   ,-._( #
 \\  '-,   #
  ) ,-\`   #
 (  \`\`-.  #
  )..-.(  #
          ##
)\`-.--. #
) ,-._( #
\\ \`-._  #
 ) ,_(  #
(  \\    #
 ).'    #
        ##
   )\\.-.   #
 ,' ,-,_)  #
(  .   __  #
 ) '._\\ _) #
(  ,   (   #
 )/'._.'   #
           ##
     .'(  #
 ,') \\  ) #
(  '-' (  #
 ) .-.  ) #
(  ,  ) \\ #
 )/    )/ #
          ##
.'(  #
\\  ) #
) (  #
\\  ) #
 ) \\ #
  )/ #
     ##
  .-,.-.,-.#
  ).-, ,-.(#
      ))   #
 .-._((    #
(      )   #
 '._.\\(    #
           ##
    .'(  #
 ,')\\  ) #
(  '/ /  #
 )   (   #
(  .\\ \\  #
 )/  )/  #
         ##
 .')      #
( /       #
 ))       #
 )'._.-.  #
(       ) #
 )/,__.'  #
          ##
 )\\   )\\  #
(  ',/ /  #
 )    (   #
(  \\(\\ \\  #
 \`.) /  ) #
     '.(  #
          ##
 )\\  )\\  #
(  \\, /  #
 ) \\ (   #
( ( \\ \\  #
 \`.)/  ) #
    '.(  #
         ##
   .-./(  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
   /\`-.  #
 ,' _  \\ #
(  '-' ( #
 ) ,._.' #
(  '     #
 )/      #
         ##
  ,.-.    #
 /    \`,  #
 ) ,-.  ) #
( /_.\` (  #
 )   ,  ) #
 '._. \\(  #
          ##
   /\`-.  #
 ,' _  \\ #
(  '-' ( #
 ) ,_ .' #
(  ' ) \\ #
 )/   )/ #
         ##
  )\\.--.  #
 (   ._.' #
  \`-.\`.   #
 ,_ (  \\  #
(  '.)  ) #
 '._,_.'  #
          ##
.-,.-.,-. #
) ,, ,. ( #
\\( |(  )/ #
   ) \\    #
   \\ (    #
    )/    #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) './ /  #
(  ,  (   #
 )/..'    #
          ##
     .'(  #
 ,') \\  ) #
(  /(/ /  #
 )    (   #
(  .'\\ \\  #
 )/   )/  #
          ##
     .'(  #
 ,') \\  ) #
(  '/  /  #
 )     )  #
(  .'\\ \\  #
 )/   )/  #
          ##
)\\    /( #
\\ (_.' / #
 )  _.'  #
 / /     #
(  \\     #
 ).'     #
         ##
.\`\`\`./(    #
)_,-,  )   #
    / / _  #
   / \`-\` ) #
  (     (  #
   ).',,'  #
           ##
  )\\ #
 ( / #
 /(  #
<  > #
 \\(  #
 ( \\ #
  )/ ##
|#
 #
 #
 #
 #
 #
 ##
/(   #
\\ )  #
 )\\  #
<  > #
 )/  #
/ )  #
\\(   ##
~#
 #
 #
 #
 #
 #
 ##
   /\`-.   #
 ,' _  \\  #
(  '-' (  #
 )   _  ) #
(  ,' ) \\ #
 )/    )/ #
          ##
   .-./(  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
   /\`-.   #
 ,' _  \\  #
(  '-' (  #
 )   _  ) #
(  ,' ) \\ #
 )/    )/ #
          ##
   .-./(  #
 ,'     ) #
(  .-, (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
     .-.  #
 ,'  /  ) #
(  ) | (  #
 ) '._\\ ) #
(  ,   (  #
 )/ ._.'  #
          ##
�#
 #
 #
 #
 #
 #
 ##`