{"version": 3, "file": "fromEventPattern.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/fromEventPattern.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAC3C,iDAAgD;AAEhD,6DAA4D;AAsI5D,SAAgB,gBAAgB,CAC9B,UAA8C,EAC9C,aAAiE,EACjE,cAAsC;IAEtC,IAAI,cAAc,EAAE;QAClB,OAAO,gBAAgB,CAAI,UAAU,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,mCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC;KAC9F;IAED,OAAO,IAAI,uBAAU,CAAU,UAAC,UAAU;QACxC,IAAM,OAAO,GAAG;YAAC,WAAS;iBAAT,UAAS,EAAT,qBAAS,EAAT,IAAS;gBAAT,sBAAS;;YAAK,OAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAA1C,CAA0C,CAAC;QAC1E,IAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,uBAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAM,OAAA,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAhC,CAAgC,CAAC,CAAC,CAAC,SAAS,CAAC;IACxF,CAAC,CAAC,CAAC;AACL,CAAC;AAdD,4CAcC"}