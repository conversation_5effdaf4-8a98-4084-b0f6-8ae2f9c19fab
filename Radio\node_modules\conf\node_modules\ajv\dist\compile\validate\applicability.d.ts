import type { AnySchemaObject } from "../../types";
import type { SchemaObjCxt } from "..";
import type { JSONType, RuleGroup, Rule } from "../rules";
export declare function schemaHasRulesForType({ schema, self }: SchemaObjCxt, type: JSONType): boolean | undefined;
export declare function shouldUseGroup(schema: AnySchemaObject, group: RuleGroup): boolean;
export declare function shouldUseRule(schema: AnySchemaObject, rule: Rule): boolean | undefined;
