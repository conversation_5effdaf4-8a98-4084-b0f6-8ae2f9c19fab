{"name": "dot-prop", "version": "5.3.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.25.3"}}