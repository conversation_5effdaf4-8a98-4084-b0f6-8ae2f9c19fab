{"version": 3, "sources": ["../src/index.ts", "../src/utils/index.ts"], "sourcesContent": ["import type {\n  BaseMessage,\n  GenerateObjectOptions,\n  GenerateTextOptions,\n  LLMProvider,\n  MessageRole,\n  ProviderObjectResponse,\n  ProviderObjectStreamResponse,\n  ProviderTextResponse,\n  ProviderTextStreamResponse,\n  StepWithContent,\n  StreamObjectOptions,\n  StreamTextOptions,\n  VoltAgentError,\n  ToolErrorInfo,\n  StreamObjectFinishResult,\n  UsageInfo,\n} from \"@voltagent/core\";\n// Import directly from the types file path within the dist folder\nimport type {\n  CallWarning,\n  CoreMessage,\n  FinishReason,\n  GenerateObjectResult,\n  GenerateTextResult,\n  LanguageModelRequestMetadata,\n  LanguageModelResponseMetadata,\n  LanguageModelUsage,\n  LanguageModelV1,\n  ProviderMetadata,\n  StepResult,\n  StreamObjectResult,\n  StreamTextResult,\n} from \"ai\";\nimport { generateObject, generateText, streamObject, streamText } from \"ai\";\nimport type { z } from \"zod\";\nimport type { VercelProviderOptions } from \"./types\";\nimport { convertToolsForSDK } from \"./utils\";\n\nexport class VercelAIProvider implements LLMProvider<LanguageModelV1> {\n  // @ts-ignore\n  constructor(private options?: VercelProviderOptions) {\n    // Bind methods to preserve 'this' context\n    this.generateText = this.generateText.bind(this);\n    this.streamText = this.streamText.bind(this);\n    this.generateObject = this.generateObject.bind(this);\n    this.streamObject = this.streamObject.bind(this);\n    this.toMessage = this.toMessage.bind(this);\n    this.createStepFromChunk = this.createStepFromChunk.bind(this);\n    this.getModelIdentifier = this.getModelIdentifier.bind(this);\n  }\n\n  getModelIdentifier = (model: LanguageModelV1): string => {\n    return model.modelId;\n  };\n\n  toMessage = (message: BaseMessage): CoreMessage => {\n    return message as CoreMessage;\n  };\n\n  createStepFromChunk = (chunk: {\n    type: string;\n    [key: string]: any;\n  }): StepWithContent | null => {\n    if (chunk.type === \"text\" && chunk.text) {\n      return {\n        id: \"\",\n        type: \"text\",\n        content: chunk.text,\n        role: \"assistant\" as MessageRole,\n        usage: chunk.usage || undefined,\n      };\n    }\n\n    if (chunk.type === \"tool-call\" || chunk.type === \"tool_call\") {\n      return {\n        id: chunk.toolCallId,\n        type: \"tool_call\",\n        name: chunk.toolName,\n        arguments: chunk.args,\n        content: JSON.stringify([\n          {\n            type: \"tool-call\",\n            toolCallId: chunk.toolCallId,\n            toolName: chunk.toolName,\n            args: chunk.args,\n          },\n        ]),\n        role: \"assistant\" as MessageRole,\n        usage: chunk.usage || undefined,\n      };\n    }\n\n    if (chunk.type === \"tool-result\" || chunk.type === \"tool_result\") {\n      return {\n        id: chunk.toolCallId,\n        type: \"tool_result\",\n        name: chunk.toolName,\n        result: chunk.result,\n        content: JSON.stringify([\n          {\n            type: \"tool-result\",\n            toolCallId: chunk.toolCallId,\n            result: chunk.result,\n          },\n        ]),\n        role: \"assistant\" as MessageRole,\n        usage: chunk.usage || undefined,\n      };\n    }\n\n    return null;\n  };\n\n  /**\n   * Creates a standardized VoltAgentError from a raw Vercel SDK error object.\n   */\n  private _createVoltagentErrorFromSdkError(\n    sdkError: any, // The raw error object from the SDK\n    errorStage:\n      | \"llm_stream\"\n      | \"object_stream\"\n      | \"llm_generate\"\n      | \"object_generate\"\n      | \"tool_execution\" = \"llm_stream\",\n  ): VoltAgentError {\n    const originalError = sdkError.error ?? sdkError; // Handle potential nesting\n    let voltagentErr: VoltAgentError;\n\n    const potentialToolCallId = (originalError as any)?.toolCallId;\n    const potentialToolName = (originalError as any)?.toolName;\n\n    if (potentialToolCallId && potentialToolName) {\n      const toolErrorDetails: ToolErrorInfo = {\n        toolCallId: potentialToolCallId,\n        toolName: potentialToolName,\n        toolArguments: (originalError as any)?.args,\n        toolExecutionError: originalError,\n      };\n      voltagentErr = {\n        message: `Error during Vercel SDK operation (tool '${potentialToolName}'): ${originalError instanceof Error ? originalError.message : \"Unknown tool error\"}`,\n        originalError: originalError,\n        toolError: toolErrorDetails,\n        stage: \"tool_execution\",\n        code: (originalError as any)?.code,\n      };\n    } else {\n      voltagentErr = {\n        message:\n          originalError instanceof Error\n            ? originalError.message\n            : `An unknown error occurred during Vercel AI operation (stage: ${errorStage})`,\n        originalError: originalError,\n        toolError: undefined,\n        stage: errorStage,\n        code: (originalError as any)?.code,\n      };\n    }\n    // Return the created error instead of calling callback\n    return voltagentErr;\n  }\n\n  generateText = async (\n    options: GenerateTextOptions<LanguageModelV1>,\n  ): Promise<ProviderTextResponse<GenerateTextResult<Record<string, any>, never>>> => {\n    const vercelMessages = options.messages.map(this.toMessage);\n    const vercelTools = options.tools ? convertToolsForSDK(options.tools) : undefined;\n\n    // Process onStepFinish if provided\n    const onStepFinish = options.onStepFinish\n      ? async (result: StepResult<Record<string, any>>) => {\n          if (options.onStepFinish) {\n            // Handle text response\n            if (result.text) {\n              const step = this.createStepFromChunk({\n                type: \"text\",\n                text: result.text,\n                usage: result.usage,\n              });\n              if (step) await options.onStepFinish(step);\n            }\n\n            // Handle all tool calls - each as a separate step\n            if (result.toolCalls && result.toolCalls.length > 0) {\n              for (const toolCall of result.toolCalls) {\n                const step = this.createStepFromChunk({\n                  type: \"tool-call\",\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  args: toolCall.args,\n                  usage: result.usage,\n                });\n                if (step) await options.onStepFinish(step);\n              }\n            }\n\n            // Handle all tool results - each as a separate step\n            if (result.toolResults && result.toolResults.length > 0) {\n              for (const toolResult of result.toolResults) {\n                const step = this.createStepFromChunk({\n                  type: \"tool-result\",\n                  toolCallId: toolResult.toolCallId,\n                  toolName: toolResult.toolName,\n                  result: toolResult.result,\n                  usage: result.usage,\n                });\n                if (step) await options.onStepFinish(step);\n              }\n            }\n          }\n        }\n      : undefined;\n\n    try {\n      const result = await generateText({\n        ...options.provider,\n        messages: vercelMessages,\n        model: options.model,\n        tools: vercelTools,\n        maxSteps: options.maxSteps,\n        abortSignal: options.signal,\n        onStepFinish,\n      });\n\n      // Return standardized response\n      return {\n        provider: result,\n        text: result.text || \"\",\n        usage: result.usage\n          ? {\n              promptTokens: result.usage.promptTokens,\n              completionTokens: result.usage.completionTokens,\n              totalTokens: result.usage.totalTokens,\n            }\n          : undefined,\n        toolCalls: result.toolCalls,\n        toolResults: result.toolResults,\n        finishReason: result.finishReason,\n      };\n    } catch (sdkError) {\n      // Create VoltAgentError using the helper\n      const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, \"llm_generate\"); // Use appropriate stage\n      // Throw the standardized error\n      throw voltagentErr;\n    }\n  };\n\n  async streamText(\n    options: StreamTextOptions<LanguageModelV1>,\n  ): Promise<ProviderTextStreamResponse<StreamTextResult<Record<string, any>, never>>> {\n    const vercelMessages = options.messages.map(this.toMessage);\n    const vercelTools = options.tools ? convertToolsForSDK(options.tools) : undefined;\n\n    // Process onStepFinish if provided\n    const onStepFinish = options.onStepFinish\n      ? async (result: StepResult<Record<string, any>>) => {\n          if (options.onStepFinish) {\n            // Handle text response\n            if (result.text) {\n              const step = this.createStepFromChunk({\n                type: \"text\",\n                text: result.text,\n                usage: result.usage,\n              });\n              if (step) await options.onStepFinish(step);\n            }\n\n            // Handle all tool calls - each as a separate step\n            if (result.toolCalls && result.toolCalls.length > 0) {\n              for (const toolCall of result.toolCalls) {\n                const step = this.createStepFromChunk({\n                  type: \"tool-call\",\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  args: toolCall.args,\n                  usage: result.usage,\n                });\n                if (step) await options.onStepFinish(step);\n              }\n            }\n\n            // Handle all tool results - each as a separate step\n            if (result.toolResults && result.toolResults.length > 0) {\n              for (const toolResult of result.toolResults) {\n                const step = this.createStepFromChunk({\n                  type: \"tool-result\",\n                  toolCallId: toolResult.toolCallId,\n                  toolName: toolResult.toolName,\n                  result: toolResult.result,\n                  usage: result.usage,\n                });\n                if (step) await options.onStepFinish(step);\n              }\n            }\n          }\n        }\n      : undefined;\n\n    const result = streamText({\n      ...options.provider,\n      messages: vercelMessages,\n      model: options.model,\n      tools: vercelTools,\n      maxSteps: options.maxSteps,\n      abortSignal: options.signal,\n      onStepFinish,\n      onChunk: async ({ chunk }) => {\n        if (options?.onChunk) {\n          // Handle the chunk directly without usage tracking\n          const step = this.createStepFromChunk(chunk);\n          if (step) await options.onChunk(step);\n        }\n      },\n      onFinish: options.onFinish\n        ? async (\n            result: Omit<StepResult<Record<string, any>>, \"stepType\" | \"isContinued\"> & {\n              readonly steps: StepResult<Record<string, any>>[];\n            },\n          ) => {\n            options.onFinish?.({\n              text: result.text,\n              usage: result.usage,\n              finishReason: result.finishReason,\n              warnings: result.warnings,\n              providerResponse: result,\n            });\n          }\n        : undefined,\n      onError: (sdkError) => {\n        // Create the error using the helper\n        const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, \"llm_stream\");\n        // Call the agent's onError callback if it exists\n        if (options.onError) {\n          options.onError(voltagentErr);\n        }\n      },\n    });\n\n    // Return only provider and textStream\n    return {\n      provider: result,\n      textStream: result.textStream as any,\n    };\n  }\n\n  generateObject = async <TSchema extends z.ZodType>(\n    options: GenerateObjectOptions<LanguageModelV1, TSchema>,\n  ): Promise<ProviderObjectResponse<GenerateObjectResult<z.infer<TSchema>>, z.infer<TSchema>>> => {\n    const vercelMessages = options.messages.map(this.toMessage);\n\n    // For object generation, we use onFinish as onStepFinish is not supported\n    const onFinish = options.onStepFinish\n      ? async (result: {\n          object: z.infer<TSchema>;\n          finishReason: FinishReason;\n          usage: LanguageModelUsage;\n          warnings: CallWarning[] | undefined;\n          request: LanguageModelRequestMetadata;\n          response: LanguageModelResponseMetadata;\n          logprobs: any | undefined;\n          providerMetadata: ProviderMetadata | undefined;\n        }) => {\n          if (options.onStepFinish) {\n            const jsonResult =\n              typeof result.object === \"string\" ? result.object : JSON.stringify(result.object);\n\n            // Create a step with usage information directly passed\n            const step = this.createStepFromChunk({\n              type: \"text\",\n              text: jsonResult,\n              usage: result.usage,\n            });\n\n            if (step) await options.onStepFinish(step);\n          }\n        }\n      : undefined;\n\n    try {\n      const result = await generateObject({\n        ...options.provider,\n        messages: vercelMessages,\n        model: options.model,\n        schema: options.schema,\n        abortSignal: options.signal,\n      });\n\n      // Call the custom onFinish handler if defined\n      await onFinish?.(result);\n\n      // Return standardized response\n      return {\n        provider: result,\n        object: result.object,\n        usage: result.usage\n          ? {\n              promptTokens: result.usage.promptTokens,\n              completionTokens: result.usage.completionTokens,\n              totalTokens: result.usage.totalTokens,\n            }\n          : undefined,\n        finishReason: result.finishReason,\n      };\n    } catch (sdkError) {\n      // Create VoltAgentError using the helper\n      const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, \"object_generate\"); // Use appropriate stage\n      // Throw the standardized error\n      throw voltagentErr;\n    }\n  };\n\n  async streamObject<TSchema extends z.ZodType>(\n    options: StreamObjectOptions<LanguageModelV1, TSchema>,\n  ): Promise<\n    ProviderObjectStreamResponse<\n      StreamObjectResult<z.infer<TSchema>, unknown, never>,\n      z.infer<TSchema>\n    >\n  > {\n    const vercelMessages = options.messages.map(this.toMessage);\n\n    // Define the onFinish handler to be passed to the Vercel SDK\n    const sdkOnFinish = async (event: {\n      // Type for Vercel SDK event\n      object: z.infer<TSchema> | undefined;\n      error: unknown | undefined; // Handle potential error in event?\n      usage: LanguageModelUsage;\n      response: LanguageModelResponseMetadata;\n      warnings?: CallWarning[];\n      providerMetadata: ProviderMetadata | undefined;\n    }) => {\n      // --- Handle onStepFinish simulation (if provided by Agent) ---\n      // This uses the final object/usage info from the finish event\n      if (options.onStepFinish) {\n        const jsonResult = event.object ? JSON.stringify(event.object) : \"\";\n        const step = this.createStepFromChunk({\n          type: \"text\", // Simulate as a text step containing the final JSON\n          text: jsonResult,\n          usage: event.usage, // Use usage from the event\n        });\n        if (step) await options.onStepFinish(step);\n      }\n      // --- End handle onStepFinish simulation ---\n\n      // --- Handle onFinish callback (if provided by Agent) ---\n      if (options.onFinish && event.object) {\n        // Check if Agent wants onFinish and object exists\n        let mappedUsage: UsageInfo | undefined = undefined;\n        if (event.usage) {\n          mappedUsage = {\n            promptTokens: event.usage.promptTokens,\n            completionTokens: event.usage.completionTokens,\n            totalTokens: event.usage.totalTokens,\n          };\n        }\n        // Construct the standardized result object\n        const finishResult: StreamObjectFinishResult<z.infer<TSchema>> = {\n          object: event.object, // The final object from the event\n          usage: mappedUsage, // Mapped usage info\n          warnings: event.warnings,\n          providerResponse: event, // Include the original SDK event object\n          // finishReason is not typically available in Vercel's streamObject finish event\n        };\n        // Call the agent's onFinish with the standardized result\n        await options.onFinish(finishResult);\n      }\n      // --- End handle onFinish callback ---\n    };\n\n    const result = streamObject({\n      ...options.provider,\n      messages: vercelMessages,\n      model: options.model,\n      schema: options.schema,\n      abortSignal: options.signal,\n      // Pass the correctly defined sdkOnFinish handler\n      // Only pass it if either onStepFinish or onFinish is provided by the agent\n      ...(options.onStepFinish || options.onFinish ? { onFinish: sdkOnFinish } : {}),\n      onError: (sdkError) => {\n        // Create the error using the helper\n        const voltagentErr = this._createVoltagentErrorFromSdkError(sdkError, \"object_stream\");\n        // Call the agent's onError callback if it exists\n        if (options.onError) {\n          options.onError(voltagentErr);\n        }\n      },\n    });\n\n    // TODO: Add usage to the result - https://sdk.vercel.ai/docs/reference/ai-sdk-core/stream-object\n\n    const partialObjectStream = result.partialObjectStream;\n    // Return only provider and objectStream\n    return {\n      provider: { ...result, partialObjectStream },\n      objectStream: partialObjectStream,\n    };\n  }\n}\n", "import { tool } from \"ai\";\n\n/**\n * Convert VoltAgent tools to Vercel AI SDK format\n * @param tools Array of agent tools\n * @returns Object mapping tool names to their SDK implementations or undefined if no tools\n */\nexport const convertToolsForSDK = (tools: any[]): Record<string, any> | undefined => {\n  if (!tools || tools.length === 0) {\n    return undefined;\n  }\n\n  const toolsMap: Record<string, any> = {};\n\n  for (const agentTool of tools) {\n    // Wrap the tool with Vercel AI SDK's tool helper\n    const sdkTool = tool({\n      description: agentTool.description,\n      parameters: agentTool.parameters,\n      execute: agentTool.execute,\n    });\n\n    toolsMap[agentTool.name] = sdkTool;\n  }\n\n  return toolsMap;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,SAAS,gBAAgB,cAAc,cAAc,kBAAkB;;;AClCvE,SAAS,YAAY;AAOd,IAAM,qBAAqB,wBAAC,UAAkD;AACnF,MAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,QAAM,WAAgC,CAAC;AAEvC,aAAW,aAAa,OAAO;AAE7B,UAAM,UAAU,KAAK;AAAA,MACnB,aAAa,UAAU;AAAA,MACvB,YAAY,UAAU;AAAA,MACtB,SAAS,UAAU;AAAA,IACrB,CAAC;AAED,aAAS,UAAU,IAAI,IAAI;AAAA,EAC7B;AAEA,SAAO;AACT,GAnBkC;;;ADgC3B,IAAM,mBAAN,MAA+D;AAAA;AAAA,EAEpE,YAAoB,SAAiC;AAAjC;AAWpB,8CAAqB,wBAAC,UAAmC;AACvD,aAAO,MAAM;AAAA,IACf,GAFqB;AAIrB,qCAAY,wBAAC,YAAsC;AACjD,aAAO;AAAA,IACT,GAFY;AAIZ,+CAAsB,wBAAC,UAGO;AAC5B,UAAI,MAAM,SAAS,UAAU,MAAM,MAAM;AACvC,eAAO;AAAA,UACL,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS,MAAM;AAAA,UACf,MAAM;AAAA,UACN,OAAO,MAAM,SAAS;AAAA,QACxB;AAAA,MACF;AAEA,UAAI,MAAM,SAAS,eAAe,MAAM,SAAS,aAAa;AAC5D,eAAO;AAAA,UACL,IAAI,MAAM;AAAA,UACV,MAAM;AAAA,UACN,MAAM,MAAM;AAAA,UACZ,WAAW,MAAM;AAAA,UACjB,SAAS,KAAK,UAAU;AAAA,YACtB;AAAA,cACE,MAAM;AAAA,cACN,YAAY,MAAM;AAAA,cAClB,UAAU,MAAM;AAAA,cAChB,MAAM,MAAM;AAAA,YACd;AAAA,UACF,CAAC;AAAA,UACD,MAAM;AAAA,UACN,OAAO,MAAM,SAAS;AAAA,QACxB;AAAA,MACF;AAEA,UAAI,MAAM,SAAS,iBAAiB,MAAM,SAAS,eAAe;AAChE,eAAO;AAAA,UACL,IAAI,MAAM;AAAA,UACV,MAAM;AAAA,UACN,MAAM,MAAM;AAAA,UACZ,QAAQ,MAAM;AAAA,UACd,SAAS,KAAK,UAAU;AAAA,YACtB;AAAA,cACE,MAAM;AAAA,cACN,YAAY,MAAM;AAAA,cAClB,QAAQ,MAAM;AAAA,YAChB;AAAA,UACF,CAAC;AAAA,UACD,MAAM;AAAA,UACN,OAAO,MAAM,SAAS;AAAA,QACxB;AAAA,MACF;AAEA,aAAO;AAAA,IACT,GApDsB;AAsGtB,wCAAe,wBACb,YACkF;AAClF,YAAM,iBAAiB,QAAQ,SAAS,IAAI,KAAK,SAAS;AAC1D,YAAM,cAAc,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAGxE,YAAM,eAAe,QAAQ,eACzB,CAAO,WAA4C;AACjD,YAAI,QAAQ,cAAc;AAExB,cAAI,OAAO,MAAM;AACf,kBAAM,OAAO,KAAK,oBAAoB;AAAA,cACpC,MAAM;AAAA,cACN,MAAM,OAAO;AAAA,cACb,OAAO,OAAO;AAAA,YAChB,CAAC;AACD,gBAAI;AAAM,oBAAM,QAAQ,aAAa,IAAI;AAAA,UAC3C;AAGA,cAAI,OAAO,aAAa,OAAO,UAAU,SAAS,GAAG;AACnD,uBAAW,YAAY,OAAO,WAAW;AACvC,oBAAM,OAAO,KAAK,oBAAoB;AAAA,gBACpC,MAAM;AAAA,gBACN,YAAY,SAAS;AAAA,gBACrB,UAAU,SAAS;AAAA,gBACnB,MAAM,SAAS;AAAA,gBACf,OAAO,OAAO;AAAA,cAChB,CAAC;AACD,kBAAI;AAAM,sBAAM,QAAQ,aAAa,IAAI;AAAA,YAC3C;AAAA,UACF;AAGA,cAAI,OAAO,eAAe,OAAO,YAAY,SAAS,GAAG;AACvD,uBAAW,cAAc,OAAO,aAAa;AAC3C,oBAAM,OAAO,KAAK,oBAAoB;AAAA,gBACpC,MAAM;AAAA,gBACN,YAAY,WAAW;AAAA,gBACvB,UAAU,WAAW;AAAA,gBACrB,QAAQ,WAAW;AAAA,gBACnB,OAAO,OAAO;AAAA,cAChB,CAAC;AACD,kBAAI;AAAM,sBAAM,QAAQ,aAAa,IAAI;AAAA,YAC3C;AAAA,UACF;AAAA,QACF;AAAA,MACF,KACA;AAEJ,UAAI;AACF,cAAM,SAAS,MAAM,aAAa,iCAC7B,QAAQ,WADqB;AAAA,UAEhC,UAAU;AAAA,UACV,OAAO,QAAQ;AAAA,UACf,OAAO;AAAA,UACP,UAAU,QAAQ;AAAA,UAClB,aAAa,QAAQ;AAAA,UACrB;AAAA,QACF,EAAC;AAGD,eAAO;AAAA,UACL,UAAU;AAAA,UACV,MAAM,OAAO,QAAQ;AAAA,UACrB,OAAO,OAAO,QACV;AAAA,YACE,cAAc,OAAO,MAAM;AAAA,YAC3B,kBAAkB,OAAO,MAAM;AAAA,YAC/B,aAAa,OAAO,MAAM;AAAA,UAC5B,IACA;AAAA,UACJ,WAAW,OAAO;AAAA,UAClB,aAAa,OAAO;AAAA,UACpB,cAAc,OAAO;AAAA,QACvB;AAAA,MACF,SAAS,UAAP;AAEA,cAAM,eAAe,KAAK,kCAAkC,UAAU,cAAc;AAEpF,cAAM;AAAA,MACR;AAAA,IACF,IAnFe;AAuLf,0CAAiB,wBACf,YAC8F;AAC9F,YAAM,iBAAiB,QAAQ,SAAS,IAAI,KAAK,SAAS;AAG1D,YAAM,WAAW,QAAQ,eACrB,CAAO,WASD;AACJ,YAAI,QAAQ,cAAc;AACxB,gBAAM,aACJ,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS,KAAK,UAAU,OAAO,MAAM;AAGlF,gBAAM,OAAO,KAAK,oBAAoB;AAAA,YACpC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO,OAAO;AAAA,UAChB,CAAC;AAED,cAAI;AAAM,kBAAM,QAAQ,aAAa,IAAI;AAAA,QAC3C;AAAA,MACF,KACA;AAEJ,UAAI;AACF,cAAM,SAAS,MAAM,eAAe,iCAC/B,QAAQ,WADuB;AAAA,UAElC,UAAU;AAAA,UACV,OAAO,QAAQ;AAAA,UACf,QAAQ,QAAQ;AAAA,UAChB,aAAa,QAAQ;AAAA,QACvB,EAAC;AAGD,cAAM,qCAAW;AAGjB,eAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ,OAAO;AAAA,UACf,OAAO,OAAO,QACV;AAAA,YACE,cAAc,OAAO,MAAM;AAAA,YAC3B,kBAAkB,OAAO,MAAM;AAAA,YAC/B,aAAa,OAAO,MAAM;AAAA,UAC5B,IACA;AAAA,UACJ,cAAc,OAAO;AAAA,QACvB;AAAA,MACF,SAAS,UAAP;AAEA,cAAM,eAAe,KAAK,kCAAkC,UAAU,iBAAiB;AAEvF,cAAM;AAAA,MACR;AAAA,IACF,IAhEiB;AA9Sf,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAmEQ,kCACN,UACA,aAKuB,cACP;AA7HpB;AA8HI,UAAM,iBAAgB,cAAS,UAAT,YAAkB;AACxC,QAAI;AAEJ,UAAM,sBAAuB,+CAAuB;AACpD,UAAM,oBAAqB,+CAAuB;AAElD,QAAI,uBAAuB,mBAAmB;AAC5C,YAAM,mBAAkC;AAAA,QACtC,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,eAAgB,+CAAuB;AAAA,QACvC,oBAAoB;AAAA,MACtB;AACA,qBAAe;AAAA,QACb,SAAS,4CAA4C,wBAAwB,yBAAyB,QAAQ,cAAc,UAAU;AAAA,QACtI;AAAA,QACA,WAAW;AAAA,QACX,OAAO;AAAA,QACP,MAAO,+CAAuB;AAAA,MAChC;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,QACb,SACE,yBAAyB,QACrB,cAAc,UACd,gEAAgE;AAAA,QACtE;AAAA,QACA,WAAW;AAAA,QACX,OAAO;AAAA,QACP,MAAO,+CAAuB;AAAA,MAChC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAuFM,WACJ,SACmF;AAAA;AACnF,YAAM,iBAAiB,QAAQ,SAAS,IAAI,KAAK,SAAS;AAC1D,YAAM,cAAc,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAGxE,YAAM,eAAe,QAAQ,eACzB,CAAOA,YAA4C;AACjD,YAAI,QAAQ,cAAc;AAExB,cAAIA,QAAO,MAAM;AACf,kBAAM,OAAO,KAAK,oBAAoB;AAAA,cACpC,MAAM;AAAA,cACN,MAAMA,QAAO;AAAA,cACb,OAAOA,QAAO;AAAA,YAChB,CAAC;AACD,gBAAI;AAAM,oBAAM,QAAQ,aAAa,IAAI;AAAA,UAC3C;AAGA,cAAIA,QAAO,aAAaA,QAAO,UAAU,SAAS,GAAG;AACnD,uBAAW,YAAYA,QAAO,WAAW;AACvC,oBAAM,OAAO,KAAK,oBAAoB;AAAA,gBACpC,MAAM;AAAA,gBACN,YAAY,SAAS;AAAA,gBACrB,UAAU,SAAS;AAAA,gBACnB,MAAM,SAAS;AAAA,gBACf,OAAOA,QAAO;AAAA,cAChB,CAAC;AACD,kBAAI;AAAM,sBAAM,QAAQ,aAAa,IAAI;AAAA,YAC3C;AAAA,UACF;AAGA,cAAIA,QAAO,eAAeA,QAAO,YAAY,SAAS,GAAG;AACvD,uBAAW,cAAcA,QAAO,aAAa;AAC3C,oBAAM,OAAO,KAAK,oBAAoB;AAAA,gBACpC,MAAM;AAAA,gBACN,YAAY,WAAW;AAAA,gBACvB,UAAU,WAAW;AAAA,gBACrB,QAAQ,WAAW;AAAA,gBACnB,OAAOA,QAAO;AAAA,cAChB,CAAC;AACD,kBAAI;AAAM,sBAAM,QAAQ,aAAa,IAAI;AAAA,YAC3C;AAAA,UACF;AAAA,QACF;AAAA,MACF,KACA;AAEJ,YAAM,SAAS,WAAW,iCACrB,QAAQ,WADa;AAAA,QAExB,UAAU;AAAA,QACV,OAAO,QAAQ;AAAA,QACf,OAAO;AAAA,QACP,UAAU,QAAQ;AAAA,QAClB,aAAa,QAAQ;AAAA,QACrB;AAAA,QACA,SAAS,CAAO,OAAc,eAAd,KAAc,WAAd,EAAE,MAAM,GAAM;AAC5B,cAAI,mCAAS,SAAS;AAEpB,kBAAM,OAAO,KAAK,oBAAoB,KAAK;AAC3C,gBAAI;AAAM,oBAAM,QAAQ,QAAQ,IAAI;AAAA,UACtC;AAAA,QACF;AAAA,QACA,UAAU,QAAQ,WACd,CACEA,YAGG;AA9Tf;AA+TY,wBAAQ,aAAR,iCAAmB;AAAA,YACjB,MAAMA,QAAO;AAAA,YACb,OAAOA,QAAO;AAAA,YACd,cAAcA,QAAO;AAAA,YACrB,UAAUA,QAAO;AAAA,YACjB,kBAAkBA;AAAA,UACpB;AAAA,QACF,KACA;AAAA,QACJ,SAAS,CAAC,aAAa;AAErB,gBAAM,eAAe,KAAK,kCAAkC,UAAU,YAAY;AAElF,cAAI,QAAQ,SAAS;AACnB,oBAAQ,QAAQ,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF,EAAC;AAGD,aAAO;AAAA,QACL,UAAU;AAAA,QACV,YAAY,OAAO;AAAA,MACrB;AAAA,IACF;AAAA;AAAA,EAoEM,aACJ,SAMA;AAAA;AACA,YAAM,iBAAiB,QAAQ,SAAS,IAAI,KAAK,SAAS;AAG1D,YAAM,cAAc,wBAAO,UAQrB;AAGJ,YAAI,QAAQ,cAAc;AACxB,gBAAM,aAAa,MAAM,SAAS,KAAK,UAAU,MAAM,MAAM,IAAI;AACjE,gBAAM,OAAO,KAAK,oBAAoB;AAAA,YACpC,MAAM;AAAA;AAAA,YACN,MAAM;AAAA,YACN,OAAO,MAAM;AAAA;AAAA,UACf,CAAC;AACD,cAAI;AAAM,kBAAM,QAAQ,aAAa,IAAI;AAAA,QAC3C;AAIA,YAAI,QAAQ,YAAY,MAAM,QAAQ;AAEpC,cAAI,cAAqC;AACzC,cAAI,MAAM,OAAO;AACf,0BAAc;AAAA,cACZ,cAAc,MAAM,MAAM;AAAA,cAC1B,kBAAkB,MAAM,MAAM;AAAA,cAC9B,aAAa,MAAM,MAAM;AAAA,YAC3B;AAAA,UACF;AAEA,gBAAM,eAA2D;AAAA,YAC/D,QAAQ,MAAM;AAAA;AAAA,YACd,OAAO;AAAA;AAAA,YACP,UAAU,MAAM;AAAA,YAChB,kBAAkB;AAAA;AAAA;AAAA,UAEpB;AAEA,gBAAM,QAAQ,SAAS,YAAY;AAAA,QACrC;AAAA,MAEF,IA7CoB;AA+CpB,YAAM,SAAS,aAAa,8DACvB,QAAQ,WADe;AAAA,QAE1B,UAAU;AAAA,QACV,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,aAAa,QAAQ;AAAA,UAGjB,QAAQ,gBAAgB,QAAQ,WAAW,EAAE,UAAU,YAAY,IAAI,CAAC,IARlD;AAAA,QAS1B,SAAS,CAAC,aAAa;AAErB,gBAAM,eAAe,KAAK,kCAAkC,UAAU,eAAe;AAErF,cAAI,QAAQ,SAAS;AACnB,oBAAQ,QAAQ,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF,EAAC;AAID,YAAM,sBAAsB,OAAO;AAEnC,aAAO;AAAA,QACL,UAAU,iCAAK,SAAL,EAAa,oBAAoB;AAAA,QAC3C,cAAc;AAAA,MAChB;AAAA,IACF;AAAA;AACF;AA1ca;", "names": ["result"]}