#!/usr/bin/env node
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  runCLI: () => runCLI
});
module.exports = __toCommonJS(src_exports);
var import_commander = require("commander");
var import_chalk5 = __toESM(require("chalk"));
var import_figlet = __toESM(require("figlet"));

// src/commands/init.ts
var import_chalk = __toESM(require("chalk"));
var import_fs = __toESM(require("fs"));
var import_path = __toESM(require("path"));
var import_child_process = require("child_process");

// src/utils/analytics.ts
var import_posthog_node = require("posthog-node");
var import_uuid = require("uuid");
var import_os = __toESM(require("os"));
var import_crypto = __toESM(require("crypto"));
var isTelemetryDisabled = () => {
  return process.env.VOLTAGENT_TELEMETRY_DISABLED === "1" || process.env.VOLTAGENT_TELEMETRY_DISABLED === "true";
};
var client = new import_posthog_node.PostHog("phc_cLPjGbbZ9BdRtLG3cxoHNch3ZJnQvNhXCHRkeWUI6z5", {
  host: "https://us.i.posthog.com",
  flushAt: 1,
  flushInterval: 0,
  disableGeoip: false
});
var getMachineId = () => {
  try {
    const hostname = import_os.default.hostname();
    const cpus = import_os.default.cpus().length;
    const platform = import_os.default.platform();
    const arch = import_os.default.arch();
    const dataToHash = `${hostname}-${cpus}-${platform}-${arch}`;
    return import_crypto.default.createHash("sha256").update(dataToHash).digest("hex").substring(0, 32);
  } catch (error) {
    return (0, import_uuid.v4)();
  }
};
var getOSInfo = () => {
  try {
    return {
      os_platform: import_os.default.platform(),
      os_release: import_os.default.release(),
      os_version: import_os.default.version(),
      os_arch: import_os.default.arch()
    };
  } catch (error) {
    return {
      os_platform: "unknown",
      os_release: "unknown",
      os_version: "unknown",
      os_arch: "unknown"
    };
  }
};
var captureInitEvent = (options) => {
  if (isTelemetryDisabled())
    return;
  client.capture({
    distinctId: getMachineId(),
    event: "cli_init",
    properties: {
      package_manager: options.packageManager,
      machine_id: getMachineId(),
      ...getOSInfo()
    }
  });
};
var captureUpdateEvent = (options) => {
  if (isTelemetryDisabled())
    return;
  client.capture({
    distinctId: getMachineId(),
    event: "cli_update_check",
    properties: {
      had_updates: options.hadUpdates,
      machine_id: getMachineId(),
      ...getOSInfo()
    }
  });
};
var captureError = (options) => {
  if (isTelemetryDisabled())
    return;
  client.capture({
    distinctId: getMachineId(),
    event: "cli_error",
    properties: {
      command: options.command,
      error_message: options.errorMessage,
      machine_id: getMachineId(),
      ...getOSInfo()
    }
  });
};
var captureWhoamiEvent = (options) => {
  if (isTelemetryDisabled())
    return;
  client.capture({
    distinctId: getMachineId(),
    event: "cli_whoami",
    properties: {
      num_volt_packages: options.numVoltPackages,
      machine_id: getMachineId(),
      ...getOSInfo()
    }
  });
};
var analytics_default = client;

// src/commands/init.ts
var registerInitCommand = (program) => {
  program.command("init").description("Integrate VoltAgent CLI into a project").action(async () => {
    try {
      console.log(import_chalk.default.cyan("Integrating VoltAgent CLI into your project..."));
      const packageJsonPath = import_path.default.join(process.cwd(), "package.json");
      if (!import_fs.default.existsSync(packageJsonPath)) {
        console.error(
          import_chalk.default.red(
            "Error: package.json file not found. This command must be run inside a Node.js project."
          )
        );
        process.exit(1);
      }
      const packageJson = JSON.parse(import_fs.default.readFileSync(packageJsonPath, "utf-8"));
      const scripts = packageJson.scripts || {};
      let packageManager = "npm";
      try {
        if (import_fs.default.existsSync(import_path.default.join(process.cwd(), "pnpm-lock.yaml"))) {
          packageManager = "pnpm";
        } else if (import_fs.default.existsSync(import_path.default.join(process.cwd(), "yarn.lock"))) {
          packageManager = "yarn";
        }
      } catch (error) {
        packageManager = "npm";
      }
      console.log(import_chalk.default.blue(`Detected package manager: ${packageManager}`));
      let modified = false;
      if (!scripts["volt"] || scripts["volt"] !== "volt") {
        scripts["volt"] = "volt";
        modified = true;
      }
      if (!modified) {
        console.log(import_chalk.default.yellow("No changes made. The 'volt' script is already configured."));
      } else {
        packageJson.scripts = scripts;
        import_fs.default.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        console.log(import_chalk.default.green("\u2713 Added 'volt' script to package.json"));
      }
      let isPackageInstalled = false;
      try {
        import_fs.default.accessSync(
          import_path.default.join(process.cwd(), "node_modules", "@voltagent", "cli"),
          import_fs.default.constants.F_OK
        );
        isPackageInstalled = true;
      } catch (error) {
        isPackageInstalled = false;
      }
      if (!isPackageInstalled) {
        console.log(import_chalk.default.cyan("Installing @voltagent/cli locally..."));
        try {
          const installCommand = packageManager === "yarn" ? "yarn add @voltagent/cli --dev" : packageManager === "pnpm" ? "pnpm add @voltagent/cli --save-dev" : "npm install @voltagent/cli --save-dev";
          (0, import_child_process.execSync)(installCommand, { stdio: "inherit" });
          console.log(import_chalk.default.green("\u2713 @voltagent/cli successfully installed!"));
        } catch (error) {
          console.error(
            import_chalk.default.red("Failed to install @voltagent/cli:"),
            error instanceof Error ? error.message : String(error)
          );
        }
      }
      const voltagentDir = import_path.default.join(process.cwd(), ".voltagent");
      if (!import_fs.default.existsSync(voltagentDir)) {
        import_fs.default.mkdirSync(voltagentDir);
      }
      console.log(import_chalk.default.green("\u2713 VoltAgent CLI successfully integrated!"));
      console.log("\n", import_chalk.default.cyan("To run VoltAgent:"), import_chalk.default.green("npm run volt"));
      captureInitEvent({
        packageManager
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(import_chalk.default.red("Error:"), errorMessage);
      captureError({
        command: "init",
        errorMessage
      });
      process.exit(1);
    }
  });
};

// src/commands/update.ts
var import_chalk2 = __toESM(require("chalk"));
var import_ora = __toESM(require("ora"));
var import_path2 = __toESM(require("path"));
var import_fs2 = __toESM(require("fs"));
var ncuPackage = __toESM(require("npm-check-updates"));
var import_inquirer = __toESM(require("inquirer"));
var checkForUpdates = async (packagePath, options) => {
  try {
    const rootDir = packagePath ? import_path2.default.dirname(packagePath) : process.cwd();
    const packageJsonPath = packagePath || import_path2.default.join(rootDir, "package.json");
    if (!import_fs2.default.existsSync(packageJsonPath)) {
      return {
        hasUpdates: false,
        updates: {},
        count: 0,
        message: "Could not find package.json"
      };
    }
    const result = await ncuPackage.default({
      packageFile: packageJsonPath,
      silent: true,
      jsonUpgraded: true,
      filter: options?.filter
    });
    const updates = result;
    const count = Object.keys(updates).length;
    if (count > 0) {
      const updatesList = Object.entries(updates).map(([name, version]) => `  - ${name} \u2192 ${version}`).join("\n");
      return {
        hasUpdates: true,
        updates,
        count,
        message: `Found ${count} outdated packages:
${updatesList}`
      };
    } else {
      return {
        hasUpdates: false,
        updates: {},
        count: 0,
        message: "All packages are up to date"
      };
    }
  } catch (error) {
    console.error("Error checking for updates:", error);
    return {
      hasUpdates: false,
      updates: {},
      count: 0,
      message: `Error checking for updates: ${error instanceof Error ? error.message : String(error)}`
    };
  }
};
var interactiveUpdate = async (updates, packagePath) => {
  const rootDir = packagePath ? import_path2.default.dirname(packagePath) : process.cwd();
  const packageJsonPath = packagePath || import_path2.default.join(rootDir, "package.json");
  const choices = Object.entries(updates).map(([name, version]) => {
    return {
      name: `${import_chalk2.default.cyan(name)}: ${import_chalk2.default.gray(getCurrentVersion(name, packageJsonPath))} \u2192 ${import_chalk2.default.green(version)}`,
      value: name,
      short: name
    };
  });
  const { selectedPackages } = await import_inquirer.default.prompt([
    {
      type: "checkbox",
      name: "selectedPackages",
      message: "Select packages to update:",
      choices,
      pageSize: 15,
      default: choices.map((c) => c.value)
      // Default select all
    }
  ]);
  if (selectedPackages.length === 0) {
    console.log(import_chalk2.default.yellow("No packages selected for update."));
    return;
  }
  const selectedFilter = selectedPackages.join(" ");
  console.log(import_chalk2.default.cyan("\nApplying updates for selected packages..."));
  try {
    await ncuPackage.default({
      packageFile: packageJsonPath,
      upgrade: true,
      filter: selectedFilter
    });
    console.log(import_chalk2.default.green(`\u2713 Updated ${selectedPackages.length} packages in package.json`));
    console.log(import_chalk2.default.green("Run 'npm install' to install updated packages"));
  } catch (error) {
    console.error(import_chalk2.default.red("Error applying updates:"));
    console.error(error instanceof Error ? error.message : String(error));
  }
};
var getCurrentVersion = (packageName, packageJsonPath) => {
  try {
    const packageJson = JSON.parse(import_fs2.default.readFileSync(packageJsonPath, "utf8"));
    for (const section of [
      "dependencies",
      "devDependencies",
      "peerDependencies",
      "optionalDependencies"
    ]) {
      if (packageJson[section]?.[packageName]) {
        return packageJson[section][packageName];
      }
    }
    return "unknown";
  } catch (error) {
    return "unknown";
  }
};
var registerUpdateCommand = (program) => {
  program.command("update").description("Interactive update for VoltAgent packages").option("--apply", "Apply updates without interactive mode").action(async (options) => {
    try {
      const spinner = (0, import_ora.default)("Checking for updates...").start();
      const filter = "@voltagent*";
      const updates = await checkForUpdates(void 0, { filter });
      spinner.stop();
      captureUpdateEvent({
        hadUpdates: updates.hasUpdates
      });
      if (!updates.hasUpdates) {
        console.log(import_chalk2.default.green("\u2713 All VoltAgent packages are up to date"));
        return;
      }
      console.log(import_chalk2.default.yellow(`Found ${updates.count} outdated VoltAgent packages:`));
      Object.entries(updates.updates).forEach(([name, version]) => {
        console.log(`  ${import_chalk2.default.cyan(name)}: ${import_chalk2.default.gray("\u2192")} ${import_chalk2.default.green(version)}`);
      });
      if (options.apply) {
        console.log(import_chalk2.default.cyan("\nApplying updates..."));
        try {
          await ncuPackage.default({
            packageFile: import_path2.default.join(process.cwd(), "package.json"),
            upgrade: true,
            filter
          });
          console.log(import_chalk2.default.green("\u2713 Updates applied to package.json"));
          console.log(import_chalk2.default.green("Run 'npm install' to install updated packages"));
          return;
        } catch (error) {
          console.error(import_chalk2.default.red("Error applying updates:"));
          console.error(error instanceof Error ? error.message : String(error));
          return;
        }
      }
      console.log();
      console.log(import_chalk2.default.cyan("Starting interactive update..."));
      await interactiveUpdate(updates.updates);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(import_chalk2.default.red("Error checking for updates:"));
      console.error(errorMessage);
      captureError({
        command: "update",
        errorMessage
      });
    }
  });
};

// src/commands/whoami.ts
var import_chalk3 = __toESM(require("chalk"));
var import_os2 = __toESM(require("os"));
var import_fs3 = __toESM(require("fs"));
var import_path3 = __toESM(require("path"));
var registerWhoamiCommand = (program) => {
  program.command("whoami").description("Display system and user information").action(async () => {
    try {
      console.log(import_chalk3.default.cyan("System and User Information:"));
      console.log(import_chalk3.default.blue(`Username: ${import_os2.default.userInfo().username}`));
      console.log(import_chalk3.default.blue(`Hostname: ${import_os2.default.hostname()}`));
      console.log(import_chalk3.default.blue(`Platform: ${import_os2.default.platform()}`));
      console.log(import_chalk3.default.blue(`OS: ${import_os2.default.type()} ${import_os2.default.release()}`));
      console.log(import_chalk3.default.blue(`Architecture: ${import_os2.default.arch()}`));
      console.log(import_chalk3.default.blue(`Home Directory: ${import_os2.default.homedir()}`));
      console.log(import_chalk3.default.cyan("\nInstalled VoltAgent Packages:"));
      let numVoltPackages = 0;
      const packageJsonPath = import_path3.default.join(process.cwd(), "package.json");
      if (import_fs3.default.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(import_fs3.default.readFileSync(packageJsonPath, "utf-8"));
        const dependencies = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies
        };
        const voltagentPackages = Object.entries(dependencies || {}).filter(([name]) => name.includes("voltagent")).map(([name, version]) => ({ name, version }));
        numVoltPackages = voltagentPackages.length;
        if (voltagentPackages.length > 0) {
          voltagentPackages.forEach((pkg) => {
            console.log(import_chalk3.default.blue(`${pkg.name}: ${pkg.version}`));
          });
        } else {
          console.log(import_chalk3.default.yellow("No VoltAgent packages found in package.json"));
        }
      } else {
        console.log(import_chalk3.default.yellow("No package.json found in current directory"));
      }
      captureWhoamiEvent({
        numVoltPackages
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(import_chalk3.default.red("Error:"), errorMessage);
      captureError({
        command: "whoami",
        errorMessage
      });
      process.exit(1);
    }
  });
};

// src/commands/add.ts
var import_chalk4 = __toESM(require("chalk"));
var registerAddCommand = (program) => {
  program.command("add <agent-slug>").description("Add a VoltAgent agent from the marketplace to your project.").action(async (agentSlug) => {
    try {
      console.log(
        import_chalk4.default.yellow(
          `
\u{1F6A7} The 'add' command is coming soon! This feature will allow you to easily integrate agents like '${agentSlug}' into your project.
`
        )
      );
      console.log(
        import_chalk4.default.cyan("\nWant to be among the first to try it out and shape its development?\n")
      );
      console.log(
        import_chalk4.default.cyan(
          `Join the discussion on GitHub and become an early user: ${import_chalk4.default.underline(
            "https://github.com/orgs/voltagent/discussions/74/"
          )}
`
        )
      );
      console.log(import_chalk4.default.gray("\nWe appreciate your interest!\n"));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(import_chalk4.default.red("\nAn unexpected error occurred:"));
      console.error(errorMessage);
      captureError({
        command: "add",
        errorMessage
      });
      process.exit(1);
    }
  });
};

// src/index.ts
var createCLI = () => {
  const program = new import_commander.Command();
  program.name("voltagent").description("VoltAgent CLI - CLI tool for update checks").version("0.1.0");
  registerInitCommand(program);
  registerUpdateCommand(program);
  registerWhoamiCommand(program);
  registerAddCommand(program);
  return program;
};
var runCLI = async () => {
  try {
    console.log(
      import_chalk5.default.cyan(
        import_figlet.default.textSync("VoltAgent CLI", {
          font: "Standard",
          horizontalLayout: "default",
          verticalLayout: "default"
        })
      )
    );
    const program = createCLI();
    await program.parseAsync(process.argv);
    await analytics_default.shutdown();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(import_chalk5.default.red("An unexpected error occurred:"));
    console.error(errorMessage);
    captureError({
      command: "unknown",
      errorMessage
    });
    await analytics_default.shutdown();
    process.exit(1);
  }
};
runCLI().catch((error) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  console.error(`Error: ${errorMessage}`);
  captureError({
    command: "cli_runner",
    errorMessage
  });
  analytics_default.shutdown().then(() => {
    process.exit(1);
  });
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  runCLI
});
//# sourceMappingURL=index.js.map