{"name": "atomically", "description": "Read and write files atomically and reliably.", "version": "1.7.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"benchmark": "node ./tasks/benchmark.js", "clean": "<PERSON><PERSON><PERSON> dist", "compile": "tsc --skipLibCheck && tstei", "compile:watch": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --watch", "test": "tap --no-coverage-report", "test:watch": "tap --no-coverage-report --watch", "prepublishOnly": "npm run clean && npm run compile && npm run test"}, "bugs": {"url": "https://github.com/fabiospampinato/atomically/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/fabiospampinato/atomically.git"}, "keywords": ["atomic", "read", "write", "file", "reliable"], "engines": {"node": ">=10.12.0"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.7.2", "lodash": "^4.17.19", "mkdirp": "^1.0.4", "promise-resolve-timeout": "^1.2.1", "require-inject": "^1.4.4", "rimraf": "^3.0.2", "tap": "^14.10.7", "typescript": "^3.5.3", "typescript-transform-export-interop": "^1.0.2", "write-file-atomic": "^3.0.3"}}