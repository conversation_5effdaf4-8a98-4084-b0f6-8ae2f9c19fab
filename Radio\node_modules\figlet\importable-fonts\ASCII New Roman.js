export default `flf2a$ 4 3 9 63 12 0 8127 0
Author : <PERSON><PERSON> and others
Date   : 2003/09/02 20:09:19
Version: 1.0
-------------------------------------------------
Font ascii_new_roman, created by <PERSON><PERSON>
and others from alt.ascii-art
-------------------------------------------------
This font has been created using <PERSON><PERSON><PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
$ #
$ #
$ #
$ ##
 __, #
(-V  #
 _o  #
(    ##
"#
 #
 #
 ##
##
 #
 #
 ##
 _||_, #
(-(__  #
 _||_) #
(      ##
 _,  _,#
(-o ,' #
 _,',o'#
(      ##
&#
 #
 #
 ##
 __, #
(-/  #
     #
     ##
 ____, #
(-//   #
 _\\\\_  #
(      ##
 ___, #
(-\\\\  #
 _//_ #
(     ##
*#
 #
 #
 ##
 ___,  #
(-_|_. #
  _|   #
 (     ##
,#
 #
 #
 ##
       #
 ____, #
(      #
       ##
.#
 #
 #
 ##
  ___, #
 (-//  #
 _//_  #
(      ##
 _____,#
(-/ /\\ #
 _\\/_/ #
(      ##
 ___, #
(-/|  #
 '_|, #
 (    ##
 __,  #
(- )  #
 ,'_, #
(     ##
 __, #
(-_) #
 __) #
(    ##
 ____, #
(-/_|_,#
   _|, #
  (    ##
 ____, #
(-|_   #
  __)  #
 (     ##
  ___, #
 (-/_  #
 _(__) #
(      ##
 ___, #
(- /  #
 _/,  #
(     ##
 _____,#
(-(__) #
 _(__) #
(      ##
 _____,#
(-(__) #
  __/, #
 (     ##
 __, #
(-o  #
 _o  #
(    ##
 __, #
(-o  #
 _p  #
(    ##
<#
 #
 #
 ##
=#
 #
 #
 ##
>#
 #
 #
 ##
 _____,#
(-(  _)#
   _(, #
  (    ##
  ___, #
 (-/_\\ #
 _((_/_#
(      ##
  ____,#
 (-/_| #
 _/  |,#
(      ##
 ____  #
(-|__) #
 _|__) #
(      ##
 ____, #
(-/    #
 _\\__, #
(      ##
 ____, #
(-|  \\ #
 _|__/ #
(      ##
 ____, #
(-|_,  #
 _|__, #
(      ##
 ____, #
(-|_,  #
 _|    #
(      ##
 ____, #
(-/ _, #
 _\\__| #
(      ##
 __, _,#
(-|__| #
 _|  |,#
(      ##
  __,  #
 (-|   #
  _|_, #
 (     ##
  _,  #
 (-|  #
 __|, #
(     ##
 __, , #
( |_/  #
 _| \\, #
(      ##
 __,   #
(-|    #
 _|__, #
(      ##
 __, _,#
(-|\\/| #
 _| _|,#
(      ##
 _,  _,#
(-|\\ | #
 _| \\|,#
(      ##
 ____, #
(-/  \\ #
 _\\__/,#
(      ##
 ____, #
(-|__) #
 _|    #
(      ##
 ____, #
(-/  \\ #
 _\\_\\/,#
(      ##
 ____, #
(-|__) #
 _|  \\,#
(      ##
 ____, #
(-(__  #
 ____) #
(      ##
 ____, #
(-|    #
 _|,   #
(      ##
 _, _, #
(-|  \\ #
 _|__/ #
(      ##
 __  _,#
(-\\  / #
  _\\/  #
 (     ##
 _   _,#
(-|  | #
 _|/\\|,#
(      ##
 _, _, #
(-\\_/  #
 _/ \\, #
(      ##
 _  _, #
(-\\_/  #
  _|,  #
 (     ##
 ___,  #
(- /   #
 _/__, #
(      ##
[#
 #
 #
 ##
 _,    #
(-\\\\   #
  _\\\\, #
 (     ##
]#
 #
 #
 ##
 ___, #
(-/\\  #
 '  \` #
      ##
_#
 #
 #
 ##
\`#
 #
 #
 ##
  ____,#
 (-/_| #
 _/  |,#
(      ##
 ____  #
(-|__) #
 _|__) #
(      ##
 ____, #
(-/    #
 _\\__, #
(      ##
 ____, #
(-|  \\ #
 _|__/ #
(      ##
 ____, #
(-|_,  #
 _|__, #
(      ##
 ____, #
(-|_,  #
 _|    #
(      ##
 ____, #
(-/ _, #
 _\\__| #
(      ##
 __, _,#
(-|__| #
 _|  |,#
(      ##
  __,  #
 (-|   #
  _|_, #
 (     ##
  _,  #
 (-|  #
 __|, #
(     ##
 __, , #
( |_/  #
 _| \\, #
(      ##
 __,   #
(-|    #
 _|__, #
(      ##
 __, _,#
(-|\\/| #
 _| _|,#
(      ##
 _,  _,#
(-|\\ | #
 _| \\|,#
(      ##
 ____, #
(-/  \\ #
 _\\__/,#
(      ##
 ____, #
(-|__) #
 _|    #
(      ##
 ____, #
(-/  \\ #
 _\\_\\/,#
(      ##
 ____, #
(-|__) #
 _|  \\,#
(      ##
 ____, #
(-(__  #
 ____) #
(      ##
 ____, #
(-|    #
 _|,   #
(      ##
 _, _, #
(-|  \\ #
 _|__/ #
(      ##
 __  _,#
(-\\  / #
  _\\/  #
 (     ##
 _   _,#
(-|  | #
 _|/\\|,#
(      ##
 _, _, #
(-\\_/  #
 _/ \\, #
(      ##
 _  _, #
(-\\_/  #
  _|,  #
 (     ##
 ___,  #
(- /   #
 _/__, #
(      ##
{#
 #
 #
 ##
 __, #
(-|  #
  |  #
 _|, ##
}#
 #
 #
 ##
~#
 #
 #
 ##
  ____,#
 (-/_| #
 _/  |,#
(      ##
 ____, #
(-/  \\ #
 _\\__/,#
(      ##
 _, _, #
(-|  \\ #
 _|__/ #
(      ##
  ____,#
 (-/_| #
 _/  |,#
(      ##
 ____, #
(-/  \\ #
 _\\__/,#
(      ##
 _, _, #
(-|  \\ #
 _|__/ #
(      ##
�#
 #
 #
 ##
`