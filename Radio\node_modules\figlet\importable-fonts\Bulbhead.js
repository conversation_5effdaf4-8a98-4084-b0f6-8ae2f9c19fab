export default `flf2a$ 4 4 99 0 20
Bulbhead by <PERSON><PERSON>, 23jun94
Figlet release 2.0 -- August 5, 1993
 ____  __  __  __    ____  _   _  ____    __    ____
(  _ \\(  )(  )(  )  (  _ \\( )_( )( ___)  /__\\  (  _ \\
 ) _ < )(__)(  )(__  ) _ < ) _ (  )___) /(__)\\  )(_) )
(____/(______)(____)(____/(_) (_)(____)(__)(__)(____/

Update February 12, 2002 by <PERSON> mark<PERSON>@jave.de
  Added german umlauts

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
4    - height of a character
4    - height of a character, not including descenders
99   - max line length (excluding comment lines) + a fudge factor
0    - default smushmode for this font (like "-m 0" on command line)
13   - number of comment lines

$$@
$$@
$$@
$$@@
/\\@
)(@
\\/@
()@@
||@
  @
  @
  @@
 | | @
-|-|-@
-|-|-@
 | | @@
 _|_ @
/ |_)@
\\_| \\@
(_|_/@@
 _  _  @
(_)/ ) @
  / /_ @
 (_/(_)@@
  _  @
 ( ) @
 /_\\/@
(__/\\@@
/@
 @
 @
 @@
  _ @
 / )@
( ( @
 \\_)@@
 _  @
( \\ @
 ) )@
(_/ @@
   @
\\|/@
/|\\@
   @@
   _   @
 _| |_ @
(_   _)@
  |_|  @@
  @
  @
()@
/ @@
     @
 ___ @
(___)@
     @@
  @
  @
  @
()@@
   _ @
  / )@
 / / @
(_/  @@
  ___  @
 / _ \\ @
( (_) )@
 \\___/ @@
  __ @
 /  )@
  )( @
 (__)@@
 ___  @
(__ \\ @
 / _/ @
(____)@@
 ___ @
(__ )@
 (_ \\@
(___/@@
  __  @
 /. | @
(_  _)@
  (_) @@
 ___ @
| __)@
|__ \\@
(___/@@
  _  @
 / ) @
/ _ \\@
\\___/@@
 ___ @
(__ )@
 / / @
(_/  @@
 ___ @
( _ )@
/ _ \\@
\\___/@@
 ___ @
/ _ \\@
\\_  /@
 (_/ @@
  @
()@
  @
()@@
()@
  @
()@
/ @@
  __@
 / /@
< < @
 \\_\\@@
 ___ @
(___)@
 ___ @
(___)@@
__  @
\\ \\ @
 > >@
/_/ @@
 ___ @
(__ )@
 (_/ @
 (_) @@
  __ @
 /  \\@
| ()/@
 \\__ @@
   __   @
  /__\\  @
 /(__)\\ @
(__)(__)@@
 ____ @
(  _ \\@
 ) _ <@
(____/@@
  ___ @
 / __)@
( (__ @
 \\___)@@
 ____  @
(  _ \\ @
 )(_) )@
(____/ @@
 ____ @
( ___)@
 )__) @
(____)@@
 ____ @
( ___)@
 )__) @
(__)  @@
  ___ @
 / __)@
( (_-.@
 \\___/@@
 _   _ @
( )_( )@
 ) _ ( @
(_) (_)@@
 ____ @
(_  _)@
 _)(_ @
(____)@@
  ____ @
 (_  _)@
.-_)(  @
\\____) @@
 _  _ @
( )/ )@
 )  ( @
(_)\\_)@@
 __   @
(  )  @
 )(__ @
(____)@@
 __  __ @
(  \\/  )@
 )    ( @
(_/\\/\\_)@@
 _  _ @
( \\( )@
 )  ( @
(_)\\_)@@
 _____ @
(  _  )@
 )(_)( @
(_____)@@
 ____ @
(  _ \\@
 )___/@
(__)  @@
 _____ @
(  _  )@
 )(_)( @
(___/\\\\@@
 ____ @
(  _ \\@
 )   /@
(_)\\_)@@
 ___ @
/ __)@
\\__ \\@
(___/@@
 ____ @
(_  _)@
  )(  @
 (__) @@
 __  __ @
(  )(  )@
 )(__)( @
(______)@@
 _  _ @
( \\/ )@
 \\  / @
  \\/  @@
 _    _ @
( \\/\\/ )@
 )    ( @
(__/\\__)@@
 _  _ @
( \\/ )@
 )  ( @
(_/\\_)@@
 _  _ @
( \\/ )@
 \\  / @
 (__) @@
 ____ @
(_   )@
 / /_ @
(____)@@
 __@
|  @
|  @
|__@@
 _   @
( \\  @
 \\ \\ @
  \\_)@@
__ @
  |@
  |@
__|@@
 / \\ @
(_^_)@
     @
     @@
     @
     @
 ___ @
(___)@@
\\@
 @
 @
 @@
   __   @
  /__\\  @
 /(__)\\ @
(__)(__)@@
 ____ @
(  _ \\@
 ) _ <@
(____/@@
  ___ @
 / __)@
( (__ @
 \\___)@@
 ____  @
(  _ \\ @
 )(_) )@
(____/ @@
 ____ @
( ___)@
 )__) @
(____)@@
 ____ @
( ___)@
 )__) @
(__)  @@
  ___ @
 / __)@
( (_-.@
 \\___/@@
 _   _ @
( )_( )@
 ) _ ( @
(_) (_)@@
 ____ @
(_  _)@
 _)(_ @
(____)@@
  ____ @
 (_  _)@
.-_)(  @
\\____) @@
 _  _ @
( )/ )@
 )  ( @
(_)\\_)@@
 __   @
(  )  @
 )(__ @
(____)@@
 __  __ @
(  \\/  )@
 )    ( @
(_/\\/\\_)@@
 _  _ @
( \\( )@
 )  ( @
(_)\\_)@@
 _____ @
(  _  )@
 )(_)( @
(_____)@@
 ____ @
(  _ \\@
 )___/@
(__)  @@
 _____ @
(  _  )@
 )(_)( @
(___/\\\\@@
 ____ @
(  _ \\@
 )   /@
(_)\\_)@@
 ___ @
/ __)@
\\__ \\@
(___/@@
 ____ @
(_  _)@
  )(  @
 (__) @@
 __  __ @
(  )(  )@
 )(__)( @
(______)@@
 _  _ @
( \\/ )@
 \\  / @
  \\/  @@
 _    _ @
( \\/\\/ )@
 )    ( @
(__/\\__)@@
 _  _ @
( \\/ )@
 )  ( @
(_/\\_)@@
 _  _ @
( \\/ )@
 \\  / @
 (__) @@
 ____ @
(_   )@
 / /_ @
(____)@@
 ,-@
_| @
 | @
 \`-@@
/\\@
||@
||@
\\/@@
-. @
 |_@
 | @
-' @@
   @
/\\/@
   @
   @@
 (_)(_) @
  /__\\  @
 /(__)\\ @
(__)(__)@@
(_)_(_)@
(  _  )@
 )(_)( @
(_____)@@
 (_)(_) @
(  )(  )@
 )(__)( @
(______)@@
 (_)(_) @
  /__\\  @
 /(__)\\ @
(__)(__)@@
(_)_(_)@
(  _  )@
 )(_)( @
(_____)@@
 (_)(_) @
(  )(  )@
 )(__)( @
(______)@@
 ___ @
/ _ )@
| _ \\@
| __/@@
160  NO-BREAK SPACE
 $@
 $@
 $@
 $@@
`