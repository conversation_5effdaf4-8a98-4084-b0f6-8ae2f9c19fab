{"name": "@voltagent/vercel-ai", "version": "0.1.9", "description": "VoltAgent Vercel AI - Vercel AI provider integration for VoltAgent", "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"@ai-sdk/openai": "^1.3.10", "@voltagent/core": "^0.1.20", "ai": "^4.2.11", "zod": "3.24.2"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.11", "eslint": "^8.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsup": "^6.7.0", "typescript": "^5.0.4"}, "peerDependencies": {"@voltagent/core": "^0.1.0"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "test": "jest --passWithNoTests"}}