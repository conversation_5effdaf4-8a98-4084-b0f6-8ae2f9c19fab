export default `flf2a$ 9 8 14 -1 16
Gothic.flf - Taken from a banner program called "gothic" by <PERSON>tization suggested bu <PERSON> (<EMAIL>)
Figletized by <PERSON> 11/19/93 (<EMAIL>)
Version 1.1 - updated on 11/23/93
Version 1.11 - updated on 11/28/93 by <PERSON> (<EMAIL>)
             - adjusted baseline and fixed some spacing problems
Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
9    - height of a character
8    - height of a character, not including descenders
14   - max line length (excluding comment lines) + a fudge factor
-1   - default smushmode for this font (like "-m 15" on command line)
16   - number of comment lines

      @
      @
      @
      @
      @
      @
      @
      @
      @@
   @
/\\ @
\\/ @
}{ @
\\/ @
   @
<> @
   @
   @@
<> <> @
 )  ) @
      @
      @
      @
      @
      @
      @
      @@
  <> <>   @
  }{ }{   @
<>++=++<> @
  }{ }{   @
<>++=++<> @
  }{ }{   @
  <> <>   @
          @
          @@
 _++_, @
||||   @
||--.  @
 ~|||| @
  |||| @
,-__-  @
  ||   @
       @
       @@
      @
    , @
<>  / @
   /  @
  /   @
 /    @
/  <> @
\`     @
      @@
      @
 /\\   @
 \\/   @
 /\\ , @
/'\\\\, @
|  \\\\ @
\\\\-/\\ @
      @
      @@
<>@
 )@
  @
  @
  @
  @
  @
  @
  @@
 / @
// @
|| @
|| @
|| @
\\\\ @
 \\ @
   @
   @@
\\  @
\\\\ @
|| @
|| @
|| @
// @
/  @
   @
   @@
        @
 <> <>  @
  \\ /   @
<>-*-<> @
  / \\   @
 <> <>  @
        @
        @
        @@
       @
       @
  <>   @
  }{   @
<>--<> @
  }{   @
  <>   @
       @
       @@
   @
   @
   @
   @
   @
   @
<> @
 ) @
   @@
      @
      @
      @
      @
<>-<> @
      @
      @
      @
      @@
   @
   @
   @
   @
   @
   @
<> @
   @
   @@
      @
    / @
   /  @
  //  @
  /   @
 //   @
 /    @
/     @
      @@
      @
 /\\\\  @
|| || @
|| || @
|| || @
|| || @
 \\\\/  @
      @
      @@
     @
 /|  @
/||  @
 ||  @
 ||  @
 ||  @
,/-' @
     @
     @@
     @
 /\\  @
(  ) @
  // @
 //  @
/(   @
{___ @
     @
     @@
____ @
\` // @
 //  @
 \\\\  @
  )) @
 //  @
/'   @
     @
     @@
  ,  @
 /|  @
/ |  @
__|_ @
---- @
  |  @
 ,_, @
     @
     @@
____  @
||  \` @
||_   @
|/ \\  @
   )) @
  //  @
 /'   @
      @
      @@
      @
  ,/  @
 //   @
((_-  @
|| )) @
(( || @
 \\//  @
      @
      @@
____  @
\`  || @
   /, @
  //  @
 ((   @
 ||   @
 |'   @
      @
      @@
      @
 /\\\\  @
|| || @
 \\ /  @
 /\\\\  @
// \\\\ @
|| || @
 \\\\/  @
      @@
      @
 /\\\\  @
|| || @
|| || @
 \\/|| @
   || @
 \\_/  @
      @
      @@
   @
   @
   @
<> @
   @
   @
<> @
   @
   @@
   @
   @
   @
<> @
   @
   @
<> @
 ) @
   @@
      @
    / @
   /  @
 //   @
<<    @
 \\\\   @
   \\  @
    \\ @
      @@
      @
      @
      @
<>-<> @
      @
<>-<> @
      @
      @
      @@
      @
\\     @
 \\    @
  \\\\  @
   >> @
  //  @
 /    @
/     @
      @@
 -_  @
/ \\\\ @
\` || @
  |, @
 ((  @
     @
 <>  @
     @
     @@
      @
 /\\\\  @
|| || @
||/|| @
||\\|  @
||    @
 \\\\_, @
      @
      @@
  ___    @
 -   -_, @
(  ~/||  @
(  / ||  @
 \\/==||  @
 /_ _||  @
(  - \\\\, @
         @
         @@
          @
_-_ _,,   @
   -/  )  @
  ~||_<   @
   || \\\\  @
   ,/--|| @
  _--_-'  @
 (        @
          @@
         @
  ,- _~. @
 (' /|   @
((  ||   @
((  ||   @
 ( / |   @
  -____- @
         @
         @@
          @
-_____    @
  ' | -,  @
 /| |  |\` @
 || |==|| @
~|| |  |, @
 ~-____,  @
(         @
          @@
         @
  ,- _~, @
 (' /| / @
((  ||/= @
((  ||   @
 ( / |   @
  -____- @
         @
         @@
    _ ,@
  ,- - @
 _||_  @
' ||   @
  ||   @
  |,   @
_-/    @
       @
       @@
    __ ,  @
  ,-| ~   @
 ('||/__, @
(( |||  | @
(( |||==| @
 ( / |  , @
  -____/  @
          @
          @@
          @
_-_-      @
  /,      @
  || __   @
 ~||-  -  @
  ||===|| @
 ( \\_, |  @
       \`  @
          @@
     @
_-_, @
  // @
  || @
 ~|| @
  || @
_-_, @
     @
     @@
       @
 _-_,, @
(  //  @
  _||  @
  _||  @
   ||  @
-__-,  @
       @
       @@
          @
_-_-,     @
  // ,    @
  ||/\\\\   @
 ~|| <    @
  ||/\\\\   @
 _-__,\\\\, @
          @
          @@
        @
_-_-    @
 /,     @
 ||     @
~||     @
 ||     @
(  -__, @
        @
        @@
           @
  /\\\\,/\\\\, @
 /| || ||  @
 || || ||  @
 ||=|= ||  @
~|| || ||  @
 |, \\\\,\\\\, @
_-         @
           @@
    __   @
   /  -, @
  ||   ) @
 ~||---) @
 ~||---, @
 ~||  /  @
  |, /   @
-_-  --~ @
         @@
    __     @
  ,-||-,   @
 ('|||  )  @
(( |||--)) @
(( |||--)) @
 ( / |  )  @
  -____-   @
           @
           @@
         @
-__ /\\\\  @
  ||  \\\\ @
 /||__|| @
 \\||__|| @
  ||  |, @
_-||-_/  @
  ||     @
         @@
    __     @
  ,-||-,   @
 ('|||  )  @
(( |||--)) @
(( |||--)) @
 ( / |  )  @
  -____-\\\\ @
           @
           @@
         @
-__ /\\   @
  || \\,  @
 /|| /   @
 \\||/-   @
  ||  \\  @
_---_-|, @
         @
         @@
        @
  -_-/  @
 (_ /   @
(_ --_  @
  --_ ) @
 _/  )) @
(_-_-   @
        @
        @@
 ___        @
-   ---___- @
   (' ||    @
  ((  ||    @
 ((   ||    @
  (( //     @
    -____-  @
            @
            @@
 _ _    _ , @
- - /  - -  @
  ('||  ||  @
 (( ||--||  @
 (( ||--||  @
 (( /   ||  @
   -___-\\\\, @
            @
            @@
 _      @
- - _-  @
  )-  ) @
  )___) @
 ~)___) @
  )  )  @
 /-_/   @
        @
        @@
 _        @
- - /, /, @
  )/ )/ ) @
  )__)__) @
 ~)__)__) @
  )  )  ) @
 /-_/-_/  @
          @
          @@
 _        @
- -    /\` @
  \\\\  /   @
   \\\\/    @
  ==/\\==  @
   / \\\\   @
\\\\/   \\\\, @
          @
          @@
         @
-_   _   @
  |,- \`  @
 ~||__)) @
 ~||__)) @
  |_ _,  @
 -' -    @
( _-_    @
         @@
      @
_-___ @
    / @
   /  @
 =/=  @
 /    @
/-__- @
      @
      @@
  __ @
||   @
||   @
||   @
||   @
||   @
||   @
||__ @
     @@
      @
\\     @
 \\    @
 \\\\   @
  \\   @
  \\\\  @
   \\  @
    \\ @
      @@
__   @
  || @
  || @
  || @
  || @
  || @
  || @
__|| @
     @@
  x   @
 / \\  @
/   \\ @
      @
      @
      @
      @
      @
      @@
      @
      @
      @
      @
      @
      @
      @
_____ @
      @@
   @
<> @
(  @
   @
   @
   @
   @
   @
   @@
      @
      @
  _   @
 < \\, @
 /-|| @
(( || @
 \\/\\\\ @
      @
      @@
      @
,,    @
||    @
||/|, @
|| || @
|| |' @
\\\\/   @
      @
      @@
     @
     @
     @
 _-_ @
||   @
||   @
\\\\,/ @
     @
     @@
      @
 |\\   @
  \\\\  @
 / \\\\ @
|| || @
|| || @
 \\\\/  @
      @
      @@
      @
      @
      @
 _-_  @
|| \\\\ @
||/   @
\\\\,/  @
      @
      @@
     @
  /\\ @
 ||  @
=||= @
 ||  @
 ||  @
 \\\\, @
     @
     @@
      @
      @
  _   @
 / \\\\ @
|| || @
|| || @
\\\\_-| @
 /  \\ @
'----\`@@
      @
,,    @
||    @
||/\\\\ @
|| || @
|| || @
\\\\ |/ @
  _/  @
      @@
   @
   @
 ' @
\\\\ @
|| @
|| @
\\\\ @
   @
   @@
   @
   @
 ' @
\\\\ @
|| @
|| @
|| @
|; @
/  @@
     @
,,   @
||   @
||/\\ @
||_< @
|| | @
\\\\,\\ @
     @
     @@
   @
,, @
|| @
|| @
|| @
|| @
\\\\ @
   @
   @@
         @
         @
         @
\\\\/\\\\/\\\\ @
|| || || @
|| || || @
\\\\ \\\\ \\\\ @
         @
         @@
      @
      @
      @
\\\\/\\\\ @
|| || @
|| || @
\\\\ \\\\ @
      @
      @@
      @
      @
      @
 /'\\\\ @
|| || @
|| || @
\\\\,/  @
      @
      @@
      @
      @
      @
-_-_  @
|| \\\\ @
|| || @
||-'  @
|/    @
'     @@
      @
      @
      @
 /'\\\\ @
|| || @
|| || @
\\\\,|| @
   || @
   '\` @@
      @
      @
      @
,._-_ @
 ||   @
 ||   @
 \\\\,  @
      @
      @@
      @
      @
      @
 _-_, @
||_.  @
 ~ || @
,-_-  @
      @
      @@
     @
  ,  @
 ||  @
=||= @
 ||  @
 ||  @
 \\\\, @
     @
     @@
      @
      @
      @
\\\\ \\\\ @
|| || @
|| || @
\\\\/\\\\ @
      @
      @@
     @
     @
;    @
\\\\/\\ @
|| | @
|| | @
\\\\/  @
     @
     @@
       @
       @
;      @
\\\\/\\/\\ @
|| | | @
|| | | @
\\\\/\\\\/ @
       @
       @@
      @
      @
,     @
\\\\ /\` @
 \\\\   @
 /\\\\  @
/  \\; @
      @
      @@
       @
       @
       @
'\\\\/\\\\ @
 || ;' @
 ||/   @
 |/    @
(      @
 -_-   @@
    @
    @
    @
/\\\\ @
 /  @
/\\\\ @
 || @
 /  @
(,  @@
   _ @
  (  @
  )  @
  )  @
-{   @
  )  @
  )  @
  (_ @
     @@
   @
|| @
|| @
|| @
|| @
|| @
|| @
|| @
   @@
_    @
 )   @
 (   @
 (   @
  }- @
 (   @
 (   @
_)   @
     @@
 _    @
/ \\_/ @
      @
      @
      @
      @
      @
      @
      @@
  ___    @
 -   -_, @
(  ~/||  @
(  / ||  @
 \\/==||  @
 /_ _||  @
(  - \\\\, @
         @
         @@
    __     @
  ,-||-,   @
 ('|||  )  @
(( |||--)) @
(( |||--)) @
 ( / |  )  @
  -____-   @
           @
           @@
 _ _    _ , @
- - /  - -  @
  ('||  ||  @
 (( ||--||  @
 (( ||--||  @
 (( /   ||  @
   -___-\\\\, @
            @
            @@
      @
 \` \`  @
  _   @
 < \\, @
 /-|| @
(( || @
 \\/\\\\ @
      @
      @@
      @
 \` \`  @
      @
 /'\\\\ @
|| || @
|| || @
\\\\,/  @
      @
      @@
      @
 \` \`  @
      @
\\\\ \\\\ @
|| || @
|| || @
\\\\/\\\\ @
      @
      @@
          @
_-_ _,,   @
   -/  )  @
  ~||_<   @
   || \\\\  @
   ,/--|| @
  _--_-'  @
 ( ||     @
   ||     @@
`