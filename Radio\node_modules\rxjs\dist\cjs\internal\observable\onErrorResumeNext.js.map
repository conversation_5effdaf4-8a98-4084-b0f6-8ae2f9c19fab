{"version": 3, "file": "onErrorResumeNext.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/onErrorResumeNext.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAE3C,yDAAwD;AACxD,sEAAqE;AACrE,qCAAoC;AACpC,yCAAwC;AAmExC,SAAgB,iBAAiB;IAC/B,iBAAyE;SAAzE,UAAyE,EAAzE,qBAAyE,EAAzE,IAAyE;QAAzE,4BAAyE;;IAEzE,IAAM,WAAW,GAA4B,+BAAc,CAAC,OAAO,CAAQ,CAAC;IAE5E,OAAO,IAAI,uBAAU,CAAC,UAAC,UAAU;QAC/B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAM,aAAa,GAAG;YACpB,IAAI,WAAW,GAAG,WAAW,CAAC,MAAM,EAAE;gBACpC,IAAI,UAAU,SAAuB,CAAC;gBACtC,IAAI;oBACF,UAAU,GAAG,qBAAS,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;iBACpD;gBAAC,OAAO,GAAG,EAAE;oBACZ,aAAa,EAAE,CAAC;oBAChB,OAAO;iBACR;gBACD,IAAM,eAAe,GAAG,IAAI,uCAAkB,CAAC,UAAU,EAAE,SAAS,EAAE,WAAI,EAAE,WAAI,CAAC,CAAC;gBAClF,UAAU,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;gBACtC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aACpC;iBAAM;gBACL,UAAU,CAAC,QAAQ,EAAE,CAAC;aACvB;QACH,CAAC,CAAC;QACF,aAAa,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC;AAzBD,8CAyBC"}