{"name": "get-stream", "version": "4.1.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": "sindresorhus/get-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"ava": "*", "into-stream": "^3.0.0", "xo": "*"}}