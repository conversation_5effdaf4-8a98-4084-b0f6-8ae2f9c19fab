export default `flf2a$ 9 8 12 63 18 0 24511 0
Font   : Fire Font-s
Author : MJP
Date   : 2003/11/6 11:11:05
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using <PERSON><PERSON><PERSON>'s FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
Based on Small.flf by <PERSON> 4/93 -- based on Standard

---

Font modified May 26, 2012 by patorjk
- Added the 0xCA0 character.
$ #
$ #
$ #
$ #
$ #
$ #
$ #
$ #
$ ##
   ____ #
  |   / #
  |  /  #
  | /   #
  |/    #
 (      #
 )\\     #
((_)    #
        ##
          #
  (   (   #
  )\\  )\\  #
 ((_)((_) #
          #
          #
          #
          #
          ##
          #
   _ _    #
 _| | |_  #
|_  .  _| #
|_     _| #
  |_|_|   #
          #
          #
          ##
     #
     #
 ||_ #
(_-< #
/ _/ #
 ||  #
     #
     #
     ##
        #
        #
        #
 _  __  #
(_)/ /  #
  / /_  #
 /_/(_) #
        #
        ##
          #
          #
      (   #
  __  )\\  #
 / _|((_) #
 > _|_ _| #
 \\_____|  #
          #
          ##
      #
  (   #
  )\\  #
 ((_) #
      #
      #
      #
      #
      ##
     #
     #
     #
  __ #
 / / #
| |  #
| |  #
 \\_\\ #
     ##
     #
     #
     #
__   #
\\ \\  #
 | | #
 | | #
/_/  #
     ##
 _/\\_ #
 >  < #
  \\/  #
      #
      #
      #
      #
      #
      ##
        #
        #
        #
   _    #
 _| |_  #
|_   _| #
  |_|   #
        #
        ##
     #
     #
     #
     #
     #
  )  #
 /(  #
(_)) #
     ##
      #
      #
      #
 ___  #
|___| #
      #
      #
      #
      ##
    #
    #
    #
    #
    #
    #
 _  #
(_) #
    ##
      #
      #
      #
   __ #
  / / #
 / /  #
/_/   #
      #
      ##
        #
     )  #
  ( /(  #
  )\\()) #
 ((_)\\  #
 /  (_) #
| () |  #
 \\__/   #
        ##
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
 / (_) #
 | |   #
 |_|   #
       ##
       #
    )  #
 ( /(  #
 )(_)) #
((_)   #
|_  )  #
 / /   #
/___|  #
       ##
        #
     )  #
  ( /(  #
  )\\()) #
 ((_)\\  #
|__ (_) #
 |_ \\   #
|___/   #
        ##
        #
     )  #
  ( /(  #
  )\\()) #
 ((_)\\  #
| | (_) #
|_  _|  #
  |_|   #
        ##
         #
 (  (    #
 )\\))(   #
((_)()\\  #
 (()((_) #
  | __|  #
  |__ \\  #
  |___/  #
         ##
       #
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_) /  #
 / _ \\ #
 \\___/ #
       ##
        #
     )  #
  ( /(  #
  )\\()) #
 ((_)\\  #
|__  /  #
  / /   #
 /_/    #
        ##
        #
 (      #
 )\\ (   #
((_))\\  #
  _((_) #
 ( _ )  #
 / _ \\  #
 \\___/  #
        ##
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
/ _(_) #
\\_, /  #
 /_/   #
       ##
    #
    #
    #
    #
 _  #
(_) #
 _  #
(_) #
    ##
      #
      #
      #
      #
   _  #
  (_) #
  (   #
  )\\  #
 ((_) ##
     #
     #
     #
     #
  __ #
 / / #
< <  #
 \\_\\ #
     ##
      #
      #
      #
      #
      #
 ___  #
|___| #
|___| #
      ##
     #
     #
     #
     #
__   #
\\ \\  #
 > > #
/_/  #
     ##
      #
      #
 (    #
 )\\   #
((_)  #
|__ \\ #
  /_/ #
 (_)  #
      ##
         #
         #
   (     #
   )\\    #
  ((_)   #
 /__ _\\  #
// _\` |\\ #
\\\\__,_|/ #
 \\____/  ##
          #
   (      #
   )\\     #
((((_)(   #
 )\\ _ )\\  #
 (_)_\\(_) #
  / _ \\   #
 /_/ \\_\\  #
          ##
       #
   (   #
 ( )\\  #
 )((_) #
((_)_  #
 | _ ) #
 | _ \\ #
 |___/ #
       ##
        #
   (    #
   )\\   #
 (((_)  #
 )\\___  #
((/ __| #
 | (__  #
  \\___| #
        ##
 (      #
 )\\ )   #
(()/(   #
 /(_))  #
(_))_   #
 |   \\  #
 | |) | #
 |___/  #
        ##
      #
      #
 (    #
 )\\   #
((_)  #
| __| #
| _|  #
|___| #
      ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))_| #
| |_   #
| __|  #
|_|    #
       ##
         #
 (       #
 )\\ )    #
(()/(    #
 /(_))_  #
(_)) __| #
  | (_ | #
   \\___| #
         ##
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
 _((_) #
| || | #
| __ | #
|_||_| #
       ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))   #
|_ _|  #
 | |   #
|___|  #
       ##
       #
       #
   (   #
   )\\  #
  ((_) #
 _ | | #
| || | #
 \\__/  #
       ##
     )  #
  ( /(  #
  )\\()) #
|((_)\\  #
|_ ((_) #
| |/ /  #
  ' <   #
 _|\\_\\  #
        ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))   #
| |    #
| |__  #
|____| #
       ##
   *     #
 (  \`    #
 )\\))(   #
((_)()\\  #
(_()((_) #
|  \\/  | #
| |\\/| | #
|_|  |_| #
         ##
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
 _((_) #
| \\| | #
| .\` | #
|_|\\_| #
       ##
    )   #
 ( /(   #
 )\\())  #
((_)\\   #
  ((_)  #
 / _ \\  #
| (_) | #
 \\___/  #
        ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))   #
| _ \\  #
|  _/  #
|_|    #
       ##
        #
   (    #
 ( )\\   #
 )((_)  #
((_)_   #
 / _ \\  #
| (_) | #
 \\__\\_\\ #
        ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))   #
| _ \\  #
|   /  #
|_|_\\  #
       ##
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_))   #
/ __|  #
\\__ \\  #
|___/  #
       ##
         #
  *   )  #
\` )  /(  #
 ( )(_)) #
(_(_())  #
|_   _|  #
  | |    #
  |_|    #
         ##
        #
        #
    (   #
    )\\  #
 _ ((_) #
| | | | #
| |_| | #
 \\___/  #
        ##
         #
         #
 (   (   #
 )\\  )\\  #
((_)((_) #
\\ \\ / /  #
 \\ V /   #
  \\_/    #
         ##
          #
 (  (     #
 )\\))(   '#
((_)()\\ ) #
_(())\\_)()#
\\ \\((_)/ /#
 \\ \\/\\/ / #
  \\_/\\_/  #
          ##
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
__((_) #
\\ \\/ / #
 >  <  #
/_/\\_\\ #
       ##
     )  #
  ( /(  #
  )\\()) #
 ((_)\\  #
__ ((_) #
\\ \\ / / #
 \\ V /  #
  |_|   #
        ##
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
 _((_) #
|_  /  #
 / /   #
/___|  #
       ##
     #
     #
     #
     #
 __  #
| _| #
| |  #
| |  #
|__| ##
      #
      #
      #
      #
__    #
\\ \\   #
 \\ \\  #
  \\_\\ #
      ##
     #
     #
     #
     #
 __  #
|_ | #
 | | #
 | | #
|__| ##
      #
  /\\  #
 |/\\| #
      #
      #
      #
      #
      #
      ##
        #
        #
        #
        #
        #
        #
        #
 _____  #
|_____| ##
\`#
 #
 #
 #
 #
 #
 #
 #
 ##
       #
       #
    )  #
 ( /(  #
 )(_)) #
((_)_  #
/ _\` | #
\\__,_| #
       ##
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
| |(_) #
| '_ \\ #
|_.__/ #
       ##
      #
      #
      #
  (   #
  )\\  #
 ((_) #
/ _|  #
\\__|  #
      ##
        #
  (     #
  )\\ )  #
 (()/(  #
  ((_)) #
  _| |  #
/ _\` |  #
\\__,_|  #
        ##
       #
       #
   (   #
  ))\\  #
 /((_) #
(_))   #
/ -_)  #
\\___|  #
       ##
       #
 (     #
 )\\ )  #
(()/(  #
 /(_)) #
(_) _| #
 |  _| #
 |_|   #
       ##
        #
        #
 (  (   #
 )\\))(  #
((_))\\  #
 (()(_) #
/ _\` |  #
\\__, |  #
|___/   ##
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
| |(_) #
| ' \\  #
|_||_| #
       ##
     #
     #
 (   #
 )\\  #
((_) #
 (_) #
 | | #
 |_| #
     ##
      #
      #
  (   #
  )\\  #
 ((_) #
   !  #
  | | #
 _/ | #
|__/  ##
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
| |(_) #
| / /  #
|_\\_\\  #
       ##
     #
 (   #
 )\\  #
((_) #
 _   #
| |  #
| |  #
|_|  #
     ##
         #
         #
    )    #
   (     #
   )\\  ' #
 _((_))  #
| '  \\() #
|_|_|_|  #
         ##
        #
        #
        #
  (     #
  )\\ )  #
 _(_/(  #
| ' \\)) #
|_||_|  #
        ##
      #
      #
      #
  (   #
  )\\  #
 ((_) #
/ _ \\ #
\\___/ #
      ##
        #
        #
        #
 \`  )   #
 /(/(   #
((_)_\\  #
| '_ \\) #
| .__/  #
|_|     ##
       #
       #
   (   #
 ( )\\  #
 )(( ) #
((_)_) #
/ _\` | #
\\__, | #
   |_| ##
      #
      #
 (    #
 )(   #
(()\\  #
 ((_) #
| '_| #
|_|   #
      ##
     #
     #
     #
 (   #
 )\\  #
((_) #
(_-< #
/__/ #
     ##
       #
    )  #
 ( /(  #
 )\\()) #
(_))/  #
| |_   #
|  _|  #
 \\__|  #
       ##
       #
       #
   (   #
  ))\\  #
 /((_) #
(_))(  #
| || | #
 \\_,_| #
       ##
        #
        #
   )    #
  /((   #
 (_))\\  #
 _)((_) #
 \\ V /  #
  \\_/   #
        ##
         #
         #
 (  (    #
 )\\))(   #
((_)()\\  #
_(()((_) #
\\ V  V / #
 \\_/\\_/  #
         ##
       #
       #
    )  #
 ( /(  #
 )\\()) #
((_)\\  #
\\ \\ /  #
/_\\_\\  #
       ##
       #
       #
 (     #
 )\\ )  #
(()/(  #
 )(_)) #
| || | #
 \\_, | #
 |__/  ##
     #
     #
     #
 (   #
 )\\  #
((_) #
|_ / #
/__| #
     ##
      #
      #
      #
      #
   __ #
  / / #
_| |  #
 | |  #
  \\_\\ ##
    #
    #
    #
    #
 _  #
| | #
| | #
| | #
|_| ##
      #
      #
      #
      #
__    #
\\ \\   #
 | |_ #
 | |  #
/_/   ##
      #
 /\\/| #
|/\\/  #
      #
      #
      #
      #
      #
      ##
  )     #
 /( (   #
(_)))\\  #
(_)_(_) #
  /_\\   #
 / _ \\  #
/_/ \\_\\ #
        #
        ##
    (   #
  ) )\\  #
 /(((_) #
(_)_(_) #
 / _ \\  #
| (_) | #
 \\___/  #
        #
        ##
  )     #
 /( (   #
(_)))\\  #
(_)_(_) #
| | | | #
| |_| | #
 \\___/  #
        #
        ##
       #
       #
   (   #
 _ )\\  #
(_((_) #
/ _\` | #
\\__,_| #
       #
       ##
        #
        #
  )     #
 /(  _  #
(_))(_) #
 / _ \\  #
 \\___/  #
        #
        ##
        #
        #
    (   #
 _  )\\  #
(_)((_) #
| |_| | #
 \\___/  #
        #
        ##
       #
   (   #
 ( )\\  #
 )((_) #
(/__ \\ #
| |< < #
| ||_/ #
|_|    #
       ##
0xCA0  KANNADA LETTER TTHA
    )   #
 ( /(   #
 )\\())  #
((_)\\__)#
 /____/ #
 / _ \\  #
| (_) | #
$\\___/$ #
        ##`