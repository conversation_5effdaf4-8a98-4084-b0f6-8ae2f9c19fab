{"version": 3, "file": "formats.js", "sourceRoot": "", "sources": ["../src/formats.ts"], "names": [], "mappings": ";;;AAmCA,SAAS,MAAM,CACb,QAA0C,EAC1C,OAA8B;IAE9B,OAAO,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAA;AAC5B,CAAC;AAEY,QAAA,WAAW,GAAmB;IACzC,uDAAuD;IACvD,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC;IAC/B,4DAA4D;IAC5D,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC;IAC/B,WAAW,EAAE,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC;IAC/C,2DAA2D;IAC3D,QAAQ,EAAE,wEAAwE;IAClF,GAAG;IACH,eAAe,EACb,woCAAwoC;IAC1oC,oDAAoD;IACpD,cAAc,EACZ,mLAAmL;IACrL,yDAAyD;IACzD,0DAA0D;IAC1D,GAAG,EAAE,odAAod;IACzd,KAAK,EACH,0IAA0I;IAC5I,QAAQ,EACN,uGAAuG;IACzG,mHAAmH;IACnH,IAAI,EAAE,2EAA2E;IACjF,IAAI,EAAE,k/BAAk/B;IACx/B,KAAK;IACL,2CAA2C;IAC3C,IAAI,EAAE,8DAA8D;IACpE,oDAAoD;IACpD,+DAA+D;IAC/D,cAAc,EAAE,2BAA2B;IAC3C,2BAA2B,EAAE,8DAA8D;IAC3F,wFAAwF;IACxF,uBAAuB,EAAE,kDAAkD;IAC3E,+GAA+G;IAC/G,gDAAgD;IAChD,IAAI;IACJ,wBAAwB;IACxB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC;IAChD,wBAAwB;IACxB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC;IAChD,eAAe;IACf,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAC;IACjD,gBAAgB;IAChB,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAC;IAClD,uCAAuC;IACvC,QAAQ,EAAE,IAAI;IACd,2BAA2B;IAC3B,MAAM,EAAE,IAAI;CACb,CAAA;AAEY,QAAA,WAAW,GAAmB;IACzC,GAAG,mBAAW;IACd,IAAI,EAAE,MAAM,CAAC,4BAA4B,EAAE,WAAW,CAAC;IACvD,IAAI,EAAE,MAAM,CACV,6EAA6E,EAC7E,WAAW,CACZ;IACD,WAAW,EAAE,MAAM,CACjB,yGAAyG,EACzG,eAAe,CAChB;IACD,4EAA4E;IAC5E,GAAG,EAAE,4CAA4C;IACjD,eAAe,EAAE,yEAAyE;IAC1F,uCAAuC;IACvC,mHAAmH;IACnH,6FAA6F;IAC7F,KAAK,EACH,kHAAkH;CACrH,CAAA;AAEY,QAAA,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAW,CAAiB,CAAA;AAEnE,SAAS,UAAU,CAAC,IAAY;IAC9B,iDAAiD;IACjD,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAA;AACjE,CAAC;AAED,MAAM,IAAI,GAAG,4BAA4B,CAAA;AACzC,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAEhE,SAAS,IAAI,CAAC,GAAW;IACvB,gEAAgE;IAChE,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC/C,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAA;IAC1B,MAAM,IAAI,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAChC,MAAM,KAAK,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACjC,MAAM,GAAG,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAC/B,OAAO,CACL,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,EAAE;QACX,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAC5D,CAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAU,EAAE,EAAU;IACzC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAA;IACrB,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAC,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC;AAED,MAAM,IAAI,GAAG,yDAAyD,CAAA;AAEtE,SAAS,IAAI,CAAC,GAAW,EAAE,YAAsB;IAC/C,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC/C,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAA;IAE1B,MAAM,IAAI,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAChC,MAAM,MAAM,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAClC,MAAM,MAAM,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAClC,MAAM,QAAQ,GAAW,OAAO,CAAC,CAAC,CAAC,CAAA;IACnC,OAAO,CACL,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC;QAC3C,CAAC,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,YAAY,IAAI,QAAQ,KAAK,EAAE,CAAC,CACnC,CAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAU,EAAE,EAAU;IACzC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACxB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACxB,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;IAC1C,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;IAC1C,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAA;IACrB,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAC,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC;AAED,MAAM,mBAAmB,GAAG,OAAO,CAAA;AACnC,SAAS,SAAS,CAAC,GAAW;IAC5B,iDAAiD;IACjD,MAAM,QAAQ,GAAa,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IACzD,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAC9E,CAAC;AAED,SAAS,eAAe,CAAC,GAAW,EAAE,GAAW;IAC/C,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;QAAE,OAAO,SAAS,CAAA;IACnC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IAC/C,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IAC/C,MAAM,GAAG,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAC/B,IAAI,GAAG,KAAK,SAAS;QAAE,OAAO,SAAS,CAAA;IACvC,OAAO,GAAG,IAAI,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACnC,CAAC;AAED,MAAM,gBAAgB,GAAG,MAAM,CAAA;AAC/B,MAAM,GAAG,GACP,8nCAA8nC,CAAA;AAEhoC,SAAS,GAAG,CAAC,GAAW;IACtB,gGAAgG;IAChG,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACpD,CAAC;AAED,MAAM,IAAI,GAAG,oEAAoE,CAAA;AAEjF,SAAS,IAAI,CAAC,GAAW;IACvB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;IAClB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACvB,CAAC;AAED,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC5B,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;AAE7B,SAAS,aAAa,CAAC,KAAa;IAClC,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,SAAS,CAAA;AAC5E,CAAC;AAED,SAAS,aAAa,CAAC,KAAa;IAClC,4FAA4F;IAC5F,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;AAChC,CAAC;AAED,SAAS,cAAc;IACrB,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,QAAQ,GAAG,UAAU,CAAA;AAC3B,SAAS,KAAK,CAAC,GAAW;IACxB,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;QAAE,OAAO,KAAK,CAAA;IACpC,IAAI;QACF,IAAI,MAAM,CAAC,GAAG,CAAC,CAAA;QACf,OAAO,IAAI,CAAA;KACZ;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAA;KACb;AACH,CAAC"}