{"name": "is-installed-globally", "version": "0.4.0", "description": "Check if your package was installed globally", "license": "MIT", "repository": "sindresorhus/is-installed-globally", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["global", "package", "globally", "module", "install", "installed", "npm", "yarn", "is", "check", "detect", "local", "locally", "cli", "bin", "binary"], "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "devDependencies": {"ava": "^2.4.0", "cpy": "^8.1.1", "del": "^6.0.0", "execa": "^5.0.0", "make-dir": "^3.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}}