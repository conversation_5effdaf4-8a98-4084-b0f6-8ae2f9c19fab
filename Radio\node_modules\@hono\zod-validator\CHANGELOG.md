# @hono/zod-validator

## 0.6.0

### Minor Changes

- [#1173](https://github.com/honojs/middleware/pull/1173) [`a62b59f4505d10f41523a36ad7c02776f9e1cb01`](https://github.com/honojs/middleware/commit/a62b59f4505d10f41523a36ad7c02776f9e1cb01) Thanks [@yusukebe](https://github.com/yusukebe)! - feat: support Zod v4

## 0.5.0

### Minor Changes

- [#1140](https://github.com/honojs/middleware/pull/1140) [`8ed99d9d791ed6bd8b897c705289b0464947e632`](https://github.com/honojs/middleware/commit/8ed99d9d791ed6bd8b897c705289b0464947e632) Thanks [@yusukebe](https://github.com/yusukebe)! - feat: add `validationFunction` option

## 0.4.3

### Patch Changes

- [#968](https://github.com/honojs/middleware/pull/968) [`b65d5a58616f861520047dd08babc9cd1d81cbd1`](https://github.com/honojs/middleware/commit/b65d5a58616f861520047dd08babc9cd1d81cbd1) Thanks [@yusukebe](https://github.com/yusukebe)! - fix: fix commonjs import problem

## 0.4.2

### Patch Changes

- [#860](https://github.com/honojs/middleware/pull/860) [`803f011e41ecb3da503ddb2b3286c6d7606d9c4b`](https://github.com/honojs/middleware/commit/803f011e41ecb3da503ddb2b3286c6d7606d9c4b) Thanks [@askorupskyy](https://github.com/askorupskyy)! - Case-insensitive Zod schemas for headers

## 0.4.1

### Patch Changes

- [#766](https://github.com/honojs/middleware/pull/766) [`ed27af46ecb8a7c3b5399b725feded4feaf6f8a6`](https://github.com/honojs/middleware/commit/ed27af46ecb8a7c3b5399b725feded4feaf6f8a6) Thanks [@yusukebe](https://github.com/yusukebe)! - chore: releasing correctly

## 0.4.0

### Minor Changes

- [#763](https://github.com/honojs/middleware/pull/763) [`2a45247d805520c08501727af0d00fd52d34b429`](https://github.com/honojs/middleware/commit/2a45247d805520c08501727af0d00fd52d34b429) Thanks [@yusukebe](https://github.com/yusukebe)! - feat: support enum types for `query`

## 0.3.0

### Minor Changes

- [#695](https://github.com/honojs/middleware/pull/695) [`eda35847916cf7f7e84289eba29a8e5517615c6b`](https://github.com/honojs/middleware/commit/eda35847916cf7f7e84289eba29a8e5517615c6b) Thanks [@bartekbp](https://github.com/bartekbp)! - feat(zod-validator): pass target to a hook

## 0.2.2

### Patch Changes

- [#552](https://github.com/honojs/middleware/pull/552) [`aa055494974eb911ec784e6462691aafefd98125`](https://github.com/honojs/middleware/commit/aa055494974eb911ec784e6462691aafefd98125) Thanks [@EdamAme-x](https://github.com/EdamAme-x)! - fix: support async hook

## 0.2.1

### Patch Changes

- [#431](https://github.com/honojs/middleware/pull/431) [`c721d14d4b90b9702936ad64f54d15bfd7ecfc88`](https://github.com/honojs/middleware/commit/c721d14d4b90b9702936ad64f54d15bfd7ecfc88) Thanks [@MAKS11060](https://github.com/MAKS11060)! - fix export esm module

## 0.2.0

### Minor Changes

- [#411](https://github.com/honojs/middleware/pull/411) [`4875e1c53146d2c67846b8159d3630d465c2a310`](https://github.com/honojs/middleware/commit/4875e1c53146d2c67846b8159d3630d465c2a310) Thanks [@yusukebe](https://github.com/yusukebe)! - feat: support coerce

## 0.1.11

### Patch Changes

- [#215](https://github.com/honojs/middleware/pull/215) [`ab2176b`](https://github.com/honojs/middleware/commit/ab2176bf6ef6cf93b6b4ee67d9614fd83e8d3fb0) Thanks [@yusukebe](https://github.com/yusukebe)! - fix: bump Hono version of `peerDependencie`

## 0.1.10

### Patch Changes

- [#208](https://github.com/honojs/middleware/pull/208) [`46575b4`](https://github.com/honojs/middleware/commit/46575b4395011f4fdf8cd1dcf87f4a662e342cbe) Thanks [@MonsterDeveloper](https://github.com/MonsterDeveloper)! - fix(zod-validator): make validation input optional when schema is optional

## 0.1.9

### Patch Changes

- [#175](https://github.com/honojs/middleware/pull/175) [`a9123dd`](https://github.com/honojs/middleware/commit/a9123dd9e3e90d4d73f495d6b407ebacf9ea0ad8) Thanks [@WildEgo](https://github.com/WildEgo)! - add: Async zod support

## 0.1.8

### Patch Changes

- [#119](https://github.com/honojs/middleware/pull/119) [`3b37eb4`](https://github.com/honojs/middleware/commit/3b37eb4aefb22f6e7711c390f94b022eaa3695cf) Thanks [@yusukebe](https://github.com/yusukebe)! - fix(zod-validator): fixed zod-validator hook types

## 0.1.7

### Patch Changes

- [#116](https://github.com/honojs/middleware/pull/116) [`c271b6d`](https://github.com/honojs/middleware/commit/c271b6d502f1b9e193beb897ff561e08e3877ce5) Thanks [@yusukebe](https://github.com/yusukebe)! - fix(zod-validator): convert TypedResponse to Response in hook

## 0.1.6

### Patch Changes

- [#114](https://github.com/honojs/middleware/pull/114) [`3de3d7c`](https://github.com/honojs/middleware/commit/3de3d7cd1bfc064f4f2c38da35f46da4998dac35) Thanks [@yusukebe](https://github.com/yusukebe)! - fix(zod-validator): support TypedResponse in hook

## 0.1.5

### Patch Changes

- [#107](https://github.com/honojs/middleware/pull/107) [`8eb3967`](https://github.com/honojs/middleware/commit/8eb3967477fce4ccbaf995e811b6d6a014c2e052) Thanks [@yusukebe](https://github.com/yusukebe)! - fix passing data to hook

## 0.1.4

### Patch Changes

- [#104](https://github.com/honojs/middleware/pull/104) [`1fb2ab1`](https://github.com/honojs/middleware/commit/1fb2ab1dad5e1f7c4071e33af342a08690832054) Thanks [@yusukebe](https://github.com/yusukebe)! - fix a Hook type

## 0.1.3

### Patch Changes

- [#87](https://github.com/honojs/middleware/pull/87) [`b7fe359`](https://github.com/honojs/middleware/commit/b7fe359dbc63b493d275df09f00c676de80b3f11) Thanks [@Sacramentix](https://github.com/Sacramentix)! - merge zod import into 1 line

## 0.1.2

### Patch Changes

- [#74](https://github.com/honojs/middleware/pull/74) [`7d84e06`](https://github.com/honojs/middleware/commit/7d84e069431ad15ecfb86c953b940c3bb99a2a96) Thanks [@yusukebe](https://github.com/yusukebe)! - refactor: pass the path to Context for the hook

## 0.1.1

### Patch Changes

- [#69](https://github.com/honojs/middleware/pull/69) [`f263d58`](https://github.com/honojs/middleware/commit/f263d58c8734c09c622fd3e7734fa6bba1a6b706) Thanks [@yusukebe](https://github.com/yusukebe)! - patch: refactor types

## 0.1.0

### Minor Changes

- [#63](https://github.com/honojs/middleware/pull/63) [`3c9dca1`](https://github.com/honojs/middleware/commit/3c9dca1c2d1cb0bff34dfdbedfc4e0a502dafd98) Thanks [@yusukebe](https://github.com/yusukebe)! - feat: Support Hono v3.1.0

## 0.0.6

### Patch Changes

- [#48](https://github.com/honojs/middleware/pull/48) [`81d06f7`](https://github.com/honojs/middleware/commit/81d06f7c9c2fef680f757d3bb25b2d40607d5ce8) Thanks [@yusukebe](https://github.com/yusukebe)! - chore: bump up Hono version to v3

## 0.0.5

### Patch Changes

- [#46](https://github.com/honojs/middleware/pull/46) [`72604c7`](https://github.com/honojs/middleware/commit/72604c74dafffeebf360e35f2fc03d94385214ab) Thanks [@yusukebe](https://github.com/yusukebe)! - refactor: rename `ValidationType` to `ValidationTargets`

## 0.0.4

### Patch Changes

- e1a49cf: bump up Hono version

## 0.0.3

### Patch Changes

- e45805f: bump up Hono version to v3.0.0-rc.9

## 0.0.2

### Patch Changes

- a831696: fix and refactor types

## 0.0.1

### Patch Changes

- 58e42aa: first release
