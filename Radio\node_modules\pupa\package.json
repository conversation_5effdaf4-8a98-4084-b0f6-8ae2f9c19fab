{"name": "pupa", "version": "2.1.1", "description": "Simple micro templating", "license": "MIT", "repository": "sindresorhus/pupa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["string", "formatting", "template", "object", "format", "interpolate", "interpolation", "templating", "expand", "simple", "replace", "placeholders", "values", "transform", "micro"], "dependencies": {"escape-goat": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}