"use strict";const h=require("yaml"),s=require("./server-DVBno7Ee.js");function p(e){const o=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const t in e)if(t!=="default"){const n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(o,t,n.get?n:{enumerable:!0,get:()=>e[t]})}}return o.default=e,Object.freeze(o)}const m=p(h);class r{static create(o){return new r(o)}constructor(o){this.rootDoc=o||{openapi:"3.0.0",info:{title:"app",version:"version"},paths:{},components:{schemas:{},responses:{},parameters:{},examples:{},requestBodies:{},headers:{},securitySchemes:{},links:{},callbacks:{}},tags:[],servers:[]}}getSpec(){return this.rootDoc}getSpecAsJson(o,t){return JSON.stringify(this.rootDoc,o,t)}getSpecAsYaml(o,t){return m.stringify(this.rootDoc,o,t)}static isValidOpenApiVersion(o){o=o||"";const t=/(\d+)\.(\d+).(\d+)/.exec(o);return!!(t&&parseInt(t[1],10)>=3)}addOpenApiVersion(o){if(!r.isValidOpenApiVersion(o))throw new Error("Invalid OpenApi version: "+o+". Follow convention: 3.x.y");return this.rootDoc.openapi=o,this}addInfo(o){return this.rootDoc.info=o,this}addContact(o){return this.rootDoc.info.contact=o,this}addLicense(o){return this.rootDoc.info.license=o,this}addTitle(o){return this.rootDoc.info.title=o,this}addDescription(o){return this.rootDoc.info.description=o,this}addTermsOfService(o){return this.rootDoc.info.termsOfService=o,this}addVersion(o){return this.rootDoc.info.version=o,this}addPath(o,t){return this.rootDoc.paths[o]={...this.rootDoc.paths[o]||{},...t},this}addSchema(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.schemas=this.rootDoc.components.schemas||{},this.rootDoc.components.schemas[o]=t,this}addResponse(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.responses=this.rootDoc.components.responses||{},this.rootDoc.components.responses[o]=t,this}addParameter(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.parameters=this.rootDoc.components.parameters||{},this.rootDoc.components.parameters[o]=t,this}addExample(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.examples=this.rootDoc.components.examples||{},this.rootDoc.components.examples[o]=t,this}addRequestBody(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.requestBodies=this.rootDoc.components.requestBodies||{},this.rootDoc.components.requestBodies[o]=t,this}addHeader(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.headers=this.rootDoc.components.headers||{},this.rootDoc.components.headers[o]=t,this}addSecurityScheme(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.securitySchemes=this.rootDoc.components.securitySchemes||{},this.rootDoc.components.securitySchemes[o]=t,this}addLink(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.links=this.rootDoc.components.links||{},this.rootDoc.components.links[o]=t,this}addCallback(o,t){return this.rootDoc.components=this.rootDoc.components||{},this.rootDoc.components.callbacks=this.rootDoc.components.callbacks||{},this.rootDoc.components.callbacks[o]=t,this}addServer(o){return this.rootDoc.servers=this.rootDoc.servers||[],this.rootDoc.servers.push(o),this}addTag(o){return this.rootDoc.tags=this.rootDoc.tags||[],this.rootDoc.tags.push(o),this}addExternalDocs(o){return this.rootDoc.externalDocs=o,this}}function c(e,o){if(!s.SpecificationExtension.isValidExtension(o))return e[o]}function i(e){return Object.prototype.hasOwnProperty.call(e,"$ref")}function a(e){return!Object.prototype.hasOwnProperty.call(e,"$ref")}const d=Object.freeze(Object.defineProperty({__proto__:null,OpenApiBuilder:r,Server:s.Server,ServerVariable:s.ServerVariable,addExtension:s.addExtension,getExtension:s.getExtension,getPath:c,isReferenceObject:i,isSchemaObject:a},Symbol.toStringTag,{value:"Module"}));exports.OpenApiBuilder=r;exports.getPath=c;exports.isReferenceObject=i;exports.isSchemaObject=a;exports.oas30=d;
//# sourceMappingURL=oas30-E9r1WxRR.js.map
