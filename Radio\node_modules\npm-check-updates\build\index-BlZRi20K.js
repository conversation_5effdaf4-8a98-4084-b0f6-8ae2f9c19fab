"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const x=require("node:process"),nr=require("node:child_process"),Se=require("node:url"),T=require("node:path"),W=require("node:util"),ce=require("node:os"),J=require("./index.js"),N=require("node:fs"),qt=require("node:constants"),vu=require("node:stream"),kt=require("node:assert"),Z=require("./index-vtankfkr.js"),Dr=require("node:events"),ir=require("node:http"),Mt=require("./index-BnIU43YD.js");var ge=typeof document<"u"?document.currentScript:null,Je,Ru;function or(){if(Ru)return Je;Ru=1;var e=qt,u=process.cwd,t=null,i=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return t||(t=u.call(process)),t};try{process.cwd()}catch{}if(typeof process.chdir=="function"){var s=process.chdir;process.chdir=function(r){t=null,s.call(process,r)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,s)}Je=o;function o(r){e.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&D(r),r.lutimes||a(r),r.chown=E(r.chown),r.fchown=E(r.fchown),r.lchown=E(r.lchown),r.chmod=c(r.chmod),r.fchmod=c(r.fchmod),r.lchmod=c(r.lchmod),r.chownSync=h(r.chownSync),r.fchownSync=h(r.fchownSync),r.lchownSync=h(r.lchownSync),r.chmodSync=l(r.chmodSync),r.fchmodSync=l(r.fchmodSync),r.lchmodSync=l(r.lchmodSync),r.stat=F(r.stat),r.fstat=F(r.fstat),r.lstat=F(r.lstat),r.statSync=d(r.statSync),r.fstatSync=d(r.fstatSync),r.lstatSync=d(r.lstatSync),r.chmod&&!r.lchmod&&(r.lchmod=function(n,f,C){C&&process.nextTick(C)},r.lchmodSync=function(){}),r.chown&&!r.lchown&&(r.lchown=function(n,f,C,g){g&&process.nextTick(g)},r.lchownSync=function(){}),i==="win32"&&(r.rename=typeof r.rename!="function"?r.rename:function(n){function f(C,g,m){var v=Date.now(),B=0;n(C,g,function A(_){if(_&&(_.code==="EACCES"||_.code==="EPERM"||_.code==="EBUSY")&&Date.now()-v<6e4){setTimeout(function(){r.stat(g,function(k,$){k&&k.code==="ENOENT"?n(C,g,A):m(_)})},B),B<100&&(B+=10);return}m&&m(_)})}return Object.setPrototypeOf&&Object.setPrototypeOf(f,n),f}(r.rename)),r.read=typeof r.read!="function"?r.read:function(n){function f(C,g,m,v,B,A){var _;if(A&&typeof A=="function"){var k=0;_=function($,ee,Y){if($&&$.code==="EAGAIN"&&k<10)return k++,n.call(r,C,g,m,v,B,_);A.apply(this,arguments)}}return n.call(r,C,g,m,v,B,_)}return Object.setPrototypeOf&&Object.setPrototypeOf(f,n),f}(r.read),r.readSync=typeof r.readSync!="function"?r.readSync:function(n){return function(f,C,g,m,v){for(var B=0;;)try{return n.call(r,f,C,g,m,v)}catch(A){if(A.code==="EAGAIN"&&B<10){B++;continue}throw A}}}(r.readSync);function D(n){n.lchmod=function(f,C,g){n.open(f,e.O_WRONLY|e.O_SYMLINK,C,function(m,v){if(m){g&&g(m);return}n.fchmod(v,C,function(B){n.close(v,function(A){g&&g(B||A)})})})},n.lchmodSync=function(f,C){var g=n.openSync(f,e.O_WRONLY|e.O_SYMLINK,C),m=!0,v;try{v=n.fchmodSync(g,C),m=!1}finally{if(m)try{n.closeSync(g)}catch{}else n.closeSync(g)}return v}}function a(n){e.hasOwnProperty("O_SYMLINK")&&n.futimes?(n.lutimes=function(f,C,g,m){n.open(f,e.O_SYMLINK,function(v,B){if(v){m&&m(v);return}n.futimes(B,C,g,function(A){n.close(B,function(_){m&&m(A||_)})})})},n.lutimesSync=function(f,C,g){var m=n.openSync(f,e.O_SYMLINK),v,B=!0;try{v=n.futimesSync(m,C,g),B=!1}finally{if(B)try{n.closeSync(m)}catch{}else n.closeSync(m)}return v}):n.futimes&&(n.lutimes=function(f,C,g,m){m&&process.nextTick(m)},n.lutimesSync=function(){})}function c(n){return n&&function(f,C,g){return n.call(r,f,C,function(m){p(m)&&(m=null),g&&g.apply(this,arguments)})}}function l(n){return n&&function(f,C){try{return n.call(r,f,C)}catch(g){if(!p(g))throw g}}}function E(n){return n&&function(f,C,g,m){return n.call(r,f,C,g,function(v){p(v)&&(v=null),m&&m.apply(this,arguments)})}}function h(n){return n&&function(f,C,g){try{return n.call(r,f,C,g)}catch(m){if(!p(m))throw m}}}function F(n){return n&&function(f,C,g){typeof C=="function"&&(g=C,C=null);function m(v,B){B&&(B.uid<0&&(B.uid+=4294967296),B.gid<0&&(B.gid+=4294967296)),g&&g.apply(this,arguments)}return C?n.call(r,f,C,m):n.call(r,f,m)}}function d(n){return n&&function(f,C){var g=C?n.call(r,f,C):n.call(r,f);return g&&(g.uid<0&&(g.uid+=4294967296),g.gid<0&&(g.gid+=4294967296)),g}}function p(n){if(!n||n.code==="ENOSYS")return!0;var f=!process.getuid||process.getuid()!==0;return!!(f&&(n.code==="EINVAL"||n.code==="EPERM"))}}return Je}var He,Iu;function sr(){if(Iu)return He;Iu=1;var e=vu.Stream;He=u;function u(t){return{ReadStream:i,WriteStream:s};function i(o,r){if(!(this instanceof i))return new i(o,r);e.call(this);var D=this;this.path=o,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,r=r||{};for(var a=Object.keys(r),c=0,l=a.length;c<l;c++){var E=a[c];this[E]=r[E]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){D._read()});return}t.open(this.path,this.flags,this.mode,function(h,F){if(h){D.emit("error",h),D.readable=!1;return}D.fd=F,D.emit("open",F),D._read()})}function s(o,r){if(!(this instanceof s))return new s(o,r);e.call(this),this.path=o,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,r=r||{};for(var D=Object.keys(r),a=0,c=D.length;a<c;a++){var l=D[a];this[l]=r[l]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}return He}var Ke,Nu;function ar(){if(Nu)return Ke;Nu=1,Ke=u;var e=Object.getPrototypeOf||function(t){return t.__proto__};function u(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var i={__proto__:e(t)};else var i=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(s){Object.defineProperty(i,s,Object.getOwnPropertyDescriptor(t,s))}),i}return Ke}var xe,Tu;function cr(){if(Tu)return xe;Tu=1;var e=N,u=or(),t=sr(),i=ar(),s=W,o,r;typeof Symbol=="function"&&typeof Symbol.for=="function"?(o=Symbol.for("graceful-fs.queue"),r=Symbol.for("graceful-fs.previous")):(o="___graceful-fs.queue",r="___graceful-fs.previous");function D(){}function a(n,f){Object.defineProperty(n,o,{get:function(){return f}})}var c=D;if(s.debuglog?c=s.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(c=function(){var n=s.format.apply(s,arguments);n="GFS4: "+n.split(/\n/).join(`
GFS4: `),console.error(n)}),!e[o]){var l=J.commonjsGlobal[o]||[];a(e,l),e.close=function(n){function f(C,g){return n.call(e,C,function(m){m||d(),typeof g=="function"&&g.apply(this,arguments)})}return Object.defineProperty(f,r,{value:n}),f}(e.close),e.closeSync=function(n){function f(C){n.apply(e,arguments),d()}return Object.defineProperty(f,r,{value:n}),f}(e.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){c(e[o]),kt.equal(e[o].length,0)})}J.commonjsGlobal[o]||a(J.commonjsGlobal,e[o]),xe=E(i(e)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!e.__patched&&(xe=E(e),e.__patched=!0);function E(n){u(n),n.gracefulify=E,n.createReadStream=Ge,n.createWriteStream=Ve;var f=n.readFile;n.readFile=C;function C(y,w,b){return typeof w=="function"&&(b=w,w=null),L(y,w,b);function L(j,P,R,I){return f(j,P,function(S){S&&(S.code==="EMFILE"||S.code==="ENFILE")?h([L,[j,P,R],S,I||Date.now(),Date.now()]):typeof R=="function"&&R.apply(this,arguments)})}}var g=n.writeFile;n.writeFile=m;function m(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return g(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var v=n.appendFile;v&&(n.appendFile=B);function B(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return v(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var A=n.copyFile;A&&(n.copyFile=_);function _(y,w,b,L){return typeof b=="function"&&(L=b,b=0),j(y,w,b,L);function j(P,R,I,S,q){return A(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var k=n.readdir;n.readdir=ee;var $=/^v[0-5]\./;function ee(y,w,b){typeof w=="function"&&(b=w,w=null);var L=$.test(process.version)?function(R,I,S,q){return k(R,j(R,I,S,q))}:function(R,I,S,q){return k(R,I,j(R,I,S,q))};return L(y,w,b);function j(P,R,I,S){return function(q,O){q&&(q.code==="EMFILE"||q.code==="ENFILE")?h([L,[P,R,I],q,S||Date.now(),Date.now()]):(O&&O.sort&&O.sort(),typeof I=="function"&&I.call(this,q,O))}}}if(process.version.substr(0,4)==="v0.8"){var Y=t(n);G=Y.ReadStream,V=Y.WriteStream}var H=n.ReadStream;H&&(G.prototype=Object.create(H.prototype),G.prototype.open=Ue);var fe=n.WriteStream;fe&&(V.prototype=Object.create(fe.prototype),V.prototype.open=We),Object.defineProperty(n,"ReadStream",{get:function(){return G},set:function(y){G=y},enumerable:!0,configurable:!0}),Object.defineProperty(n,"WriteStream",{get:function(){return V},set:function(y){V=y},enumerable:!0,configurable:!0});var me=G;Object.defineProperty(n,"FileReadStream",{get:function(){return me},set:function(y){me=y},enumerable:!0,configurable:!0});var ye=V;Object.defineProperty(n,"FileWriteStream",{get:function(){return ye},set:function(y){ye=y},enumerable:!0,configurable:!0});function G(y,w){return this instanceof G?(H.apply(this,arguments),this):G.apply(Object.create(G.prototype),arguments)}function Ue(){var y=this;Fe(y.path,y.flags,y.mode,function(w,b){w?(y.autoClose&&y.destroy(),y.emit("error",w)):(y.fd=b,y.emit("open",b),y.read())})}function V(y,w){return this instanceof V?(fe.apply(this,arguments),this):V.apply(Object.create(V.prototype),arguments)}function We(){var y=this;Fe(y.path,y.flags,y.mode,function(w,b){w?(y.destroy(),y.emit("error",w)):(y.fd=b,y.emit("open",b))})}function Ge(y,w){return new n.ReadStream(y,w)}function Ve(y,w){return new n.WriteStream(y,w)}var Ye=n.open;n.open=Fe;function Fe(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return Ye(P,R,I,function(O,rr){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}return n}function h(n){c("ENQUEUE",n[0].name,n[1]),e[o].push(n),p()}var F;function d(){for(var n=Date.now(),f=0;f<e[o].length;++f)e[o][f].length>2&&(e[o][f][3]=n,e[o][f][4]=n);p()}function p(){if(clearTimeout(F),F=void 0,e[o].length!==0){var n=e[o].shift(),f=n[0],C=n[1],g=n[2],m=n[3],v=n[4];if(m===void 0)c("RETRY",f.name,C),f.apply(null,C);else if(Date.now()-m>=6e4){c("TIMEOUT",f.name,C);var B=C.pop();typeof B=="function"&&B.call(null,g)}else{var A=Date.now()-v,_=Math.max(v-m,1),k=Math.min(_*1.2,100);A>=k?(c("RETRY",f.name,C),f.apply(null,C.concat([m]))):e[o].push(n)}F===void 0&&(F=setTimeout(p,0))}}return xe}var lr=cr();const $e=J.getDefaultExportFromCjs(lr),le=ce.homedir(),{env:Ce}=process,$u=Ce.XDG_DATA_HOME||(le?T.join(le,".local","share"):void 0),Pe=Ce.XDG_CONFIG_HOME||(le?T.join(le,".config"):void 0);Ce.XDG_STATE_HOME||le&&T.join(le,".local","state");Ce.XDG_CACHE_HOME||le&&T.join(le,".cache");Ce.XDG_RUNTIME_DIR;const fr=(Ce.XDG_DATA_DIRS||"/usr/local/share/:/usr/share/").split(":");$u&&fr.unshift($u);const Fr=(Ce.XDG_CONFIG_DIRS||"/etc/xdg").split(":");Pe&&Fr.unshift(Pe);const ne=(e,u)=>function(...i){return e.apply(void 0,i).catch(u)},re=(e,u)=>function(...i){try{return e.apply(void 0,i)}catch(s){return u(s)}},hr=x.getuid?!x.getuid():!1,dr=1e4,z=()=>{},U={isChangeErrorOk:e=>{if(!U.isNodeError(e))return!1;const{code:u}=e;return u==="ENOSYS"||!hr&&(u==="EINVAL"||u==="EPERM")},isNodeError:e=>e instanceof Error,isRetriableError:e=>{if(!U.isNodeError(e))return!1;const{code:u}=e;return u==="EMFILE"||u==="ENFILE"||u==="EAGAIN"||u==="EBUSY"||u==="EACCESS"||u==="EACCES"||u==="EACCS"||u==="EPERM"},onChangeError:e=>{if(!U.isNodeError(e))throw e;if(!U.isChangeErrorOk(e))throw e}};class pr{constructor(){this.interval=25,this.intervalId=void 0,this.limit=dr,this.queueActive=new Set,this.queueWaiting=new Set,this.init=()=>{this.intervalId||(this.intervalId=setInterval(this.tick,this.interval))},this.reset=()=>{this.intervalId&&(clearInterval(this.intervalId),delete this.intervalId)},this.add=u=>{this.queueWaiting.add(u),this.queueActive.size<this.limit/2?this.tick():this.init()},this.remove=u=>{this.queueWaiting.delete(u),this.queueActive.delete(u)},this.schedule=()=>new Promise(u=>{const t=()=>this.remove(i),i=()=>u(t);this.add(i)}),this.tick=()=>{if(!(this.queueActive.size>=this.limit)){if(!this.queueWaiting.size)return this.reset();for(const u of this.queueWaiting){if(this.queueActive.size>=this.limit)break;this.queueWaiting.delete(u),this.queueActive.add(u),u()}}}}}const Er=new pr,De=(e,u)=>function(i){return function s(...o){return Er.schedule().then(r=>{const D=c=>(r(),c),a=c=>{if(r(),Date.now()>=i)throw c;if(u(c)){const l=Math.round(100*Math.random());return new Promise(h=>setTimeout(h,l)).then(()=>s.apply(void 0,o))}throw c};return e.apply(void 0,o).then(D,a)})}},ie=(e,u)=>function(i){return function s(...o){try{return e.apply(void 0,o)}catch(r){if(Date.now()>i)throw r;if(u(r))return s.apply(void 0,o);throw r}}},K={attempt:{chmod:ne(W.promisify(N.chmod),U.onChangeError),chown:ne(W.promisify(N.chown),U.onChangeError),close:ne(W.promisify(N.close),z),fsync:ne(W.promisify(N.fsync),z),mkdir:ne(W.promisify(N.mkdir),z),realpath:ne(W.promisify(N.realpath),z),stat:ne(W.promisify(N.stat),z),unlink:ne(W.promisify(N.unlink),z),chmodSync:re(N.chmodSync,U.onChangeError),chownSync:re(N.chownSync,U.onChangeError),closeSync:re(N.closeSync,z),existsSync:re(N.existsSync,z),fsyncSync:re(N.fsync,z),mkdirSync:re(N.mkdirSync,z),realpathSync:re(N.realpathSync,z),statSync:re(N.statSync,z),unlinkSync:re(N.unlinkSync,z)},retry:{close:De(W.promisify(N.close),U.isRetriableError),fsync:De(W.promisify(N.fsync),U.isRetriableError),open:De(W.promisify(N.open),U.isRetriableError),readFile:De(W.promisify(N.readFile),U.isRetriableError),rename:De(W.promisify(N.rename),U.isRetriableError),stat:De(W.promisify(N.stat),U.isRetriableError),write:De(W.promisify(N.write),U.isRetriableError),writeFile:De(W.promisify(N.writeFile),U.isRetriableError),closeSync:ie(N.closeSync,U.isRetriableError),fsyncSync:ie(N.fsyncSync,U.isRetriableError),openSync:ie(N.openSync,U.isRetriableError),readFileSync:ie(N.readFileSync,U.isRetriableError),renameSync:ie(N.renameSync,U.isRetriableError),statSync:ie(N.statSync,U.isRetriableError),writeSync:ie(N.writeSync,U.isRetriableError),writeFileSync:ie(N.writeFileSync,U.isRetriableError)}},Cr="utf8",Pu=438,gr=511,mr={},yr=ce.userInfo().uid,Br=ce.userInfo().gid,Ar=1e3,vr=!!x.getuid;x.getuid&&x.getuid();const Lu=128,br=e=>e instanceof Error&&"code"in e,ju=e=>typeof e=="string",Xe=e=>e===void 0,wr=x.platform==="linux",Ut=x.platform==="win32",bu=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];Ut||bu.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");wr&&bu.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED");class Sr{constructor(){this.callbacks=new Set,this.exited=!1,this.exit=u=>{if(!this.exited){this.exited=!0;for(const t of this.callbacks)t();u&&(Ut&&u!=="SIGINT"&&u!=="SIGTERM"&&u!=="SIGKILL"?x.kill(x.pid,"SIGTERM"):x.kill(x.pid,u))}},this.hook=()=>{x.once("exit",()=>this.exit());for(const u of bu)try{x.once(u,()=>this.exit(u))}catch{}},this.register=u=>(this.callbacks.add(u),()=>{this.callbacks.delete(u)}),this.hook()}}const _r=new Sr,xr=_r.register,X={store:{},create:e=>{const u=`000000${Math.floor(Math.random()*16777215).toString(16)}`.slice(-6),s=`.tmp-${Date.now().toString().slice(-10)}${u}`;return`${e}${s}`},get:(e,u,t=!0)=>{const i=X.truncate(u(e));return i in X.store?X.get(e,u,t):(X.store[i]=t,[i,()=>delete X.store[i]])},purge:e=>{X.store[e]&&(delete X.store[e],K.attempt.unlink(e))},purgeSync:e=>{X.store[e]&&(delete X.store[e],K.attempt.unlinkSync(e))},purgeSyncAll:()=>{for(const e in X.store)X.purgeSync(e)},truncate:e=>{const u=T.basename(e);if(u.length<=Lu)return e;const t=/^(\.?)(.*?)((?:\.[^.]+)?(?:\.tmp-\d{10}[a-f0-9]{6})?)$/.exec(u);if(!t)return e;const i=u.length-Lu;return`${e.slice(0,-u.length)}${t[1]}${t[2].slice(0,-i)}${t[3]}`}};xr(X.purgeSyncAll);function Bu(e,u,t=mr){if(ju(t))return Bu(e,u,{encoding:t});const i=Date.now()+((t.timeout??Ar)||-1);let s=null,o=null,r=null;try{const D=K.attempt.realpathSync(e),a=!!D;e=D||e,[o,s]=X.get(e,t.tmpCreate||X.create,t.tmpPurge!==!1);const c=vr&&Xe(t.chown),l=Xe(t.mode);if(a&&(c||l)){const E=K.attempt.statSync(e);E&&(t={...t},c&&(t.chown={uid:E.uid,gid:E.gid}),l&&(t.mode=E.mode))}if(!a){const E=T.dirname(e);K.attempt.mkdirSync(E,{mode:gr,recursive:!0})}r=K.retry.openSync(i)(o,"w",t.mode||Pu),t.tmpCreated&&t.tmpCreated(o),ju(u)?K.retry.writeSync(i)(r,u,0,t.encoding||Cr):Xe(u)||K.retry.writeSync(i)(r,u,0,u.length,0),t.fsync!==!1&&(t.fsyncWait!==!1?K.retry.fsyncSync(i)(r):K.attempt.fsync(r)),K.retry.closeSync(i)(r),r=null,t.chown&&(t.chown.uid!==yr||t.chown.gid!==Br)&&K.attempt.chownSync(o,t.chown.uid,t.chown.gid),t.mode&&t.mode!==Pu&&K.attempt.chmodSync(o,t.mode);try{K.retry.renameSync(i)(o,e)}catch(E){if(!br(E)||E.code!=="ENAMETOOLONG")throw E;K.retry.renameSync(i)(o,X.truncate(e))}s(),o=null}finally{r&&K.attempt.closeSync(r),o&&X.purge(o)}}const Ee=e=>{const u=typeof e;return e!==null&&(u==="object"||u==="function")},ze=new Set(["__proto__","prototype","constructor"]),Or=new Set("0123456789");function ke(e){const u=[];let t="",i="start",s=!1;for(const o of e)switch(o){case"\\":{if(i==="index")throw new Error("Invalid character in an index");if(i==="indexEnd")throw new Error("Invalid character after an index");s&&(t+=o),i="property",s=!s;break}case".":{if(i==="index")throw new Error("Invalid character in an index");if(i==="indexEnd"){i="property";break}if(s){s=!1,t+=o;break}if(ze.has(t))return[];u.push(t),t="",i="property";break}case"[":{if(i==="index")throw new Error("Invalid character in an index");if(i==="indexEnd"){i="index";break}if(s){s=!1,t+=o;break}if(i==="property"){if(ze.has(t))return[];u.push(t),t=""}i="index";break}case"]":{if(i==="index"){u.push(Number.parseInt(t,10)),t="",i="indexEnd";break}if(i==="indexEnd")throw new Error("Invalid character after an index")}default:{if(i==="index"&&!Or.has(o))throw new Error("Invalid character in an index");if(i==="indexEnd")throw new Error("Invalid character after an index");i==="start"&&(i="property"),s&&(s=!1,t+="\\"),t+=o}}switch(s&&(t+="\\"),i){case"property":{if(ze.has(t))return[];u.push(t);break}case"index":throw new Error("Index was not closed");case"start":{u.push("");break}}return u}function wu(e,u){if(typeof u!="number"&&Array.isArray(e)){const t=Number.parseInt(u,10);return Number.isInteger(t)&&e[t]===e[u]}return!1}function Wt(e,u){if(wu(e,u))throw new Error("Cannot use string index")}function Rr(e,u,t){if(!Ee(e)||typeof u!="string")return t===void 0?e:t;const i=ke(u);if(i.length===0)return t;for(let s=0;s<i.length;s++){const o=i[s];if(wu(e,o)?e=s===i.length-1?void 0:null:e=e[o],e==null){if(s!==i.length-1)return t;break}}return e===void 0?t:e}function qu(e,u,t){if(!Ee(e)||typeof u!="string")return e;const i=e,s=ke(u);for(let o=0;o<s.length;o++){const r=s[o];Wt(e,r),o===s.length-1?e[r]=t:Ee(e[r])||(e[r]=typeof s[o+1]=="number"?[]:{}),e=e[r]}return i}function Ir(e,u){if(!Ee(e)||typeof u!="string")return!1;const t=ke(u);for(let i=0;i<t.length;i++){const s=t[i];if(Wt(e,s),i===t.length-1)return delete e[s],!0;if(e=e[s],!Ee(e))return!1}}function Nr(e,u){if(!Ee(e)||typeof u!="string")return!1;const t=ke(u);if(t.length===0)return!1;for(const i of t){if(!Ee(e)||!(i in e)||wu(e,i))return!1;e=e[i]}return!0}function Tr(e,u){const t=u?T.join(e,"config.json"):T.join("configstore",`${e}.json`),i=Pe??$e.mkdtempSync($e.realpathSync(ce.tmpdir())+T.sep);return T.join(i,t)}const ku="You don't have access to this file.",$r={mode:448,recursive:!0},Mu={mode:384};class Pr{constructor(u,t,i={}){this._path=i.configPath??Tr(u,i.globalConfigPath),t&&(this.all={...t,...this.all})}get all(){try{return JSON.parse($e.readFileSync(this._path,"utf8"))}catch(u){if(u.code==="ENOENT")return{};if(u.code==="EACCES"&&(u.message=`${u.message}
${ku}
`),u.name==="SyntaxError")return Bu(this._path,"",Mu),{};throw u}}set all(u){try{$e.mkdirSync(T.dirname(this._path),$r),Bu(this._path,JSON.stringify(u,void 0,"	"),Mu)}catch(t){throw t.code==="EACCES"&&(t.message=`${t.message}
${ku}
`),t}}get size(){return Object.keys(this.all||{}).length}get(u){return Rr(this.all,u)}set(u,t){const i=this.all;if(arguments.length===1)for(const s of Object.keys(u))qu(i,s,u[s]);else qu(i,u,t);this.all=i}has(u){return Nr(this.all,u)}delete(u){const t=this.all;Ir(t,u),this.all=t}clear(){this.all={}}get path(){return this._path}}var Lr=J.requireDiff();const jr=J.getDefaultExportFromCjs(Lr);var qr=J.requireGt();const kr=J.getDefaultExportFromCjs(qr);class Uu extends Error{constructor(u,t,i){const s=u.status||u.status===0?u.status:"",o=u.statusText||"",r=`${s} ${o}`.trim(),D=r?`status code ${r}`:"an unknown error";super(`Request failed with ${D}: ${t.method} ${t.url}`),Object.defineProperty(this,"response",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"request",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"options",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="HTTPError",this.response=u,this.request=t,this.options=i}}class Gt extends Error{constructor(u){super(`Request timed out: ${u.method} ${u.url}`),Object.defineProperty(this,"request",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="TimeoutError",this.request=u}}const Te=e=>e!==null&&typeof e=="object",Oe=(...e)=>{for(const u of e)if((!Te(u)||Array.isArray(u))&&u!==void 0)throw new TypeError("The `options` argument must be an object");return Su({},...e)},Vt=(e={},u={})=>{const t=new globalThis.Headers(e),i=u instanceof globalThis.Headers,s=new globalThis.Headers(u);for(const[o,r]of s.entries())i&&r==="undefined"||r===void 0?t.delete(o):t.set(o,r);return t},Su=(...e)=>{let u={},t={};for(const i of e)if(Array.isArray(i))Array.isArray(u)||(u=[]),u=[...u,...i];else if(Te(i)){for(let[s,o]of Object.entries(i))Te(o)&&s in u&&(o=Su(u[s],o)),u={...u,[s]:o};Te(i.headers)&&(t=Vt(t,i.headers),u.headers=t)}return u},Mr=(()=>{let e=!1,u=!1;const t=typeof globalThis.ReadableStream=="function",i=typeof globalThis.Request=="function";if(t&&i)try{u=new globalThis.Request("https://empty.invalid",{body:new globalThis.ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type")}catch(s){if(s instanceof Error&&s.message==="unsupported BodyInit type")return!1;throw s}return e&&!u})(),Ur=typeof globalThis.AbortController=="function",Wr=typeof globalThis.ReadableStream=="function",Gr=typeof globalThis.FormData=="function",Yt=["get","post","put","patch","head","delete"],Vr={json:"application/json",text:"text/*",formData:"multipart/form-data",arrayBuffer:"*/*",blob:"*/*"},Qe=2147483647,Jt=Symbol("stop"),Yr={json:!0,parseJson:!0,stringifyJson:!0,searchParams:!0,prefixUrl:!0,retry:!0,timeout:!0,hooks:!0,throwHttpErrors:!0,onDownloadProgress:!0,fetch:!0},Jr={method:!0,headers:!0,body:!0,mode:!0,credentials:!0,cache:!0,redirect:!0,referrer:!0,referrerPolicy:!0,integrity:!0,keepalive:!0,signal:!0,window:!0,dispatcher:!0,duplex:!0,priority:!0},Hr=e=>Yt.includes(e)?e.toUpperCase():e,Kr=["get","put","head","delete","options","trace"],Xr=[408,413,429,500,502,503,504],Ht=[413,429,503],Wu={limit:2,methods:Kr,statusCodes:Xr,afterStatusCodes:Ht,maxRetryAfter:Number.POSITIVE_INFINITY,backoffLimit:Number.POSITIVE_INFINITY,delay:e=>.3*2**(e-1)*1e3},zr=(e={})=>{if(typeof e=="number")return{...Wu,limit:e};if(e.methods&&!Array.isArray(e.methods))throw new Error("retry.methods must be an array");if(e.statusCodes&&!Array.isArray(e.statusCodes))throw new Error("retry.statusCodes must be an array");return{...Wu,...e,afterStatusCodes:Ht}};async function Qr(e,u,t,i){return new Promise((s,o)=>{const r=setTimeout(()=>{t&&t.abort(),o(new Gt(e))},i.timeout);i.fetch(e,u).then(s).catch(o).then(()=>{clearTimeout(r)})})}async function Zr(e,{signal:u}){return new Promise((t,i)=>{u&&(u.throwIfAborted(),u.addEventListener("abort",s,{once:!0}));function s(){clearTimeout(o),i(u.reason)}const o=setTimeout(()=>{u?.removeEventListener("abort",s),t()},e)})}const en=(e,u)=>{const t={};for(const i in u)!(i in Jr)&&!(i in Yr)&&!(i in e)&&(t[i]=u[i]);return t};class Le{static create(u,t){const i=new Le(u,t),s=async()=>{if(typeof i._options.timeout=="number"&&i._options.timeout>Qe)throw new RangeError(`The \`timeout\` option cannot be greater than ${Qe}`);await Promise.resolve();let D=await i._fetch();for(const a of i._options.hooks.afterResponse){const c=await a(i.request,i._options,i._decorateResponse(D.clone()));c instanceof globalThis.Response&&(D=c)}if(i._decorateResponse(D),!D.ok&&i._options.throwHttpErrors){let a=new Uu(D,i.request,i._options);for(const c of i._options.hooks.beforeError)a=await c(a);throw a}if(i._options.onDownloadProgress){if(typeof i._options.onDownloadProgress!="function")throw new TypeError("The `onDownloadProgress` option must be a function");if(!Wr)throw new Error("Streams are not supported in your environment. `ReadableStream` is missing.");return i._stream(D.clone(),i._options.onDownloadProgress)}return D},r=i._options.retry.methods.includes(i.request.method.toLowerCase())?i._retry(s):s();for(const[D,a]of Object.entries(Vr))r[D]=async()=>{i.request.headers.set("accept",i.request.headers.get("accept")||a);const l=(await r).clone();if(D==="json"){if(l.status===204||(await l.clone().arrayBuffer()).byteLength===0)return"";if(t.parseJson)return t.parseJson(await l.text())}return l[D]()};return r}constructor(u,t={}){Object.defineProperty(this,"request",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"abortController",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_retryCount",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_input",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_options",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this._input=u;const i=this._input instanceof Request&&"credentials"in Request.prototype?this._input.credentials:void 0;if(this._options={...i&&{credentials:i},...t,headers:Vt(this._input.headers,t.headers),hooks:Su({beforeRequest:[],beforeRetry:[],beforeError:[],afterResponse:[]},t.hooks),method:Hr(t.method??this._input.method),prefixUrl:String(t.prefixUrl||""),retry:zr(t.retry),throwHttpErrors:t.throwHttpErrors!==!1,timeout:t.timeout??1e4,fetch:t.fetch??globalThis.fetch.bind(globalThis)},typeof this._input!="string"&&!(this._input instanceof URL||this._input instanceof globalThis.Request))throw new TypeError("`input` must be a string, URL, or Request");if(this._options.prefixUrl&&typeof this._input=="string"){if(this._input.startsWith("/"))throw new Error("`input` must not begin with a slash when using `prefixUrl`");this._options.prefixUrl.endsWith("/")||(this._options.prefixUrl+="/"),this._input=this._options.prefixUrl+this._input}if(Ur){if(this.abortController=new globalThis.AbortController,this._options.signal){const s=this._options.signal;this._options.signal.addEventListener("abort",()=>{this.abortController.abort(s.reason)})}this._options.signal=this.abortController.signal}if(Mr&&(this._options.duplex="half"),this._options.json!==void 0&&(this._options.body=this._options.stringifyJson?.(this._options.json)??JSON.stringify(this._options.json),this._options.headers.set("content-type",this._options.headers.get("content-type")??"application/json")),this.request=new globalThis.Request(this._input,this._options),this._options.searchParams){const o="?"+(typeof this._options.searchParams=="string"?this._options.searchParams.replace(/^\?/,""):new URLSearchParams(this._options.searchParams).toString()),r=this.request.url.replace(/(?:\?.*?)?(?=#|$)/,o);(Gr&&this._options.body instanceof globalThis.FormData||this._options.body instanceof URLSearchParams)&&!(this._options.headers&&this._options.headers["content-type"])&&this.request.headers.delete("content-type"),this.request=new globalThis.Request(new globalThis.Request(r,{...this.request}),this._options)}}_calculateRetryDelay(u){if(this._retryCount++,this._retryCount<=this._options.retry.limit&&!(u instanceof Gt)){if(u instanceof Uu){if(!this._options.retry.statusCodes.includes(u.response.status))return 0;const i=u.response.headers.get("Retry-After");if(i&&this._options.retry.afterStatusCodes.includes(u.response.status)){let s=Number(i)*1e3;Number.isNaN(s)&&(s=Date.parse(i)-Date.now());const o=this._options.retry.maxRetryAfter??s;return s<o?s:o}if(u.response.status===413)return 0}const t=this._options.retry.delay(this._retryCount);return Math.min(this._options.retry.backoffLimit,t)}return 0}_decorateResponse(u){return this._options.parseJson&&(u.json=async()=>this._options.parseJson(await u.text())),u}async _retry(u){try{return await u()}catch(t){const i=Math.min(this._calculateRetryDelay(t),Qe);if(i!==0&&this._retryCount>0){await Zr(i,{signal:this._options.signal});for(const s of this._options.hooks.beforeRetry)if(await s({request:this.request,options:this._options,error:t,retryCount:this._retryCount})===Jt)return;return this._retry(u)}throw t}}async _fetch(){for(const i of this._options.hooks.beforeRequest){const s=await i(this.request,this._options);if(s instanceof Request){this.request=s;break}if(s instanceof Response)return s}const u=en(this.request,this._options),t=this.request;return this.request=t.clone(),this._options.timeout===!1?this._options.fetch(t,u):Qr(t,u,this.abortController,this._options)}_stream(u,t){const i=Number(u.headers.get("content-length"))||0;let s=0;return u.status===204?(t&&t({percent:1,totalBytes:i,transferredBytes:s},new Uint8Array),new globalThis.Response(null,{status:u.status,statusText:u.statusText,headers:u.headers})):new globalThis.Response(new globalThis.ReadableStream({async start(o){const r=u.body.getReader();t&&t({percent:0,transferredBytes:0,totalBytes:i},new Uint8Array);async function D(){const{done:a,value:c}=await r.read();if(a){o.close();return}if(t){s+=c.byteLength;const l=i===0?0:s/i;t({percent:l,transferredBytes:s,totalBytes:i},c)}o.enqueue(c),await D()}await D()}}),{status:u.status,statusText:u.statusText,headers:u.headers})}}/*! MIT License © Sindre Sorhus */const Au=e=>{const u=(t,i)=>Le.create(t,Oe(e,i));for(const t of Yt)u[t]=(i,s)=>Le.create(i,Oe(e,s,{method:t}));return u.create=t=>Au(Oe(t)),u.extend=t=>Au(Oe(e,t)),u.stop=Jt,u},un=Au();var he={},oe={},Gu;function tn(){if(Gu)return oe;Gu=1,oe.parse=oe.decode=i,oe.stringify=oe.encode=u,oe.safe=o,oe.unsafe=r;var e=typeof process<"u"&&process.platform==="win32"?`\r
`:`
`;function u(D,a){var c=[],l="";typeof a=="string"?a={section:a,whitespace:!1}:(a=a||{},a.whitespace=a.whitespace===!0);var E=a.whitespace?" = ":"=";return Object.keys(D).forEach(function(h,F,d){var p=D[h];p&&Array.isArray(p)?p.forEach(function(n){l+=o(h+"[]")+E+o(n)+`
`}):p&&typeof p=="object"?c.push(h):l+=o(h)+E+o(p)+e}),a.section&&l.length&&(l="["+o(a.section)+"]"+e+l),c.forEach(function(h,F,d){var p=t(h).join("\\."),n=(a.section?a.section+".":"")+p,f=u(D[h],{section:n,whitespace:a.whitespace});l.length&&f.length&&(l+=e),l+=f}),l}function t(D){return D.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(function(a){return a.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")})}function i(D){var a={},c=a,l=null,E=/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i,h=D.split(/[\r\n]+/g);return h.forEach(function(F,d,p){if(!(!F||F.match(/^\s*[;#]/))){var n=F.match(E);if(n){if(n[1]!==void 0){if(l=r(n[1]),l==="__proto__"){c={};return}c=a[l]=a[l]||{};return}var f=r(n[2]);if(f!=="__proto__"){var C=n[3]?r(n[4]):!0;switch(C){case"true":case"false":case"null":C=JSON.parse(C)}if(f.length>2&&f.slice(-2)==="[]"){if(f=f.substring(0,f.length-2),f==="__proto__")return;c[f]?Array.isArray(c[f])||(c[f]=[c[f]]):c[f]=[]}Array.isArray(c[f])?c[f].push(C):c[f]=C}}}}),Object.keys(a).filter(function(F,d,p){if(!a[F]||typeof a[F]!="object"||Array.isArray(a[F]))return!1;var n=t(F),f=a,C=n.pop(),g=C.replace(/\\\./g,".");return n.forEach(function(m,v,B){m!=="__proto__"&&((!f[m]||typeof f[m]!="object")&&(f[m]={}),f=f[m])}),f===a&&g===C?!1:(f[g]=a[F],!0)}).forEach(function(F,d,p){delete a[F]}),a}function s(D){return D.charAt(0)==='"'&&D.slice(-1)==='"'||D.charAt(0)==="'"&&D.slice(-1)==="'"}function o(D){return typeof D!="string"||D.match(/[=\r\n]/)||D.match(/^\[/)||D.length>1&&s(D)||D!==D.trim()?JSON.stringify(D):D.replace(/;/g,"\\;").replace(/#/g,"\\#")}function r(D,a){if(D=(D||"").trim(),s(D)){D.charAt(0)==="'"&&(D=D.substr(1,D.length-2));try{D=JSON.parse(D)}catch{}}else{for(var c=!1,l="",E=0,h=D.length;E<h;E++){var F=D.charAt(E);if(c)"\\;#".indexOf(F)!==-1?l+=F:l+="\\"+F,c=!1;else{if(";#".indexOf(F)!==-1)break;F==="\\"?c=!0:l+=F}}return c&&(l+="\\"),l.trim()}return D}return oe}var Ze,Vu;function rn(){if(Vu)return Ze;Vu=1;var e=1,u=2;function t(){return""}function i(s,o,r){return s.slice(o,r).replace(/\S/g," ")}return Ze=function(s,o){o=o||{};for(var r,D,a=!1,c=!1,l=0,E="",h=o.whitespace===!1?t:i,F=0;F<s.length;F++){if(r=s[F],D=s[F+1],!c&&r==='"'){var d=s[F-1]==="\\"&&s[F-2]!=="\\";d||(a=!a)}if(!a){if(!c&&r+D==="//")E+=s.slice(l,F),l=F,c=e,F++;else if(c===e&&r+D===`\r
`){F++,c=!1,E+=h(s,l,F),l=F;continue}else if(c===e&&r===`
`)c=!1,E+=h(s,l,F),l=F;else if(!c&&r+D==="/*"){E+=s.slice(l,F),l=F,c=u,F++;continue}else if(c===u&&r+D==="*/"){F++,c=!1,E+=h(s,l,F+1),l=F+1;continue}}}return E+(c?h(s.substr(l)):s.substr(l))},Ze}var Yu;function nn(){if(Yu)return he;Yu=1;var e=N,u=tn(),t=T,i=rn(),s=he.parse=function(r){return/^\s*{/.test(r)?JSON.parse(i(r)):u.parse(r)},o=he.file=function(){var r=[].slice.call(arguments).filter(function(c){return c!=null});for(var D in r)if(typeof r[D]!="string")return;var a=t.join.apply(null,r);try{return e.readFileSync(a,"utf-8")}catch{return}};return he.json=function(){var r=o.apply(null,arguments);return r?s(r):null},he.env=function(r,D){D=D||process.env;var a={},c=r.length;for(var l in D)if(l.toLowerCase().indexOf(r.toLowerCase())===0){for(var E=l.substring(c).split("__"),h;(h=E.indexOf(""))>-1;)E.splice(h,1);var F=a;E.forEach(function(p,n){!p||typeof F!="object"||(n===E.length-1&&(F[p]=D[l]),F[p]===void 0&&(F[p]={}),F=F[p])})}return a},he.find=function(){var r=t.join.apply(null,[].slice.call(arguments));function D(a,c){var l=t.join(a,c);try{return e.statSync(l),l}catch{if(t.dirname(a)!==a)return D(t.dirname(a),c)}}return D(process.cwd(),r)},he}var eu={exports:{}};/*!
 * @description Recursive object extending
 * <AUTHOR> Lotsmanov <<EMAIL>>
 * @license MIT
 *
 * The MIT License (MIT)
 *
 * Copyright (c) 2013-2018 Viacheslav Lotsmanov
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */var Ju;function Dn(){if(Ju)return eu.exports;Ju=1;function e(o){return o instanceof Buffer||o instanceof Date||o instanceof RegExp}function u(o){if(o instanceof Buffer){var r=Buffer.alloc?Buffer.alloc(o.length):new Buffer(o.length);return o.copy(r),r}else{if(o instanceof Date)return new Date(o.getTime());if(o instanceof RegExp)return new RegExp(o);throw new Error("Unexpected situation")}}function t(o){var r=[];return o.forEach(function(D,a){typeof D=="object"&&D!==null?Array.isArray(D)?r[a]=t(D):e(D)?r[a]=u(D):r[a]=s({},D):r[a]=D}),r}function i(o,r){return r==="__proto__"?void 0:o[r]}var s=eu.exports=function(){if(arguments.length<1||typeof arguments[0]!="object")return!1;if(arguments.length<2)return arguments[0];var o=arguments[0],r=Array.prototype.slice.call(arguments,1),D,a;return r.forEach(function(c){typeof c!="object"||c===null||Array.isArray(c)||Object.keys(c).forEach(function(l){if(a=i(o,l),D=i(c,l),D!==o)if(typeof D!="object"||D===null){o[l]=D;return}else if(Array.isArray(D)){o[l]=t(D);return}else if(e(D)){o[l]=u(D);return}else if(typeof a!="object"||a===null||Array.isArray(a)){o[l]=s({},D);return}else{o[l]=s(a,D);return}})}),o};return eu.exports}var uu,Hu;function on(){if(Hu)return uu;Hu=1;function e(i,s){var o=i;s.slice(0,-1).forEach(function(D){o=o[D]||{}});var r=s[s.length-1];return r in o}function u(i){return typeof i=="number"||/^0x[0-9a-f]+$/i.test(i)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(i)}function t(i,s){return s==="constructor"&&typeof i[s]=="function"||s==="__proto__"}return uu=function(i,s){s||(s={});var o={bools:{},strings:{},unknownFn:null};typeof s.unknown=="function"&&(o.unknownFn=s.unknown),typeof s.boolean=="boolean"&&s.boolean?o.allBools=!0:[].concat(s.boolean).filter(Boolean).forEach(function(A){o.bools[A]=!0});var r={};function D(A){return r[A].some(function(_){return o.bools[_]})}Object.keys(s.alias||{}).forEach(function(A){r[A]=[].concat(s.alias[A]),r[A].forEach(function(_){r[_]=[A].concat(r[A].filter(function(k){return _!==k}))})}),[].concat(s.string).filter(Boolean).forEach(function(A){o.strings[A]=!0,r[A]&&[].concat(r[A]).forEach(function(_){o.strings[_]=!0})});var a=s.default||{},c={_:[]};function l(A,_){return o.allBools&&/^--[^=]+$/.test(_)||o.strings[A]||o.bools[A]||r[A]}function E(A,_,k){for(var $=A,ee=0;ee<_.length-1;ee++){var Y=_[ee];if(t($,Y))return;$[Y]===void 0&&($[Y]={}),($[Y]===Object.prototype||$[Y]===Number.prototype||$[Y]===String.prototype)&&($[Y]={}),$[Y]===Array.prototype&&($[Y]=[]),$=$[Y]}var H=_[_.length-1];t($,H)||(($===Object.prototype||$===Number.prototype||$===String.prototype)&&($={}),$===Array.prototype&&($=[]),$[H]===void 0||o.bools[H]||typeof $[H]=="boolean"?$[H]=k:Array.isArray($[H])?$[H].push(k):$[H]=[$[H],k])}function h(A,_,k){if(!(k&&o.unknownFn&&!l(A,k)&&o.unknownFn(k)===!1)){var $=!o.strings[A]&&u(_)?Number(_):_;E(c,A.split("."),$),(r[A]||[]).forEach(function(ee){E(c,ee.split("."),$)})}}Object.keys(o.bools).forEach(function(A){h(A,a[A]===void 0?!1:a[A])});var F=[];i.indexOf("--")!==-1&&(F=i.slice(i.indexOf("--")+1),i=i.slice(0,i.indexOf("--")));for(var d=0;d<i.length;d++){var p=i[d],n,f;if(/^--.+=/.test(p)){var C=p.match(/^--([^=]+)=([\s\S]*)$/);n=C[1];var g=C[2];o.bools[n]&&(g=g!=="false"),h(n,g,p)}else if(/^--no-.+/.test(p))n=p.match(/^--no-(.+)/)[1],h(n,!1,p);else if(/^--.+/.test(p))n=p.match(/^--(.+)/)[1],f=i[d+1],f!==void 0&&!/^(-|--)[^-]/.test(f)&&!o.bools[n]&&!o.allBools&&(!r[n]||!D(n))?(h(n,f,p),d+=1):/^(true|false)$/.test(f)?(h(n,f==="true",p),d+=1):h(n,o.strings[n]?"":!0,p);else if(/^-[^-]+/.test(p)){for(var m=p.slice(1,-1).split(""),v=!1,B=0;B<m.length;B++){if(f=p.slice(B+2),f==="-"){h(m[B],f,p);continue}if(/[A-Za-z]/.test(m[B])&&f[0]==="="){h(m[B],f.slice(1),p),v=!0;break}if(/[A-Za-z]/.test(m[B])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(f)){h(m[B],f,p),v=!0;break}if(m[B+1]&&m[B+1].match(/\W/)){h(m[B],p.slice(B+2),p),v=!0;break}else h(m[B],o.strings[m[B]]?"":!0,p)}n=p.slice(-1)[0],!v&&n!=="-"&&(i[d+1]&&!/^(-|--)[^-]/.test(i[d+1])&&!o.bools[n]&&(!r[n]||!D(n))?(h(n,i[d+1],p),d+=1):i[d+1]&&/^(true|false)$/.test(i[d+1])?(h(n,i[d+1]==="true",p),d+=1):h(n,o.strings[n]?"":!0,p))}else if((!o.unknownFn||o.unknownFn(p)!==!1)&&c._.push(o.strings._||!u(p)?p:Number(p)),s.stopEarly){c._.push.apply(c._,i.slice(d+1));break}}return Object.keys(a).forEach(function(A){e(c,A.split("."))||(E(c,A.split("."),a[A]),(r[A]||[]).forEach(function(_){E(c,_.split("."),a[A])}))}),s["--"]?c["--"]=F.slice():F.forEach(function(A){c._.push(A)}),c},uu}var tu,Ku;function sn(){if(Ku)return tu;Ku=1;var e=nn(),u=T.join,t=Dn(),i="/etc",s=process.platform==="win32",o=s?process.env.USERPROFILE:process.env.HOME;return tu=function(r,D,a,c){if(typeof r!="string")throw new Error("rc(name): name *must* be string");a||(a=on()(process.argv.slice(2))),D=(typeof D=="string"?e.json(D):D)||{},c=c||e.parse;var l=e.env(r+"_"),E=[D],h=[];function F(d){if(!(h.indexOf(d)>=0)){var p=e.file(d);p&&(E.push(c(p)),h.push(d))}}return s||[u(i,r,"config"),u(i,r+"rc")].forEach(F),o&&[u(o,".config",r,"config"),u(o,".config",r),u(o,"."+r,"config"),u(o,"."+r+"rc")].forEach(F),F(e.find("."+r+"rc")),l.config&&F(l.config),a.config&&F(a.config),t.apply(null,E.concat([l,a,h.length?{configs:h,config:h[h.length-1]}:void 0]))},tu}var an=sn();const cn=J.getDefaultExportFromCjs(an);function ln(e){const u=cn("npm",{registry:"https://registry.npmjs.org/"}),t=u[`${e}:registry`]||u.config_registry||u.registry;return t.slice(-1)==="/"?t:`${t}/`}var ru={exports:{}},de={},pe={},nu,Xu;function fn(){if(Xu)return nu;Xu=1;var e=qt,u=process.cwd,t=null,i=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return t||(t=u.call(process)),t};try{process.cwd()}catch{}if(typeof process.chdir=="function"){var s=process.chdir;process.chdir=function(r){t=null,s.call(process,r)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,s)}nu=o;function o(r){e.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&D(r),r.lutimes||a(r),r.chown=E(r.chown),r.fchown=E(r.fchown),r.lchown=E(r.lchown),r.chmod=c(r.chmod),r.fchmod=c(r.fchmod),r.lchmod=c(r.lchmod),r.chownSync=h(r.chownSync),r.fchownSync=h(r.fchownSync),r.lchownSync=h(r.lchownSync),r.chmodSync=l(r.chmodSync),r.fchmodSync=l(r.fchmodSync),r.lchmodSync=l(r.lchmodSync),r.stat=F(r.stat),r.fstat=F(r.fstat),r.lstat=F(r.lstat),r.statSync=d(r.statSync),r.fstatSync=d(r.fstatSync),r.lstatSync=d(r.lstatSync),r.chmod&&!r.lchmod&&(r.lchmod=function(n,f,C){C&&process.nextTick(C)},r.lchmodSync=function(){}),r.chown&&!r.lchown&&(r.lchown=function(n,f,C,g){g&&process.nextTick(g)},r.lchownSync=function(){}),i==="win32"&&(r.rename=typeof r.rename!="function"?r.rename:function(n){function f(C,g,m){var v=Date.now(),B=0;n(C,g,function A(_){if(_&&(_.code==="EACCES"||_.code==="EPERM")&&Date.now()-v<6e4){setTimeout(function(){r.stat(g,function(k,$){k&&k.code==="ENOENT"?n(C,g,A):m(_)})},B),B<100&&(B+=10);return}m&&m(_)})}return Object.setPrototypeOf&&Object.setPrototypeOf(f,n),f}(r.rename)),r.read=typeof r.read!="function"?r.read:function(n){function f(C,g,m,v,B,A){var _;if(A&&typeof A=="function"){var k=0;_=function($,ee,Y){if($&&$.code==="EAGAIN"&&k<10)return k++,n.call(r,C,g,m,v,B,_);A.apply(this,arguments)}}return n.call(r,C,g,m,v,B,_)}return Object.setPrototypeOf&&Object.setPrototypeOf(f,n),f}(r.read),r.readSync=typeof r.readSync!="function"?r.readSync:function(n){return function(f,C,g,m,v){for(var B=0;;)try{return n.call(r,f,C,g,m,v)}catch(A){if(A.code==="EAGAIN"&&B<10){B++;continue}throw A}}}(r.readSync);function D(n){n.lchmod=function(f,C,g){n.open(f,e.O_WRONLY|e.O_SYMLINK,C,function(m,v){if(m){g&&g(m);return}n.fchmod(v,C,function(B){n.close(v,function(A){g&&g(B||A)})})})},n.lchmodSync=function(f,C){var g=n.openSync(f,e.O_WRONLY|e.O_SYMLINK,C),m=!0,v;try{v=n.fchmodSync(g,C),m=!1}finally{if(m)try{n.closeSync(g)}catch{}else n.closeSync(g)}return v}}function a(n){e.hasOwnProperty("O_SYMLINK")&&n.futimes?(n.lutimes=function(f,C,g,m){n.open(f,e.O_SYMLINK,function(v,B){if(v){m&&m(v);return}n.futimes(B,C,g,function(A){n.close(B,function(_){m&&m(A||_)})})})},n.lutimesSync=function(f,C,g){var m=n.openSync(f,e.O_SYMLINK),v,B=!0;try{v=n.futimesSync(m,C,g),B=!1}finally{if(B)try{n.closeSync(m)}catch{}else n.closeSync(m)}return v}):n.futimes&&(n.lutimes=function(f,C,g,m){m&&process.nextTick(m)},n.lutimesSync=function(){})}function c(n){return n&&function(f,C,g){return n.call(r,f,C,function(m){p(m)&&(m=null),g&&g.apply(this,arguments)})}}function l(n){return n&&function(f,C){try{return n.call(r,f,C)}catch(g){if(!p(g))throw g}}}function E(n){return n&&function(f,C,g,m){return n.call(r,f,C,g,function(v){p(v)&&(v=null),m&&m.apply(this,arguments)})}}function h(n){return n&&function(f,C,g){try{return n.call(r,f,C,g)}catch(m){if(!p(m))throw m}}}function F(n){return n&&function(f,C,g){typeof C=="function"&&(g=C,C=null);function m(v,B){B&&(B.uid<0&&(B.uid+=4294967296),B.gid<0&&(B.gid+=4294967296)),g&&g.apply(this,arguments)}return C?n.call(r,f,C,m):n.call(r,f,m)}}function d(n){return n&&function(f,C){var g=C?n.call(r,f,C):n.call(r,f);return g&&(g.uid<0&&(g.uid+=4294967296),g.gid<0&&(g.gid+=4294967296)),g}}function p(n){if(!n||n.code==="ENOSYS")return!0;var f=!process.getuid||process.getuid()!==0;return!!(f&&(n.code==="EINVAL"||n.code==="EPERM"))}}return nu}var Du,zu;function Fn(){if(zu)return Du;zu=1;var e=vu.Stream;Du=u;function u(t){return{ReadStream:i,WriteStream:s};function i(o,r){if(!(this instanceof i))return new i(o,r);e.call(this);var D=this;this.path=o,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,r=r||{};for(var a=Object.keys(r),c=0,l=a.length;c<l;c++){var E=a[c];this[E]=r[E]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){D._read()});return}t.open(this.path,this.flags,this.mode,function(h,F){if(h){D.emit("error",h),D.readable=!1;return}D.fd=F,D.emit("open",F),D._read()})}function s(o,r){if(!(this instanceof s))return new s(o,r);e.call(this),this.path=o,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,r=r||{};for(var D=Object.keys(r),a=0,c=D.length;a<c;a++){var l=D[a];this[l]=r[l]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}return Du}var iu,Qu;function hn(){if(Qu)return iu;Qu=1,iu=u;var e=Object.getPrototypeOf||function(t){return t.__proto__};function u(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var i={__proto__:e(t)};else var i=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(s){Object.defineProperty(i,s,Object.getOwnPropertyDescriptor(t,s))}),i}return iu}var Re,Zu;function dn(){if(Zu)return Re;Zu=1;var e=N,u=fn(),t=Fn(),i=hn(),s=W,o,r;typeof Symbol=="function"&&typeof Symbol.for=="function"?(o=Symbol.for("graceful-fs.queue"),r=Symbol.for("graceful-fs.previous")):(o="___graceful-fs.queue",r="___graceful-fs.previous");function D(){}function a(n,f){Object.defineProperty(n,o,{get:function(){return f}})}var c=D;if(s.debuglog?c=s.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(c=function(){var n=s.format.apply(s,arguments);n="GFS4: "+n.split(/\n/).join(`
GFS4: `),console.error(n)}),!e[o]){var l=J.commonjsGlobal[o]||[];a(e,l),e.close=function(n){function f(C,g){return n.call(e,C,function(m){m||d(),typeof g=="function"&&g.apply(this,arguments)})}return Object.defineProperty(f,r,{value:n}),f}(e.close),e.closeSync=function(n){function f(C){n.apply(e,arguments),d()}return Object.defineProperty(f,r,{value:n}),f}(e.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){c(e[o]),kt.equal(e[o].length,0)})}J.commonjsGlobal[o]||a(J.commonjsGlobal,e[o]),Re=E(i(e)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!e.__patched&&(Re=E(e),e.__patched=!0);function E(n){u(n),n.gracefulify=E,n.createReadStream=Ge,n.createWriteStream=Ve;var f=n.readFile;n.readFile=C;function C(y,w,b){return typeof w=="function"&&(b=w,w=null),L(y,w,b);function L(j,P,R,I){return f(j,P,function(S){S&&(S.code==="EMFILE"||S.code==="ENFILE")?h([L,[j,P,R],S,I||Date.now(),Date.now()]):typeof R=="function"&&R.apply(this,arguments)})}}var g=n.writeFile;n.writeFile=m;function m(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return g(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var v=n.appendFile;v&&(n.appendFile=B);function B(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return v(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var A=n.copyFile;A&&(n.copyFile=_);function _(y,w,b,L){return typeof b=="function"&&(L=b,b=0),j(y,w,b,L);function j(P,R,I,S,q){return A(P,R,I,function(O){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}var k=n.readdir;n.readdir=ee;var $=/^v[0-5]\./;function ee(y,w,b){typeof w=="function"&&(b=w,w=null);var L=$.test(process.version)?function(R,I,S,q){return k(R,j(R,I,S,q))}:function(R,I,S,q){return k(R,I,j(R,I,S,q))};return L(y,w,b);function j(P,R,I,S){return function(q,O){q&&(q.code==="EMFILE"||q.code==="ENFILE")?h([L,[P,R,I],q,S||Date.now(),Date.now()]):(O&&O.sort&&O.sort(),typeof I=="function"&&I.call(this,q,O))}}}if(process.version.substr(0,4)==="v0.8"){var Y=t(n);G=Y.ReadStream,V=Y.WriteStream}var H=n.ReadStream;H&&(G.prototype=Object.create(H.prototype),G.prototype.open=Ue);var fe=n.WriteStream;fe&&(V.prototype=Object.create(fe.prototype),V.prototype.open=We),Object.defineProperty(n,"ReadStream",{get:function(){return G},set:function(y){G=y},enumerable:!0,configurable:!0}),Object.defineProperty(n,"WriteStream",{get:function(){return V},set:function(y){V=y},enumerable:!0,configurable:!0});var me=G;Object.defineProperty(n,"FileReadStream",{get:function(){return me},set:function(y){me=y},enumerable:!0,configurable:!0});var ye=V;Object.defineProperty(n,"FileWriteStream",{get:function(){return ye},set:function(y){ye=y},enumerable:!0,configurable:!0});function G(y,w){return this instanceof G?(H.apply(this,arguments),this):G.apply(Object.create(G.prototype),arguments)}function Ue(){var y=this;Fe(y.path,y.flags,y.mode,function(w,b){w?(y.autoClose&&y.destroy(),y.emit("error",w)):(y.fd=b,y.emit("open",b),y.read())})}function V(y,w){return this instanceof V?(fe.apply(this,arguments),this):V.apply(Object.create(V.prototype),arguments)}function We(){var y=this;Fe(y.path,y.flags,y.mode,function(w,b){w?(y.destroy(),y.emit("error",w)):(y.fd=b,y.emit("open",b))})}function Ge(y,w){return new n.ReadStream(y,w)}function Ve(y,w){return new n.WriteStream(y,w)}var Ye=n.open;n.open=Fe;function Fe(y,w,b,L){return typeof b=="function"&&(L=b,b=null),j(y,w,b,L);function j(P,R,I,S,q){return Ye(P,R,I,function(O,rr){O&&(O.code==="EMFILE"||O.code==="ENFILE")?h([j,[P,R,I,S],O,q||Date.now(),Date.now()]):typeof S=="function"&&S.apply(this,arguments)})}}return n}function h(n){c("ENQUEUE",n[0].name,n[1]),e[o].push(n),p()}var F;function d(){for(var n=Date.now(),f=0;f<e[o].length;++f)e[o][f].length>2&&(e[o][f][3]=n,e[o][f][4]=n);p()}function p(){if(clearTimeout(F),F=void 0,e[o].length!==0){var n=e[o].shift(),f=n[0],C=n[1],g=n[2],m=n[3],v=n[4];if(m===void 0)c("RETRY",f.name,C),f.apply(null,C);else if(Date.now()-m>=6e4){c("TIMEOUT",f.name,C);var B=C.pop();typeof B=="function"&&B.call(null,g)}else{var A=Date.now()-v,_=Math.max(v-m,1),k=Math.min(_*1.2,100);A>=k?(c("RETRY",f.name,C),f.apply(null,C.concat([m]))):e[o].push(n)}F===void 0&&(F=setTimeout(p,0))}}return Re}var et;function pn(){if(et)return pe;et=1;var e=pe&&pe.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(pe,"__esModule",{value:!0}),pe.readCAFileSync=void 0;const u=e(dn());function t(i){try{const s=u.default.readFileSync(i,"utf8"),o="-----END CERTIFICATE-----";return s.split(o).filter(D=>!!D.trim()).map(D=>`${D.trimLeft()}${o}`)}catch(s){if(s.code==="ENOENT")return;throw s}}return pe.readCAFileSync=t,pe}var ut;function En(){return ut||(ut=1,function(e){var u=de&&de.__createBinding||(Object.create?function(i,s,o,r){r===void 0&&(r=o);var D=Object.getOwnPropertyDescriptor(s,o);(!D||("get"in D?!s.__esModule:D.writable||D.configurable))&&(D={enumerable:!0,get:function(){return s[o]}}),Object.defineProperty(i,r,D)}:function(i,s,o,r){r===void 0&&(r=o),i[r]=s[o]}),t=de&&de.__exportStar||function(i,s){for(var o in i)o!=="default"&&!Object.prototype.hasOwnProperty.call(s,o)&&u(s,i,o)};Object.defineProperty(e,"__esModule",{value:!0}),t(pn(),e)}(de)),de}var ou={exports:{}},su,tt;function Cn(){if(tt)return su;tt=1,su=u;function e(t,i){if(typeof Object.setPrototypeOf=="function")return Object.setPrototypeOf(t,i);t.__proto__=i}function u(){this.list=[];var t=null;Object.defineProperty(this,"root",{get:function(){return t},set:function(i){t=i,this.list.length&&e(this.list[this.list.length-1],i)},enumerable:!0,configurable:!0})}return u.prototype={get length(){return this.list.length},get keys(){var t=[];for(var i in this.list[0])t.push(i);return t},get snapshot(){var t={};return this.keys.forEach(function(i){t[i]=this.get(i)},this),t},get store(){return this.list[0]},push:function(t){return typeof t!="object"&&(t={valueOf:t}),this.list.length>=1&&e(this.list[this.list.length-1],t),e(t,this.root),this.list.push(t)},pop:function(){return this.list.length>=2&&e(this.list[this.list.length-2],this.root),this.list.pop()},unshift:function(t){return e(t,this.list[0]||this.root),this.list.unshift(t)},shift:function(){return this.list.length===1&&e(this.list[0],this.root),this.list.shift()},get:function(t){return this.list[0][t]},set:function(t,i,s){return this.length||this.push({}),s&&this.list[0].hasOwnProperty(t)&&this.push({}),this.list[0][t]=i},forEach:function(t,i){for(var s in this.list[0])t.call(i,s,this.list[0][s])},slice:function(){return this.list.slice.apply(this.list,arguments)},splice:function(){for(var t=this.list.splice.apply(this.list,arguments),i=0,s=this.list.length;i<s;i++)e(this.list[i],this.list[i+1]||this.root);return t}},su}var se={},rt;function gn(){if(rt)return se;rt=1,se.parse=se.decode=i,se.stringify=se.encode=u,se.safe=o,se.unsafe=r;var e=typeof process<"u"&&process.platform==="win32"?`\r
`:`
`;function u(D,a){var c=[],l="";typeof a=="string"?a={section:a,whitespace:!1}:(a=a||{},a.whitespace=a.whitespace===!0);var E=a.whitespace?" = ":"=";return Object.keys(D).forEach(function(h,F,d){var p=D[h];p&&Array.isArray(p)?p.forEach(function(n){l+=o(h+"[]")+E+o(n)+`
`}):p&&typeof p=="object"?c.push(h):l+=o(h)+E+o(p)+e}),a.section&&l.length&&(l="["+o(a.section)+"]"+e+l),c.forEach(function(h,F,d){var p=t(h).join("\\."),n=(a.section?a.section+".":"")+p,f=u(D[h],{section:n,whitespace:a.whitespace});l.length&&f.length&&(l+=e),l+=f}),l}function t(D){return D.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(function(a){return a.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")})}function i(D){var a={},c=a,l=null,E=/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i,h=D.split(/[\r\n]+/g);return h.forEach(function(F,d,p){if(!(!F||F.match(/^\s*[;#]/))){var n=F.match(E);if(n){if(n[1]!==void 0){if(l=r(n[1]),l==="__proto__"){c={};return}c=a[l]=a[l]||{};return}var f=r(n[2]);if(f!=="__proto__"){var C=n[3]?r(n[4]):!0;switch(C){case"true":case"false":case"null":C=JSON.parse(C)}if(f.length>2&&f.slice(-2)==="[]"){if(f=f.substring(0,f.length-2),f==="__proto__")return;c[f]?Array.isArray(c[f])||(c[f]=[c[f]]):c[f]=[]}Array.isArray(c[f])?c[f].push(C):c[f]=C}}}}),Object.keys(a).filter(function(F,d,p){if(!a[F]||typeof a[F]!="object"||Array.isArray(a[F]))return!1;var n=t(F),f=a,C=n.pop(),g=C.replace(/\\\./g,".");return n.forEach(function(m,v,B){m!=="__proto__"&&((!f[m]||typeof f[m]!="object")&&(f[m]={}),f=f[m])}),f===a&&g===C?!1:(f[g]=a[F],!0)}).forEach(function(F,d,p){delete a[F]}),a}function s(D){return D.charAt(0)==='"'&&D.slice(-1)==='"'||D.charAt(0)==="'"&&D.slice(-1)==="'"}function o(D){return typeof D!="string"||D.match(/[=\r\n]/)||D.match(/^\[/)||D.length>1&&s(D)||D!==D.trim()?JSON.stringify(D):D.replace(/;/g,"\\;").replace(/#/g,"\\#")}function r(D,a){if(D=(D||"").trim(),s(D)){D.charAt(0)==="'"&&(D=D.substr(1,D.length-2));try{D=JSON.parse(D)}catch{}}else{for(var c=!1,l="",E=0,h=D.length;E<h;E++){var F=D.charAt(E);if(c)"\\;#".indexOf(F)!==-1?l+=F:l+="\\"+F,c=!1;else{if(";#".indexOf(F)!==-1)break;F==="\\"?c=!0:l+=F}}return c&&(l+="\\"),l.trim()}return D}return se}var nt;function mn(){if(nt)return ou.exports;nt=1;var e=Cn(),u=T,t=N,i=gn(),s=Dr.EventEmitter,o=Se,r=ir,D=ou.exports=function(){for(var h=[].slice.call(arguments),F=new l;h.length;){var d=h.shift();d&&F.push(typeof d=="string"?c(d):d)}return F};D.find=function(){var h=u.join.apply(null,[].slice.call(arguments));function F(d,p){var n=u.join(d,p);try{return t.statSync(n),n}catch{if(u.dirname(d)!==d)return F(u.dirname(d),p)}}return F(__dirname,h)};var a=D.parse=function(h,F,d){if(h=""+h,d)if(d==="json")if(this.emit)try{return JSON.parse(h)}catch(p){this.emit("error",p)}else return JSON.parse(h);else return i.parse(h);else try{return JSON.parse(h)}catch{return i.parse(h)}},c=D.json=function(){var h=[].slice.call(arguments).filter(function(p){return p!=null}),F=u.join.apply(null,h),d;try{d=t.readFileSync(F,"utf-8")}catch{return}return a(d,F,"json")};D.env=function(h,F){F=F||process.env;var d={},p=h.length;for(var n in F)n.indexOf(h)===0&&(d[n.substring(p)]=F[n]);return d},D.ConfigChain=l;function l(){s.apply(this),e.apply(this,arguments),this._awaiting=0,this._saving=0,this.sources={}}var E={constructor:{value:l}};return Object.keys(s.prototype).forEach(function(h){E[h]=Object.getOwnPropertyDescriptor(s.prototype,h)}),l.prototype=Object.create(e.prototype,E),l.prototype.del=function(h,F){if(F){var d=this.sources[F];if(d=d&&d.data,!d)return this.emit("error",new Error("not found "+F));delete d[h]}else for(var p=0,n=this.list.length;p<n;p++)delete this.list[p][h];return this},l.prototype.set=function(h,F,d){var p;if(d){if(p=this.sources[d],p=p&&p.data,!p)return this.emit("error",new Error("not found "+d))}else if(p=this.list[0],!p)return this.emit("error",new Error("cannot set, no confs!"));return p[h]=F,this},l.prototype.get=function(h,F){return F?(F=this.sources[F],F&&(F=F.data),F&&Object.hasOwnProperty.call(F,h)?F[h]:void 0):this.list[0][h]},l.prototype.save=function(h,f,d){typeof f=="function"&&(d=f,f=null);var p=this.sources[h];if(!p||!(p.path||p.source)||!p.data)return this.emit("error",new Error("bad save target: "+h));if(p.source){var n=p.prefix||"";return Object.keys(p.data).forEach(function(g){p.source[n+g]=p.data[g]}),this}var f=f||p.type,C=p.data;return p.type==="json"?C=JSON.stringify(C):C=i.stringify(C),this._saving++,t.writeFile(p.path,C,"utf8",function(g){if(this._saving--,g)return d?d(g):this.emit("error",g);this._saving===0&&(d&&d(),this.emit("save"))}.bind(this)),this},l.prototype.addFile=function(h,F,d){d=d||h;var p={__source__:d};return this.sources[d]={path:h,type:F},this.push(p),this._await(),t.readFile(h,"utf8",function(n,f){n&&this.emit("error",n),this.addString(f,h,F,p)}.bind(this)),this},l.prototype.addEnv=function(h,F,d){d=d||"env";var p=D.env(h,F);return this.sources[d]={data:p,source:F,prefix:h},this.add(p,d)},l.prototype.addUrl=function(h,F,d){this._await();var p=o.format(h);d=d||p;var n={__source__:d};return this.sources[d]={href:p,type:F},this.push(n),r.request(h,function(f){var C=[],g=f.headers["content-type"];F||(F=g.indexOf("json")!==-1?"json":g.indexOf("ini")!==-1?"ini":p.match(/\.json$/)?"json":p.match(/\.ini$/)?"ini":null,n.type=F),f.on("data",C.push.bind(C)).on("end",function(){this.addString(Buffer.concat(C),p,F,n)}.bind(this)).on("error",this.emit.bind(this,"error"))}.bind(this)).on("error",this.emit.bind(this,"error")).end(),this},l.prototype.addString=function(h,F,d,p){return h=this.parse(h,F,d),this.add(h,p),this},l.prototype.add=function(h,F){if(F&&typeof F=="object"){var d=this.list.indexOf(F);if(d===-1)return this.emit("error",new Error("bad marker"));this.splice(d,1,h),F=F.__source__,this.sources[F]=this.sources[F]||{},this.sources[F].data=h,this._resolve()}else typeof F=="string"&&(this.sources[F]=this.sources[F]||{},this.sources[F].data=h),this._await(),this.push(h),process.nextTick(this._resolve.bind(this));return this},l.prototype.parse=D.parse,l.prototype._await=function(){this._awaiting++},l.prototype._resolve=function(){this._awaiting--,this._awaiting===0&&this.emit("load",this)},ou.exports}var au,Dt;function yn(){if(Dt)return au;Dt=1,au=function(u){const t=u.indexOf(":");if(t===-1)return e(u);const i=u.substr(0,t),s=u.substr(t+1);return`${e(i)}:${e(s)}`};function e(u){if(u=u.toLowerCase(),u==="_authtoken")return"_authToken";let t=u[0];for(let i=1;i<u.length;i++)t+=u[i]==="_"?"-":u[i];return t}return au}var Be={},cu={},Ae={},it;function Bn(){if(it)return Ae;it=1,Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.envReplace=void 0;const e=/(?<!\\)(\\*)\$\{([^${}]+)\}/g;function u(o,r){return o.replace(e,t.bind(null,r))}Ae.envReplace=u;function t(o,r,D,a){if(D.length%2)return r.slice((D.length+1)/2);const c=s(o,a);if(c===void 0)throw new Error(`Failed to replace env in config: ${r}`);return`${D.slice(D.length/2)}${c}`}const i=/([^:-]+)(:?)-(.+)/;function s(o,r){const D=r.match(i);if(!D)return o[r];const[,a,c,l]=D;return Object.prototype.hasOwnProperty.call(o,a)?!o[a]&&c?l:o[a]:l}return Ae}var ot;function An(){return ot||(ot=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.envReplace=void 0;var u=Bn();Object.defineProperty(e,"envReplace",{enumerable:!0,get:function(){return u.envReplace}})}(cu)),cu}var st;function vn(){if(st)return Be;st=1;const e=N,u=T,{envReplace:t}=An(),i=(o,r,D)=>{if(typeof r!="string")return r;const a=[].concat(o[D]),c=a.indexOf(u)!==-1,l=a.indexOf(Boolean)!==-1,E=a.indexOf(String)!==-1,h=a.indexOf(Number)!==-1;if(r=`${r}`.trim(),/^".*"$/.test(r))try{r=JSON.parse(r)}catch{throw new Error(`Failed parsing JSON config key ${D}: ${r}`)}if(l&&!E&&r==="")return!0;switch(r){case"true":return!0;case"false":return!1;case"null":return null;case"undefined":return}return r=t(r,process.env),c&&((process.platform==="win32"?/^~(\/|\\)/:/^~\//).test(r)&&process.env.HOME&&(r=u.resolve(process.env.HOME,r.substr(2))),r=u.resolve(r)),h&&!isNaN(r)&&(r=Number(r)),r},s=o=>{o=u.resolve(o);let r=!1;for(;u.basename(o)==="node_modules";)o=u.dirname(o),r=!0;if(r)return o;const D=(a,c)=>{const l=/^[a-zA-Z]:(\\|\/)?$/;if(a==="/"||process.platform==="win32"&&l.test(a))return c;try{const E=e.readdirSync(a);if(E.includes("node_modules")||E.includes("package.json")||E.includes("package.json5")||E.includes("package.yaml")||E.includes("pnpm-workspace.yaml"))return a;const h=u.dirname(a);return h===a?c:D(h,c)}catch(E){if(a===c){if(E.code==="ENOENT")return c;throw E}return c}};return D(o,o)};return Be.envReplace=t,Be.findPrefix=s,Be.parseField=i,Be}var lu={},at;function bn(){if(at)return lu;at=1;const e=T,u=vu.Stream,t=Se,i=()=>{},s=()=>[],o=()=>{};return lu.types={access:[null,"restricted","public"],"allow-same-version":Boolean,"always-auth":Boolean,also:[null,"dev","development"],audit:Boolean,"auth-type":["legacy","sso","saml","oauth"],"bin-links":Boolean,browser:[null,String],ca:[null,String,Array],cafile:e,cache:e,"cache-lock-stale":Number,"cache-lock-retries":Number,"cache-lock-wait":Number,"cache-max":Number,"cache-min":Number,cert:[null,String],cidr:[null,String,Array],color:["always",Boolean],depth:Number,description:Boolean,dev:Boolean,"dry-run":Boolean,editor:String,"engine-strict":Boolean,force:Boolean,"fetch-retries":Number,"fetch-retry-factor":Number,"fetch-retry-mintimeout":Number,"fetch-retry-maxtimeout":Number,git:String,"git-tag-version":Boolean,"commit-hooks":Boolean,global:Boolean,globalconfig:e,"global-style":Boolean,group:[Number,String],"https-proxy":[null,t],"user-agent":String,"ham-it-up":Boolean,heading:String,"if-present":Boolean,"ignore-prepublish":Boolean,"ignore-scripts":Boolean,"init-module":e,"init-author-name":String,"init-author-email":String,"init-author-url":["",t],"init-license":String,"init-version":o,json:Boolean,key:[null,String],"legacy-bundling":Boolean,link:Boolean,"local-address":s(),loglevel:["silent","error","warn","notice","http","timing","info","verbose","silly"],logstream:u,"logs-max":Number,long:Boolean,maxsockets:Number,message:String,"metrics-registry":[null,String],"node-options":[null,String],"node-version":[null,o],"no-proxy":[null,String,Array],offline:Boolean,"onload-script":[null,String],only:[null,"dev","development","prod","production"],optional:Boolean,"package-lock":Boolean,otp:[null,String],"package-lock-only":Boolean,parseable:Boolean,"prefer-offline":Boolean,"prefer-online":Boolean,prefix:e,production:Boolean,progress:Boolean,proxy:[null,!1,t],provenance:Boolean,"read-only":Boolean,"rebuild-bundle":Boolean,registry:[null,t],rollback:Boolean,save:Boolean,"save-bundle":Boolean,"save-dev":Boolean,"save-exact":Boolean,"save-optional":Boolean,"save-prefix":String,"save-prod":Boolean,scope:String,"script-shell":[null,String],"scripts-prepend-node-path":[!1,!0,"auto","warn-only"],searchopts:String,searchexclude:[null,String],searchlimit:Number,searchstaleness:Number,"send-metrics":Boolean,shell:String,shrinkwrap:Boolean,"sign-git-tag":Boolean,"sso-poll-frequency":Number,"sso-type":[null,"oauth","saml"],"strict-ssl":Boolean,tag:String,timing:Boolean,tmp:e,unicode:Boolean,"unsafe-perm":Boolean,usage:Boolean,user:[Number,String],userconfig:e,umask:i,version:Boolean,"tag-version-prefix":String,versions:Boolean,viewer:String,_exit:Boolean},lu}var fu,ct;function wn(){if(ct)return fu;ct=1;const{readCAFileSync:e}=En(),u=N,t=T,{ConfigChain:i}=mn(),s=yn(),o=vn();class r extends i{constructor(a,c){super(a),this.root=a,this._parseField=o.parseField.bind(null,c||bn())}add(a,c){try{for(const l of Object.keys(a))a[l]=this._parseField(a[l],l)}catch(l){throw l}return super.add(a,c)}addFile(a,c){c=c||a;const l={__source__:c};this.sources[c]={path:a,type:"ini"},this.push(l),this._await();try{const E=u.readFileSync(a,"utf8");this.addString(E,a,"ini",l)}catch(E){if(E.code==="ENOENT")this.add({},l);else return`Issue while reading "${a}". ${E.message}`}}addEnv(a){a=a||process.env;const c={};return Object.keys(a).filter(l=>/^npm_config_/i.test(l)).forEach(l=>{a[l]&&(c[s(l.substr(11))]=a[l])}),super.addEnv("",c,"env")}loadPrefix(){const a=this.list[0];Object.defineProperty(this,"prefix",{enumerable:!0,set:l=>{const E=this.get("global");this[E?"globalPrefix":"localPrefix"]=l},get:()=>this.get("global")?this.globalPrefix:this.localPrefix}),Object.defineProperty(this,"globalPrefix",{enumerable:!0,set:l=>{this.set("prefix",l)},get:()=>t.resolve(this.get("prefix"))});let c;if(Object.defineProperty(this,"localPrefix",{enumerable:!0,set:l=>{c=l},get:()=>c}),Object.prototype.hasOwnProperty.call(a,"prefix"))c=t.resolve(a.prefix);else try{c=o.findPrefix(process.cwd())}catch(l){throw l}return c}loadCAFile(a){if(!a)return;const c=e(a);c&&this.set("ca",c)}loadUser(){const a=this.root;if(this.get("global"))return;if(process.env.SUDO_UID){a.user=Number(process.env.SUDO_UID);return}const c=t.resolve(this.get("prefix"));try{const l=u.statSync(c);a.user=l.uid}catch(l){if(l.code==="ENOENT")return;throw l}}}return fu=r,fu}var Fu={},lt;function Sn(){return lt||(lt=1,function(e){const u=ce,t=T,i=u.tmpdir(),s=process.getuid?process.getuid():process.pid,o=()=>!0,r=process.platform==="win32",D={editor:()=>process.env.EDITOR||process.env.VISUAL||(r?"notepad.exe":"vi"),shell:()=>r?process.env.COMSPEC||"cmd.exe":process.env.SHELL||"/bin/bash"},a={fromString:()=>process.umask()};let c=u.homedir();c?process.env.HOME=c:c=t.resolve(i,"npm-"+s);const l=process.platform==="win32"?"npm-cache":".npm",E=process.platform==="win32"&&process.env.APPDATA||c,h=t.resolve(E,l);let F,d;Object.defineProperty(e,"defaults",{get:function(){return F||(process.env.PREFIX?d=process.env.PREFIX:process.platform==="win32"?d=t.dirname(process.execPath):(d=t.dirname(t.dirname(process.execPath)),process.env.DESTDIR&&(d=t.join(process.env.DESTDIR,d))),F={access:null,"allow-same-version":!1,"always-auth":!1,also:null,audit:!0,"auth-type":"legacy","bin-links":!0,browser:null,ca:null,cafile:null,cache:h,"cache-lock-stale":6e4,"cache-lock-retries":10,"cache-lock-wait":1e4,"cache-max":1/0,"cache-min":10,cert:null,cidr:null,color:process.env.NO_COLOR==null,depth:1/0,description:!0,dev:!1,"dry-run":!1,editor:D.editor(),"engine-strict":!1,force:!1,"fetch-retries":2,"fetch-retry-factor":10,"fetch-retry-mintimeout":1e4,"fetch-retry-maxtimeout":6e4,git:"git","git-tag-version":!0,"commit-hooks":!0,global:!1,globalconfig:t.resolve(d,"etc","npmrc"),"global-style":!1,group:process.platform==="win32"?0:process.env.SUDO_GID||process.getgid&&process.getgid(),"ham-it-up":!1,heading:"npm","if-present":!1,"ignore-prepublish":!1,"ignore-scripts":!1,"init-module":t.resolve(c,".npm-init.js"),"init-author-name":"","init-author-email":"","init-author-url":"","init-version":"1.0.0","init-license":"ISC",json:!1,key:null,"legacy-bundling":!1,link:!1,"local-address":void 0,loglevel:"notice",logstream:process.stderr,"logs-max":10,long:!1,maxsockets:50,message:"%s","metrics-registry":null,"node-options":null,offline:!1,"onload-script":!1,only:null,optional:!0,otp:null,"package-lock":!0,"package-lock-only":!1,parseable:!1,"prefer-offline":!1,"prefer-online":!1,prefix:d,production:process.env.NODE_ENV==="production",progress:!process.env.TRAVIS&&!process.env.CI,provenance:!1,proxy:null,"https-proxy":null,"no-proxy":null,"user-agent":"npm/{npm-version} node/{node-version} {platform} {arch}","read-only":!1,"rebuild-bundle":!0,registry:"https://registry.npmjs.org/",rollback:!0,save:!0,"save-bundle":!1,"save-dev":!1,"save-exact":!1,"save-optional":!1,"save-prefix":"^","save-prod":!1,scope:"","script-shell":null,"scripts-prepend-node-path":"warn-only",searchopts:"",searchexclude:null,searchlimit:20,searchstaleness:15*60,"send-metrics":!1,shell:D.shell(),shrinkwrap:!0,"sign-git-tag":!1,"sso-poll-frequency":500,"sso-type":"oauth","strict-ssl":!0,tag:"latest","tag-version-prefix":"v",timing:!1,tmp:i,unicode:o(),"unsafe-perm":process.platform==="win32"||process.platform==="cygwin"||!(process.getuid&&process.setuid&&process.getgid&&process.setgid)||process.getuid()!==0,usage:!1,user:process.platform==="win32"?0:"nobody",userconfig:t.resolve(c,".npmrc"),umask:process.umask?process.umask():a.fromString("022"),version:!1,versions:!1,viewer:process.platform==="win32"?"browser":"man",_exit:!0},F)}})}(Fu)),Fu}var ft;function _n(){return ft||(ft=1,function(e){const u=T,t=wn(),i=Sn();e.exports=(s,o,r)=>{const D=new t(Object.assign({},i.defaults,r),o);D.add(Object.assign({},s),"cli");const a=[];let c=!1;if(require.resolve.paths){const F=require.resolve.paths("npm");let d;try{d=require.resolve("npm",{paths:F.slice(-1)})}catch{c=!0}d&&a.push(D.addFile(u.resolve(u.dirname(d),"..","npmrc"),"builtin"))}D.addEnv(),D.loadPrefix();const l=u.resolve(D.localPrefix,".npmrc"),E=D.get("userconfig");if(!D.get("global")&&l!==E?a.push(D.addFile(l,"project")):D.add({},"project"),D.get("workspace-prefix")&&D.get("workspace-prefix")!==l){const F=u.resolve(D.get("workspace-prefix"),".npmrc");a.push(D.addFile(F,"workspace"))}if(a.push(D.addFile(D.get("userconfig"),"user")),D.get("prefix")){const F=u.resolve(D.get("prefix"),"etc");D.root.globalconfig=u.resolve(F,"npmrc"),D.root.globalignorefile=u.resolve(F,"npmignore")}a.push(D.addFile(D.get("globalconfig"),"global")),D.loadUser();const h=D.get("cafile");return h&&D.loadCAFile(h),{config:D,warnings:a.filter(Boolean),failedToLoadBuiltInConfig:c}},Object.defineProperty(e.exports,"defaults",{get(){return i.defaults},enumerable:!0})}(ru)),ru.exports}var hu,Ft;function xn(){if(Ft)return hu;Ft=1;const e=Se,u=_n(),t=":_authToken",i=":_auth",s=":username",o=":_password";hu=function(){let p,n;arguments.length>=2?(p=arguments[0],n=Object.assign({},arguments[1])):typeof arguments[0]=="string"?p=arguments[0]:n=Object.assign({},arguments[0]),n=n||{};const f=n.npmrc;return n.npmrc=(n.npmrc?{config:{get:C=>f[C]}}:u()).config,p=p||n.npmrc.get("registry")||u.defaults.registry,r(p,n)||D(n.npmrc)};function r(d,p){const n=e.parse(d,!1,!0);let f;for(;f!=="/"&&n.pathname!==f;){f=n.pathname||"/";const C="//"+n.host+f.replace(/\/$/,""),g=c(C,p.npmrc);if(g)return g;if(!p.recursive)return/\/$/.test(d)?void 0:r(e.resolve(d,"."),p);n.pathname=e.resolve(a(f),"..")||"/"}}function D(d){return d.get("_auth")?{token:l(d.get("_auth")),type:"Basic"}:void 0}function a(d){return d[d.length-1]==="/"?d:d+"/"}function c(d,p){const n=E(p.get(d+t)||p.get(d+"/"+t));if(n)return n;const f=p.get(d+s)||p.get(d+"/"+s),C=p.get(d+o)||p.get(d+"/"+o),g=h(f,C);if(g)return g;const m=F(p.get(d+i)||p.get(d+"/"+i));if(m)return m}function l(d){return d.replace(/^\$\{?([^}]*)\}?$/,function(p,n){return process.env[n]})}function E(d){return d?{token:l(d),type:"Bearer"}:void 0}function h(d,p){if(!d||!p)return;const n=Buffer.from(l(p),"base64").toString("utf8");return{token:Buffer.from(d+":"+n,"utf8").toString("base64"),type:"Basic",password:n,username:d}}function F(d){return d?{token:l(d),type:"Basic"}:void 0}return hu}var On=xn();const Rn=J.getDefaultExportFromCjs(On);class In extends Error{constructor(u){super(`Package \`${u}\` could not be found`),this.name="PackageNotFoundError"}}class Nn extends Error{constructor(u,t){super(`Version \`${t}\` for package \`${u}\` could not be found`),this.name="VersionNotFoundError"}}async function Tn(e,u={}){let{version:t="latest"}=u;const{omitDeprecated:i=!0}=u,s=e.split("/")[0],o=u.registryUrl??ln(s),r=new URL(encodeURIComponent(e).replace(/^%40/,"@"),o),D=Rn(o.toString(),{recursive:!0}),a={accept:"application/vnd.npm.install-v1+json; q=1.0, application/json; q=0.8, */*"};u.fullMetadata&&delete a.accept,D&&(a.authorization=`${D.type} ${D.token}`);let c;try{c=await un(r,{headers:a,keepalive:!0}).json()}catch(E){throw E?.response?.status===404?new In(e):E}if(u.allVersions)return c;const l=new Nn(e,t);if(c["dist-tags"][t]){const{time:E}=c;c=c.versions[c["dist-tags"][t]],c.time=E}else if(t){const E=!!c.versions[t];if(i&&!E)for(const[F,d]of Object.entries(c.versions))d.deprecated&&delete c.versions[F];if(!E){const F=Object.keys(c.versions);if(t=J.semver.maxSatisfying(F,t),!t)throw l}const{time:h}=c;if(c=c.versions[t],c.time=h,!c)throw l}return c}async function $n(e,u){const{version:t}=await Tn(e.toLowerCase(),u);return t}const ht=x.env.npm_package_json,je=x.env.npm_config_user_agent,Pn=!!(je&&je.startsWith("npm")),Ln=!!(ht&&ht.endsWith("package.json")),jn=Pn||Ln,qn=!!(je&&je.startsWith("yarn")),kn=jn||qn;var du,dt;function Mn(){if(dt)return du;dt=1;const{hasOwnProperty:e}=Object.prototype,u=(D,a={})=>{typeof a=="string"&&(a={section:a}),a.align=a.align===!0,a.newline=a.newline===!0,a.sort=a.sort===!0,a.whitespace=a.whitespace===!0||a.align===!0,a.platform=a.platform||typeof process<"u"&&process.platform,a.bracketedArray=a.bracketedArray!==!1;const c=a.platform==="win32"?`\r
`:`
`,l=a.whitespace?" = ":"=",E=[],h=a.sort?Object.keys(D).sort():Object.keys(D);let F=0;a.align&&(F=o(h.filter(n=>D[n]===null||Array.isArray(D[n])||typeof D[n]!="object").map(n=>Array.isArray(D[n])?`${n}[]`:n).concat([""]).reduce((n,f)=>o(n).length>=o(f).length?n:f)).length);let d="";const p=a.bracketedArray?"[]":"";for(const n of h){const f=D[n];if(f&&Array.isArray(f))for(const C of f)d+=o(`${n}${p}`).padEnd(F," ")+l+o(C)+c;else f&&typeof f=="object"?E.push(n):d+=o(n).padEnd(F," ")+l+o(f)+c}a.section&&d.length&&(d="["+o(a.section)+"]"+(a.newline?c+c:c)+d);for(const n of E){const f=t(n,".").join("\\."),C=(a.section?a.section+".":"")+f,g=u(D[n],{...a,section:C});d.length&&g.length&&(d+=c),d+=g}return d};function t(D,a){var c=0,l=0,E=0,h=[];do if(E=D.indexOf(a,c),E!==-1){if(c=E+a.length,E>0&&D[E-1]==="\\")continue;h.push(D.slice(l,E)),l=E+a.length}while(E!==-1);return h.push(D.slice(l)),h}const i=(D,a={})=>{a.bracketedArray=a.bracketedArray!==!1;const c=Object.create(null);let l=c,E=null;const h=/^\[([^\]]*)\]\s*$|^([^=]+)(=(.*))?$/i,F=D.split(/[\r\n]+/g),d={};for(const n of F){if(!n||n.match(/^\s*[;#]/)||n.match(/^\s*$/))continue;const f=n.match(h);if(!f)continue;if(f[1]!==void 0){if(E=r(f[1]),E==="__proto__"){l=Object.create(null);continue}l=c[E]=c[E]||Object.create(null);continue}const C=r(f[2]);let g;a.bracketedArray?g=C.length>2&&C.slice(-2)==="[]":(d[C]=(d?.[C]||0)+1,g=d[C]>1);const m=g?C.slice(0,-2):C;if(m==="__proto__")continue;const v=f[3]?r(f[4]):!0,B=v==="true"||v==="false"||v==="null"?JSON.parse(v):v;g&&(e.call(l,m)?Array.isArray(l[m])||(l[m]=[l[m]]):l[m]=[]),Array.isArray(l[m])?l[m].push(B):l[m]=B}const p=[];for(const n of Object.keys(c)){if(!e.call(c,n)||typeof c[n]!="object"||Array.isArray(c[n]))continue;const f=t(n,".");l=c;const C=f.pop(),g=C.replace(/\\\./g,".");for(const m of f)m!=="__proto__"&&((!e.call(l,m)||typeof l[m]!="object")&&(l[m]=Object.create(null)),l=l[m]);l===c&&g===C||(l[g]=c[n],p.push(n))}for(const n of p)delete c[n];return c},s=D=>D.startsWith('"')&&D.endsWith('"')||D.startsWith("'")&&D.endsWith("'"),o=D=>typeof D!="string"||D.match(/[=\r\n]/)||D.match(/^\[/)||D.length>1&&s(D)||D!==D.trim()?JSON.stringify(D):D.split(";").join("\\;").split("#").join("\\#"),r=(D,a)=>{if(D=(D||"").trim(),s(D)){D.charAt(0)==="'"&&(D=D.slice(1,-1));try{D=JSON.parse(D)}catch{}}else{let c=!1,l="";for(let E=0,h=D.length;E<h;E++){const F=D.charAt(E);if(c)"\\;#".indexOf(F)!==-1?l+=F:l+="\\"+F,c=!1;else{if(";#".indexOf(F)!==-1)break;F==="\\"?c=!0:l+=F}}return c&&(l+="\\"),l.trim()}return D};return du={parse:i,decode:i,stringify:u,encode:u,safe:o,unsafe:r},du}var Un=Mn();const Wn=J.getDefaultExportFromCjs(Un),_e=x.platform==="win32",pt=e=>{try{return Wn.parse(N.readFileSync(e,"utf8")).prefix}catch{}},Gn=()=>Object.keys(x.env).reduce((e,u)=>/^npm_config_prefix$/i.test(u)?x.env[u]:e,void 0),Vn=()=>{if(_e&&x.env.APPDATA)return T.join(x.env.APPDATA,"/npm/etc/npmrc");if(x.execPath.includes("/Cellar/node")){const e=x.execPath.slice(0,x.execPath.indexOf("/Cellar/node"));return T.join(e,"/lib/node_modules/npm/npmrc")}if(x.execPath.endsWith("/bin/node")){const e=T.dirname(T.dirname(x.execPath));return T.join(e,"/etc/npmrc")}},Yn=()=>{if(_e){const{APPDATA:e}=x.env;return e?T.join(e,"npm"):T.dirname(x.execPath)}return T.dirname(T.dirname(x.execPath))},Jn=()=>{const e=Gn();if(e)return e;const u=pt(T.join(ce.homedir(),".npmrc"));if(u)return u;if(x.env.PREFIX)return x.env.PREFIX;const t=pt(Vn());return t||Yn()},ve=T.resolve(Jn()),Kt=()=>{if(_e&&x.env.LOCALAPPDATA){const e=T.join(x.env.LOCALAPPDATA,"Yarn");if(N.existsSync(e))return e}return!1},Hn=()=>{if(x.env.PREFIX)return x.env.PREFIX;const e=Kt();if(e)return e;const u=T.join(ce.homedir(),".config/yarn");if(N.existsSync(u))return u;const t=T.join(ce.homedir(),".yarn-config");return N.existsSync(t)?t:ve},ue={};ue.npm={};ue.npm.prefix=ve;ue.npm.packages=T.join(ve,_e?"node_modules":"lib/node_modules");ue.npm.binaries=_e?ve:T.join(ve,"bin");const Xt=T.resolve(Hn());ue.yarn={};ue.yarn.prefix=Xt;ue.yarn.packages=T.join(Xt,Kt()?"Data/global/node_modules":"global/node_modules");ue.yarn.binaries=T.join(ue.yarn.packages,".bin");function Et(e,u){const t=T.relative(u,e);return!!(t&&t!==".."&&!t.startsWith(`..${T.sep}`)&&t!==T.resolve(e))}const Ct=T.dirname(Se.fileURLToPath(typeof document>"u"?require("url").pathToFileURL(__filename).href:ge&&ge.tagName.toUpperCase()==="SCRIPT"&&ge.src||new URL("index-BlZRi20K.js",document.baseURI).href)),Kn=(()=>{try{return Et(Ct,ue.yarn.packages)||Et(Ct,N.realpathSync(ue.npm.packages))}catch{return!1}})();function Xn(e){return e===161||e===164||e===167||e===168||e===170||e===173||e===174||e>=176&&e<=180||e>=182&&e<=186||e>=188&&e<=191||e===198||e===208||e===215||e===216||e>=222&&e<=225||e===230||e>=232&&e<=234||e===236||e===237||e===240||e===242||e===243||e>=247&&e<=250||e===252||e===254||e===257||e===273||e===275||e===283||e===294||e===295||e===299||e>=305&&e<=307||e===312||e>=319&&e<=322||e===324||e>=328&&e<=331||e===333||e===338||e===339||e===358||e===359||e===363||e===462||e===464||e===466||e===468||e===470||e===472||e===474||e===476||e===593||e===609||e===708||e===711||e>=713&&e<=715||e===717||e===720||e>=728&&e<=731||e===733||e===735||e>=768&&e<=879||e>=913&&e<=929||e>=931&&e<=937||e>=945&&e<=961||e>=963&&e<=969||e===1025||e>=1040&&e<=1103||e===1105||e===8208||e>=8211&&e<=8214||e===8216||e===8217||e===8220||e===8221||e>=8224&&e<=8226||e>=8228&&e<=8231||e===8240||e===8242||e===8243||e===8245||e===8251||e===8254||e===8308||e===8319||e>=8321&&e<=8324||e===8364||e===8451||e===8453||e===8457||e===8467||e===8470||e===8481||e===8482||e===8486||e===8491||e===8531||e===8532||e>=8539&&e<=8542||e>=8544&&e<=8555||e>=8560&&e<=8569||e===8585||e>=8592&&e<=8601||e===8632||e===8633||e===8658||e===8660||e===8679||e===8704||e===8706||e===8707||e===8711||e===8712||e===8715||e===8719||e===8721||e===8725||e===8730||e>=8733&&e<=8736||e===8739||e===8741||e>=8743&&e<=8748||e===8750||e>=8756&&e<=8759||e===8764||e===8765||e===8776||e===8780||e===8786||e===8800||e===8801||e>=8804&&e<=8807||e===8810||e===8811||e===8814||e===8815||e===8834||e===8835||e===8838||e===8839||e===8853||e===8857||e===8869||e===8895||e===8978||e>=9312&&e<=9449||e>=9451&&e<=9547||e>=9552&&e<=9587||e>=9600&&e<=9615||e>=9618&&e<=9621||e===9632||e===9633||e>=9635&&e<=9641||e===9650||e===9651||e===9654||e===9655||e===9660||e===9661||e===9664||e===9665||e>=9670&&e<=9672||e===9675||e>=9678&&e<=9681||e>=9698&&e<=9701||e===9711||e===9733||e===9734||e===9737||e===9742||e===9743||e===9756||e===9758||e===9792||e===9794||e===9824||e===9825||e>=9827&&e<=9829||e>=9831&&e<=9834||e===9836||e===9837||e===9839||e===9886||e===9887||e===9919||e>=9926&&e<=9933||e>=9935&&e<=9939||e>=9941&&e<=9953||e===9955||e===9960||e===9961||e>=9963&&e<=9969||e===9972||e>=9974&&e<=9977||e===9979||e===9980||e===9982||e===9983||e===10045||e>=10102&&e<=10111||e>=11094&&e<=11097||e>=12872&&e<=12879||e>=57344&&e<=63743||e>=65024&&e<=65039||e===65533||e>=127232&&e<=127242||e>=127248&&e<=127277||e>=127280&&e<=127337||e>=127344&&e<=127373||e===127375||e===127376||e>=127387&&e<=127404||e>=917760&&e<=917999||e>=983040&&e<=1048573||e>=1048576&&e<=1114109}function zn(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Qn(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9800&&e<=9811||e===9855||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12771||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=19903||e>=19968&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101632&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129672||e>=129680&&e<=129725||e>=129727&&e<=129733||e>=129742&&e<=129755||e>=129760&&e<=129768||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}function Zn(e){if(!Number.isSafeInteger(e))throw new TypeError(`Expected a code point, got \`${typeof e}\`.`)}function eD(e,{ambiguousAsWide:u=!1}={}){return Zn(e),zn(e)||Qn(e)||u&&Xn(e)?2:1}const uD=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,tD=new Intl.Segmenter,rD=new RegExp("^\\p{Default_Ignorable_Code_Point}$","u");function te(e,u={}){if(typeof e!="string"||e.length===0)return 0;const{ambiguousIsNarrow:t=!0,countAnsiEscapeCodes:i=!1}=u;if(i||(e=Mt.default(e)),e.length===0)return 0;let s=0;const o={ambiguousAsWide:!t};for(const{segment:r}of tD.segment(e)){const D=r.codePointAt(0);if(!(D<=31||D>=127&&D<=159)&&!(D>=8203&&D<=8207||D===65279)&&!(D>=768&&D<=879||D>=6832&&D<=6911||D>=7616&&D<=7679||D>=8400&&D<=8447||D>=65056&&D<=65071)&&!(D>=55296&&D<=57343)&&!(D>=65024&&D<=65039)&&!rD.test(r)){if(uD().test(r)){s+=2;continue}s+=eD(D,o)}}return s}function zt(e){let u=0;for(const t of e.split(`
`))u=Math.max(u,te(t));return u}var Ie={exports:{}};const nD={topLeft:"┌",top:"─",topRight:"┐",right:"│",bottomRight:"┘",bottom:"─",bottomLeft:"└",left:"│"},DD={topLeft:"╔",top:"═",topRight:"╗",right:"║",bottomRight:"╝",bottom:"═",bottomLeft:"╚",left:"║"},iD={topLeft:"╭",top:"─",topRight:"╮",right:"│",bottomRight:"╯",bottom:"─",bottomLeft:"╰",left:"│"},oD={topLeft:"┏",top:"━",topRight:"┓",right:"┃",bottomRight:"┛",bottom:"━",bottomLeft:"┗",left:"┃"},sD={topLeft:"╓",top:"─",topRight:"╖",right:"║",bottomRight:"╜",bottom:"─",bottomLeft:"╙",left:"║"},aD={topLeft:"╒",top:"═",topRight:"╕",right:"│",bottomRight:"╛",bottom:"═",bottomLeft:"╘",left:"│"},cD={topLeft:"+",top:"-",topRight:"+",right:"|",bottomRight:"+",bottom:"-",bottomLeft:"+",left:"|"},lD={topLeft:"↘",top:"↓",topRight:"↙",right:"←",bottomRight:"↖",bottom:"↑",bottomLeft:"↗",left:"→"},fD={single:nD,double:DD,round:iD,bold:oD,singleDouble:sD,doubleSingle:aD,classic:cD,arrow:lD};var gt;function FD(){if(gt)return Ie.exports;gt=1;const e=fD;return Ie.exports=e,Ie.exports.default=e,Ie.exports}var hD=FD();const dD=J.getDefaultExportFromCjs(hD),pD=/[\p{Lu}]/u,ED=/[\p{Ll}]/u,mt=/^[\p{Lu}](?![\p{Lu}])/gu,Qt=/([\p{Alpha}\p{N}_]|$)/u,_u=/[_.\- ]+/,CD=new RegExp("^"+_u.source),yt=new RegExp(_u.source+Qt.source,"gu"),Bt=new RegExp("\\d+"+Qt.source,"gu"),gD=(e,u,t,i)=>{let s=!1,o=!1,r=!1,D=!1;for(let a=0;a<e.length;a++){const c=e[a];D=a>2?e[a-3]==="-":!0,s&&pD.test(c)?(e=e.slice(0,a)+"-"+e.slice(a),s=!1,r=o,o=!0,a++):o&&r&&ED.test(c)&&(!D||i)?(e=e.slice(0,a-1)+"-"+e.slice(a-1),r=o,o=!1,s=!0):(s=u(c)===c&&t(c)!==c,r=o,o=t(c)===c&&u(c)!==c)}return e},mD=(e,u)=>(mt.lastIndex=0,e.replaceAll(mt,t=>u(t))),yD=(e,u)=>(yt.lastIndex=0,Bt.lastIndex=0,e.replaceAll(Bt,(t,i,s)=>["_","-"].includes(e.charAt(s+t.length))?t:u(t)).replaceAll(yt,(t,i)=>u(i)));function BD(e,u){if(!(typeof e=="string"||Array.isArray(e)))throw new TypeError("Expected the input to be `string | string[]`");if(u={pascalCase:!1,preserveConsecutiveUppercase:!1,...u},Array.isArray(e)?e=e.map(o=>o.trim()).filter(o=>o.length).join("-"):e=e.trim(),e.length===0)return"";const t=u.locale===!1?o=>o.toLowerCase():o=>o.toLocaleLowerCase(u.locale),i=u.locale===!1?o=>o.toUpperCase():o=>o.toLocaleUpperCase(u.locale);return e.length===1?_u.test(e)?"":u.pascalCase?i(e):t(e):(e!==t(e)&&(e=gD(e,t,i,u.preserveConsecutiveUppercase)),e=e.replace(CD,""),e=u.preserveConsecutiveUppercase?mD(e,t):t(e),u.pascalCase&&(e=i(e.charAt(0))+e.slice(1)),yD(e,i))}var Ne={exports:{}},pu,At;function AD(){return At||(At=1,pu=({onlyFirst:e=!1}={})=>{const u=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(u,e?void 0:"g")}),pu}var Eu,vt;function vD(){if(vt)return Eu;vt=1;const e=AD();return Eu=u=>typeof u=="string"?u.replace(e(),""):u,Eu}var Cu,bt;function bD(){return bt||(bt=1,Cu=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}),Cu}var wt;function wD(){if(wt)return Ne.exports;wt=1;const e=vD(),u=J.requireIsFullwidthCodePoint(),t=bD(),i=s=>{if(typeof s!="string"||s.length===0||(s=e(s),s.length===0))return 0;s=s.replace(t(),"  ");let o=0;for(let r=0;r<s.length;r++){const D=s.codePointAt(r);D<=31||D>=127&&D<=159||D>=768&&D<=879||(D>65535&&r++,o+=u(D)?2:1)}return o};return Ne.exports=i,Ne.exports.default=i,Ne.exports}var gu,St;function SD(){if(St)return gu;St=1;const e=wD();function u(s,o){if(!s)return s;o=o||{};const r=o.align||"center";if(r==="left")return s;const D=o.split||`
`,a=o.pad||" ",c=r!=="right"?t:i;let l=!1;Array.isArray(s)||(l=!0,s=String(s).split(D));let E,h=0;return s=s.map(function(F){return F=String(F),E=e(F),h=Math.max(E,h),{str:F,width:E}}).map(function(F){return new Array(c(h,F.width)+1).join(a)+F.str}),l?s.join(D):s}u.left=function(o){return u(o,{align:"left"})},u.center=function(o){return u(o,{align:"center"})},u.right=function(o){return u(o,{align:"right"})},gu=u;function t(s,o){return Math.floor((s-o)/2)}function i(s,o){return s-o}return gu}var _D=SD();const _t=J.getDefaultExportFromCjs(_D),mu=10,xt=(e=0)=>u=>`\x1B[${u+e}m`,Ot=(e=0)=>u=>`\x1B[${38+e};5;${u}m`,Rt=(e=0)=>(u,t,i)=>`\x1B[${38+e};2;${u};${t};${i}m`,M={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};Object.keys(M.modifier);const xD=Object.keys(M.color),OD=Object.keys(M.bgColor);[...xD,...OD];function RD(){const e=new Map;for(const[u,t]of Object.entries(M)){for(const[i,s]of Object.entries(t))M[i]={open:`\x1B[${s[0]}m`,close:`\x1B[${s[1]}m`},t[i]=M[i],e.set(s[0],s[1]);Object.defineProperty(M,u,{value:t,enumerable:!1})}return Object.defineProperty(M,"codes",{value:e,enumerable:!1}),M.color.close="\x1B[39m",M.bgColor.close="\x1B[49m",M.color.ansi=xt(),M.color.ansi256=Ot(),M.color.ansi16m=Rt(),M.bgColor.ansi=xt(mu),M.bgColor.ansi256=Ot(mu),M.bgColor.ansi16m=Rt(mu),Object.defineProperties(M,{rgbToAnsi256:{value:(u,t,i)=>u===t&&t===i?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(t/255*5)+Math.round(i/255*5),enumerable:!1},hexToRgb:{value:u=>{const t=/[a-f\d]{6}|[a-f\d]{3}/i.exec(u.toString(16));if(!t)return[0,0,0];let[i]=t;i.length===3&&(i=[...i].map(o=>o+o).join(""));const s=Number.parseInt(i,16);return[s>>16&255,s>>8&255,s&255]},enumerable:!1},hexToAnsi256:{value:u=>M.rgbToAnsi256(...M.hexToRgb(u)),enumerable:!1},ansi256ToAnsi:{value:u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let t,i,s;if(u>=232)t=((u-232)*10+8)/255,i=t,s=t;else{u-=16;const D=u%36;t=Math.floor(u/36)/5,i=Math.floor(D/6)/5,s=D%6/5}const o=Math.max(t,i,s)*2;if(o===0)return 30;let r=30+(Math.round(s)<<2|Math.round(i)<<1|Math.round(t));return o===2&&(r+=60),r},enumerable:!1},rgbToAnsi:{value:(u,t,i)=>M.ansi256ToAnsi(M.rgbToAnsi256(u,t,i)),enumerable:!1},hexToAnsi:{value:u=>M.ansi256ToAnsi(M.hexToAnsi256(u)),enumerable:!1}}),M}const ID=RD(),Me=new Set(["\x1B",""]),ND=39,xu="\x07",Zt="[",TD="]",er="m",qe=`${TD}8;;`,It=e=>`${Me.values().next().value}${Zt}${e}${er}`,Nt=e=>`${Me.values().next().value}${qe}${e}${xu}`,$D=e=>e.split(" ").map(u=>te(u)),yu=(e,u,t)=>{const i=[...u];let s=!1,o=!1,r=te(Mt.default(e.at(-1)));for(const[D,a]of i.entries()){const c=te(a);if(r+c<=t?e[e.length-1]+=a:(e.push(a),r=0),Me.has(a)&&(s=!0,o=i.slice(D+1,D+1+qe.length).join("")===qe),s){o?a===xu&&(s=!1,o=!1):a===er&&(s=!1);continue}r+=c,r===t&&D<i.length-1&&(e.push(""),r=0)}!r&&e.at(-1).length>0&&e.length>1&&(e[e.length-2]+=e.pop())},PD=e=>{const u=e.split(" ");let t=u.length;for(;t>0&&!(te(u[t-1])>0);)t--;return t===u.length?e:u.slice(0,t).join(" ")+u.slice(t).join("")},LD=(e,u,t={})=>{if(t.trim!==!1&&e.trim()==="")return"";let i="",s,o;const r=$D(e);let D=[""];for(const[E,h]of e.split(" ").entries()){t.trim!==!1&&(D[D.length-1]=D.at(-1).trimStart());let F=te(D.at(-1));if(E!==0&&(F>=u&&(t.wordWrap===!1||t.trim===!1)&&(D.push(""),F=0),(F>0||t.trim===!1)&&(D[D.length-1]+=" ",F++)),t.hard&&r[E]>u){const d=u-F,p=1+Math.floor((r[E]-d-1)/u);Math.floor((r[E]-1)/u)<p&&D.push(""),yu(D,h,u);continue}if(F+r[E]>u&&F>0&&r[E]>0){if(t.wordWrap===!1&&F<u){yu(D,h,u);continue}D.push("")}if(F+r[E]>u&&t.wordWrap===!1){yu(D,h,u);continue}D[D.length-1]+=h}t.trim!==!1&&(D=D.map(E=>PD(E)));const a=D.join(`
`),c=[...a];let l=0;for(const[E,h]of c.entries()){if(i+=h,Me.has(h)){const{groups:d}=new RegExp(`(?:\\${Zt}(?<code>\\d+)m|\\${qe}(?<uri>.*)${xu})`).exec(a.slice(l))||{groups:{}};if(d.code!==void 0){const p=Number.parseFloat(d.code);s=p===ND?void 0:p}else d.uri!==void 0&&(o=d.uri.length===0?void 0:d.uri)}const F=ID.codes.get(Number(s));c[E+1]===`
`?(o&&(i+=Nt("")),s&&F&&(i+=It(F))):h===`
`&&(s&&F&&(i+=It(s)),o&&(i+=Nt(o))),l+=h.length}return i};function ur(e,u,t){return String(e).normalize().replaceAll(`\r
`,`
`).split(`
`).map(i=>LD(i,u,t)).join(`
`)}const ae=`
`,Q=" ",be="none",tr=()=>{const{env:e,stdout:u,stderr:t}=x;return u?.columns?u.columns:t?.columns?t.columns:e.COLUMNS?Number.parseInt(e.COLUMNS,10):80},Tt=e=>typeof e=="number"?{top:e,right:e*3,bottom:e,left:e*3}:{top:0,right:0,bottom:0,left:0,...e},we=e=>e===be?0:2,jD=e=>{const u=["topLeft","topRight","bottomRight","bottomLeft","left","right","top","bottom"];let t;if(e===be){e={};for(const i of u)e[i]=""}if(typeof e=="string"){if(t=dD[e],!t)throw new TypeError(`Invalid border style: ${e}`)}else{typeof e?.vertical=="string"&&(e.left=e.vertical,e.right=e.vertical),typeof e?.horizontal=="string"&&(e.top=e.horizontal,e.bottom=e.horizontal);for(const i of u)if(e[i]===null||typeof e[i]!="string")throw new TypeError(`Invalid border style: ${i}`);t=e}return t},qD=(e,u,t)=>{let i="";const s=te(e);switch(t){case"left":{i=e+u.slice(s);break}case"right":{i=u.slice(s)+e;break}default:{u=u.slice(s),u.length%2===1?(u=u.slice(Math.floor(u.length/2)),i=u.slice(1)+e+u):(u=u.slice(u.length/2),i=u+e+u);break}}return i},kD=(e,{padding:u,width:t,textAlignment:i,height:s})=>{e=_t(e,{align:i});let o=e.split(ae);const r=zt(e),D=t-u.left-u.right;if(r>D){const l=[];for(const E of o){const h=ur(E,D,{hard:!0}),d=_t(h,{align:i}).split(`
`),p=Math.max(...d.map(n=>te(n)));for(const n of d){let f;switch(i){case"center":{f=Q.repeat((D-p)/2)+n;break}case"right":{f=Q.repeat(D-p)+n;break}default:{f=n;break}}l.push(f)}}o=l}i==="center"&&r<D?o=o.map(l=>Q.repeat((D-r)/2)+l):i==="right"&&r<D&&(o=o.map(l=>Q.repeat(D-r)+l));const a=Q.repeat(u.left),c=Q.repeat(u.right);return o=o.map(l=>{const E=a+l+c;return E+Q.repeat(t-te(E))}),u.top>0&&(o=[...Array.from({length:u.top}).fill(Q.repeat(t)),...o]),u.bottom>0&&(o=[...o,...Array.from({length:u.bottom}).fill(Q.repeat(t))]),s&&o.length>s?o=o.slice(0,s):s&&o.length<s&&(o=[...o,...Array.from({length:s-o.length}).fill(Q.repeat(t))]),o.join(ae)},MD=(e,u,t)=>{const i=l=>{const E=t.borderColor?GD(t.borderColor)(l):l;return t.dimBorder?Z.default.dim(E):E},s=l=>t.backgroundColor?VD(t.backgroundColor)(l):l,o=jD(t.borderStyle),r=tr();let D=Q.repeat(t.margin.left);if(t.float==="center"){const l=Math.max((r-u-we(t.borderStyle))/2,0);D=Q.repeat(l)}else if(t.float==="right"){const l=Math.max(r-u-t.margin.right-we(t.borderStyle),0);D=Q.repeat(l)}let a="";t.margin.top&&(a+=ae.repeat(t.margin.top)),(t.borderStyle!==be||t.title)&&(a+=i(D+o.topLeft+(t.title?qD(t.title,o.top.repeat(u),t.titleAlignment):o.top.repeat(u))+o.topRight)+ae);const c=e.split(ae);return a+=c.map(l=>D+i(o.left)+s(l)+i(o.right)).join(ae),t.borderStyle!==be&&(a+=ae+i(D+o.bottomLeft+o.bottom.repeat(u)+o.bottomRight)),t.margin.bottom&&(a+=ae.repeat(t.margin.bottom)),a},UD=e=>{if(e.fullscreen&&x?.stdout){let u=[x.stdout.columns,x.stdout.rows];typeof e.fullscreen=="function"&&(u=e.fullscreen(...u)),e.width||=u[0],e.height||=u[1]}return e.width&&=Math.max(1,e.width-we(e.borderStyle)),e.height&&=Math.max(1,e.height-we(e.borderStyle)),e},$t=(e,u)=>u===be?e:` ${e} `,WD=(e,u)=>{u=UD(u);const t=u.width!==void 0,i=tr(),s=we(u.borderStyle),o=i-u.margin.left-u.margin.right-s,r=zt(ur(e,i-s,{hard:!0,trim:!1}))+u.padding.left+u.padding.right;if(u.title&&t?(u.title=u.title.slice(0,Math.max(0,u.width-2)),u.title&&=$t(u.title,u.borderStyle)):u.title&&(u.title=u.title.slice(0,Math.max(0,o-2)),u.title&&(u.title=$t(u.title,u.borderStyle),te(u.title)>r&&(u.width=te(u.title)))),u.width||=r,!t){if(u.margin.left&&u.margin.right&&u.width>o){const a=(i-u.width-s)/(u.margin.left+u.margin.right);u.margin.left=Math.max(0,Math.floor(u.margin.left*a)),u.margin.right=Math.max(0,Math.floor(u.margin.right*a))}u.width=Math.min(u.width,i-s-u.margin.left-u.margin.right)}return u.width-(u.padding.left+u.padding.right)<=0&&(u.padding.left=0,u.padding.right=0),u.height&&u.height-(u.padding.top+u.padding.bottom)<=0&&(u.padding.top=0,u.padding.bottom=0),u},Ou=e=>e.match(/^#(?:[0-f]{3}){1,2}$/i),Pt=e=>typeof e=="string"&&(Z.default[e]??Ou(e)),GD=e=>Ou(e)?Z.default.hex(e):Z.default[e],VD=e=>Ou(e)?Z.default.bgHex(e):Z.default[BD(["bg",e])];function Lt(e,u){if(u={padding:0,borderStyle:"single",dimBorder:!1,textAlignment:"left",float:"left",titleAlignment:"left",...u},u.align&&(u.textAlignment=u.align),u.borderColor&&!Pt(u.borderColor))throw new Error(`${u.borderColor} is not a valid borderColor`);if(u.backgroundColor&&!Pt(u.backgroundColor))throw new Error(`${u.backgroundColor} is not a valid backgroundColor`);return u.padding=Tt(u.padding),u.margin=Tt(u.margin),u=WD(e,u),e=kD(e,u),MD(e,u.width,u)}const YD=x.env.CI!=="0"&&x.env.CI!=="false"&&("CI"in x.env||"CONTINUOUS_INTEGRATION"in x.env||Object.keys(x.env).some(e=>e.startsWith("CI_"))),jt=e=>e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");function JD(e,...u){if(typeof e=="string")return jt(e);let t=e[0];for(const[i,s]of u.entries())t=t+jt(String(s))+e[i+1];return t}class HD extends Error{constructor(u){super(`Missing a value for ${u?`the placeholder: ${u}`:"a placeholder"}`,u),this.name="MissingValueError",this.key=u}}function KD(e,u,{ignoreMissing:t=!1,transform:i=({value:s})=>s}={}){if(typeof e!="string")throw new TypeError(`Expected a \`string\` in the first argument, got \`${typeof e}\``);if(typeof u!="object")throw new TypeError(`Expected an \`object\` or \`Array\` in the second argument, got \`${typeof u}\``);const s=(a,c)=>{let l=u;for(const h of c.split("."))l=l?l[h]:void 0;const E=i({value:l,key:c});if(E===void 0){if(t)return a;throw new HD(c)}return String(E)},o=a=>(...c)=>JD(a(...c)),r=/{{(\d+|[a-z$_][\w\-$]*?(?:\.[\w\-$]*?)*?)}}/gi;r.test(e)&&(e=e.replace(r,o(s)));const D=/{(\d+|[a-z$_][\w\-$]*?(?:\.[\w\-$]*?)*?)}/gi;return e.replace(D,s)}const XD=T.dirname(Se.fileURLToPath(typeof document>"u"?require("url").pathToFileURL(__filename).href:ge&&ge.tagName.toUpperCase()==="SCRIPT"&&ge.src||new URL("index-BlZRi20K.js",document.baseURI).href)),zD=1e3*60*60*24;class QD{config;update;_packageName;_shouldNotifyInNpmScript;#u;#e;#r;#t;constructor(u={}){if(this.#u=u,u.pkg??={},u.distTag??="latest",u.pkg={name:u.pkg.name??u.packageName,version:u.pkg.version??u.packageVersion},!u.pkg.name||!u.pkg.version)throw new Error("pkg.name and pkg.version required");if(this._packageName=u.pkg.name,this.#e=u.pkg.version,this.#r=typeof u.updateCheckInterval=="number"?u.updateCheckInterval:zD,this.#t="NO_UPDATE_NOTIFIER"in x.env||x.env.NODE_ENV==="test"||x.argv.includes("--no-update-notifier")||YD,this._shouldNotifyInNpmScript=u.shouldNotifyInNpmScript,!this.#t)try{this.config=new Pr(`update-notifier-${this._packageName}`,{optOut:!1,lastUpdateCheck:Date.now()})}catch{const t=Z.default.yellow(W.format(" %s update check failed ",u.pkg.name))+W.format(`
 Try running with %s or get access `,Z.default.cyan("sudo"))+`
 to the local update config store via 
`+Z.default.cyan(W.format(" sudo chown -R $USER:$(id -gn $USER) %s ",Pe));x.on("exit",()=>{console.error(Lt(t,{textAlignment:"center"}))})}}check(){!this.config||this.config.get("optOut")||this.#t||(this.update=this.config.get("update"),this.update&&(this.update.current=this.#e,this.config.delete("update")),!(Date.now()-this.config.get("lastUpdateCheck")<this.#r)&&nr.spawn(x.execPath,[T.join(XD,"check.js"),JSON.stringify(this.#u)],{detached:!0,stdio:"ignore"}).unref())}async fetchInfo(){const{distTag:u}=this.#u,t=await $n(this._packageName,{version:u});return{latest:t,current:this.#e,type:jr(this.#e,t)??u,name:this._packageName}}notify(u){const t=!this._shouldNotifyInNpmScript&&kn;if(!x.stdout.isTTY||t||!this.update||!kr(this.update.latest,this.update.current))return this;u={isGlobal:Kn,...u};const i=u.isGlobal?`npm i -g ${this._packageName}`:`npm i ${this._packageName}`,s="Update available "+Z.default.dim("{currentVersion}")+Z.default.reset(" → ")+Z.default.green("{latestVersion}")+` 
Run `+Z.default.cyan("{updateCommand}")+" to update",o=u.message||s;u.boxenOptions??={padding:1,margin:1,textAlignment:"center",borderColor:"yellow",borderStyle:"round"};const r=Lt(KD(o,{packageName:this._packageName,currentVersion:this.update.current,latestVersion:this.update.latest,updateCommand:i}),u.boxenOptions);return u.defer===!1?console.error(r):x.on("exit",()=>{console.error(r)}),this}}function ZD(e){const u=new QD(e);return u.check(),u}exports.default=ZD;
//# sourceMappingURL=index-BlZRi20K.js.map
